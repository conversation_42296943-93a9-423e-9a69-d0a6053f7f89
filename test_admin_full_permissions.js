// Test admin với full permissions từ SuperAdmin role
const puppeteer = require('puppeteer');

async function testAdminFullPermissions() {
  console.log('🚀 Test admin với full permissions...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('AppsPage') || text.includes('Permission') || text.includes('Module')) {
      console.log(`🔍 Frontend: ${text}`);
    }
  });
  
  try {
    // Đăng nhập với admin
    console.log('🔐 Đăng nhập với admin (SuperAdmin + Admin roles)...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    
    // Clear any existing data
    await page.evaluate(() => {
      localStorage.clear();
    });
    
    await page.type('input[placeholder="Tên đăng nhập"]', 'admin');
    await page.type('input[placeholder="Mật khẩu"]', 'admin123');
    await page.click('button[type="submit"]');
    
    // Đợi login process hoàn thành
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Navigate đến apps page
    console.log('🔄 Navigate đến apps page...');
    await page.goto('http://localhost:5173/apps', { waitUntil: 'networkidle2' });
    
    // Đợi permissions và modules load
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    // Kiểm tra user permissions
    const permissionsInfo = await page.evaluate(async () => {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (!user.id) return { error: 'No user found' };
      
      try {
        const token = localStorage.getItem('token');
        const headers = {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        };
        
        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
        }
        
        const response = await fetch(`http://localhost:8000/api/user-management/users/${user.id}/permissions`, {
          headers
        });
        
        if (response.ok) {
          const permissions = await response.json();
          const moduleGroups = {};
          permissions.forEach(perm => {
            const module = perm.module_name || 'unknown';
            if (!moduleGroups[module]) {
              moduleGroups[module] = [];
            }
            moduleGroups[module].push(perm.code);
          });
          
          return {
            userId: user.id,
            username: user.username,
            totalPermissions: permissions.length,
            moduleGroups: moduleGroups
          };
        } else {
          return { error: `HTTP ${response.status}: ${response.statusText}` };
        }
      } catch (error) {
        return { error: error.message };
      }
    });
    
    console.log('📊 Admin permissions info:');
    console.log(`   User: ${permissionsInfo.username} (${permissionsInfo.userId})`);
    console.log(`   Total permissions: ${permissionsInfo.totalPermissions}`);
    console.log('   Permissions by module:');
    
    if (permissionsInfo.moduleGroups) {
      Object.keys(permissionsInfo.moduleGroups).forEach(module => {
        console.log(`     ${module}: ${permissionsInfo.moduleGroups[module].length} permissions`);
      });
    }
    
    // Đếm module cards visible
    const moduleInfo = await page.evaluate(() => {
      const moduleCards = document.querySelectorAll('.ant-card');
      const moduleTexts = Array.from(moduleCards).map(card => {
        const title = card.querySelector('.ant-typography');
        return title ? title.textContent.trim() : 'Unknown';
      });
      return {
        count: moduleCards.length,
        modules: moduleTexts
      };
    });
    
    console.log('📊 Module cards visible to admin:');
    console.log(`   Count: ${moduleInfo.count}`);
    moduleInfo.modules.forEach((module, index) => {
      console.log(`   ${index + 1}. ${module}`);
    });
    
    console.log('');
    console.log('📝 EXPECTED RESULT:');
    console.log('   - Admin có 35 permissions từ SuperAdmin role');
    console.log('   - Admin should see ALL 6 modules: user_management, site_management, activity_logs, base, multi_languages, sample_development');
    console.log('   - Nếu vẫn chỉ thấy 1-2 modules thì có vấn đề với permission filtering logic');
    console.log('');
    
    if (moduleInfo.count >= 5) {
      console.log('✅ MULTI-ROLE PERMISSION SYSTEM WORKING CORRECTLY!');
      console.log('   Admin có thể thấy nhiều modules như mong đợi');
    } else {
      console.log('❌ PERMISSION FILTERING ISSUE');
      console.log('   Admin should see more modules với SuperAdmin permissions');
    }
    
    console.log('⏳ Browser sẽ mở trong 60 giây để kiểm tra...');
    await new Promise(resolve => setTimeout(resolve, 60000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành test');
  }
}

testAdminFullPermissions().catch(console.error);
