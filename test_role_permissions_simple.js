// Script đơn gi<PERSON><PERSON> để test chức năng Role permissions
const puppeteer = require('puppeteer');

async function testRolePermissions() {
  console.log('🚀 Bắt đầu test Role permissions...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs từ browser
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('RolePermissionsPage') || text.includes('Error') || text.includes('DEBUG')) {
      console.log(`🔍 Frontend: ${text}`);
    }
  });
  
  try {
    // Đăng nhập
    console.log('🔐 Đăng nhập...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    await page.type('input[placeholder="Tên đăng nhập"]', 'admin');
    await page.type('input[placeholder="Mật khẩu"]', 'admin123');
    await page.click('button[type="submit"]');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Navigate đến user-management
    console.log('🔄 Navigate đến user-management...');
    await page.goto('http://localhost:5173/user-management', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Click vào menu Role permissions
    console.log('📋 Click vào menu Role permissions...');
    await page.evaluate(() => {
      const menuItems = Array.from(document.querySelectorAll('.ant-menu-item'));
      const rolePermissionsItem = menuItems.find(item => 
        item.textContent && item.textContent.includes('Role permissions')
      );
      
      if (rolePermissionsItem) {
        console.log('Found Role permissions menu item, clicking...');
        rolePermissionsItem.click();
      }
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Kiểm tra xem page đã load chưa
    const hasRolePermissionsPage = await page.evaluate(() => {
      const title = document.querySelector('.ant-card-head-title');
      return title && title.textContent.includes('Phân quyền cho Role');
    });
    
    if (hasRolePermissionsPage) {
      console.log('✅ Role permissions page loaded!');
      
      // Chọn role đầu tiên
      console.log('🎯 Chọn role...');
      await page.click('.search-role-dropdown .dropdown-input');
      await new Promise(resolve => setTimeout(resolve, 1000));

      await page.evaluate(() => {
        const options = document.querySelectorAll('.dropdown-option');
        if (options.length > 0) {
          console.log('Found', options.length, 'role options, clicking first one');
          options[0].click();
        } else {
          console.log('No role options found');
        }
      });
      
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Click nút Lưu
      console.log('💾 Click nút Lưu...');
      await page.evaluate(() => {
        const saveButton = Array.from(document.querySelectorAll('button')).find(btn => 
          btn.textContent && btn.textContent.trim() === 'Lưu'
        );
        if (saveButton && !saveButton.disabled) {
          console.log('RolePermissionsPage: Clicking save button');
          saveButton.click();
        }
      });
      
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Kiểm tra thông báo
      const notifications = await page.evaluate(() => {
        const messages = Array.from(document.querySelectorAll('.ant-message-notice-content'));
        return messages.map(msg => msg.textContent);
      });
      
      console.log('📢 Notifications:', notifications);
    } else {
      console.log('❌ Role permissions page not found');
    }
    
    console.log('⏳ Giữ browser mở 20 giây...');
    await new Promise(resolve => setTimeout(resolve, 20000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành');
  }
}

testRolePermissions().catch(console.error);
