// Test thông báo message notifications
const puppeteer = require('puppeteer');

async function testMessageNotifications() {
  console.log('🚀 Test message notifications...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('RolePermissionsPage') || text.includes('Save') || text.includes('message')) {
      console.log(`🔍 Frontend: ${text}`);
    }
  });
  
  try {
    // Đăng nhập
    console.log('🔐 Đăng nhập...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    await page.type('input[placeholder="Tên đăng nhập"]', 'admin');
    await page.type('input[placeholder="Mật khẩu"]', 'admin123');
    await page.click('button[type="submit"]');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Navigate đến user-management
    console.log('🔄 Navigate đến user-management...');
    await page.goto('http://localhost:5173/user-management', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Thử click vào menu Role permissions bằng JavaScript
    console.log('📋 Tìm và click menu Role permissions...');
    const menuClicked = await page.evaluate(() => {
      // Tìm tất cả menu items
      const menuItems = document.querySelectorAll('.ant-menu-item');
      console.log('Found menu items:', menuItems.length);
      
      for (let i = 0; i < menuItems.length; i++) {
        const item = menuItems[i];
        const text = item.textContent || '';
        console.log(`Menu item ${i}:`, text);
        
        if (text.includes('Role permissions') || text.includes('role-permissions')) {
          console.log('Found Role permissions menu, clicking...');
          item.click();
          return true;
        }
      }
      
      // Nếu không tìm thấy, thử click item cuối cùng
      if (menuItems.length >= 5) {
        console.log('Clicking last menu item (should be Role permissions)...');
        menuItems[4].click();
        return true;
      }
      
      return false;
    });
    
    if (menuClicked) {
      console.log('✅ Đã click menu Role permissions');
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Kiểm tra xem RolePermissionsPage có load không
      const pageLoaded = await page.evaluate(() => {
        const dropdown = document.querySelector('.search-role-dropdown');
        const tree = document.querySelector('.ant-tree');
        const saveButton = document.querySelector('button[type="button"]');
        
        return {
          hasDropdown: !!dropdown,
          hasTree: !!tree,
          hasSaveButton: !!saveButton,
          saveButtonText: saveButton?.textContent
        };
      });
      
      console.log('📊 Page status:', pageLoaded);
      
      if (pageLoaded.hasDropdown && pageLoaded.hasTree) {
        console.log('✅ RolePermissionsPage loaded successfully!');
        console.log('');
        console.log('📝 HƯỚNG DẪN TEST THÔNG BÁO:');
        console.log('   1. Chọn role "admin" từ dropdown');
        console.log('   2. Check/uncheck một vài permissions');
        console.log('   3. Click nút "Lưu"');
        console.log('   4. Quan sát thông báo success ở góc trên bên phải');
        console.log('');
        console.log('⏳ Browser sẽ mở trong 5 phút để test...');
        
        await new Promise(resolve => setTimeout(resolve, 300000));
      } else {
        console.log('❌ RolePermissionsPage chưa load được');
      }
    } else {
      console.log('❌ Không tìm thấy menu Role permissions');
    }
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành test');
  }
}

testMessageNotifications().catch(console.error);
