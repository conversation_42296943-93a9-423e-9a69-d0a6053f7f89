// Quick test để kiểm tra auto-check
const puppeteer = require('puppeteer');

async function quickTestAutoCheck() {
  console.log('🚀 Quick test auto-check...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('RolePermissionsPage')) {
      console.log(`🔍 Frontend: ${text}`);
    }
  });
  
  try {
    // Đăng nhập
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    await page.type('input[placeholder="Tên đăng nhập"]', 'admin');
    await page.type('input[placeholder="Mật khẩu"]', 'admin123');
    await page.click('button[type="submit"]');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Navigate đến user-management
    await page.goto('http://localhost:5173/user-management', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Click vào menu Role permissions
    await page.evaluate(() => {
      const menuItems = Array.from(document.querySelectorAll('.ant-menu-item'));
      const rolePermissionsItem = menuItems.find(item => 
        item.textContent && item.textContent.includes('Role permissions')
      );
      
      if (rolePermissionsItem) {
        rolePermissionsItem.click();
      }
    });
    
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('✅ Page loaded, checking tree data...');
    
    // Kiểm tra tree data
    const treeInfo = await page.evaluate(() => {
      const treeNodes = document.querySelectorAll('.ant-tree-treenode');
      const nodeInfo = Array.from(treeNodes).slice(0, 5).map(node => ({
        key: node.getAttribute('data-key'),
        title: node.querySelector('.ant-tree-title')?.textContent?.trim(),
        hasCheckbox: !!node.querySelector('.ant-tree-checkbox')
      }));
      
      return {
        totalNodes: treeNodes.length,
        sampleNodes: nodeInfo
      };
    });
    
    console.log('📊 Tree info:', JSON.stringify(treeInfo, null, 2));
    
    console.log('⏳ Giữ browser mở 60 giây để test manual...');
    console.log('📝 Hãy chọn role "admin" và xem có permissions nào được auto-check không');
    
    await new Promise(resolve => setTimeout(resolve, 60000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành');
  }
}

quickTestAutoCheck().catch(console.error);
