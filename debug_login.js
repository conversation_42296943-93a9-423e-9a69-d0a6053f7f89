// Debug login process
const puppeteer = require('puppeteer');

async function debugLogin() {
  console.log('🚀 Debug login process...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs
  page.on('console', msg => {
    console.log(`🔍 Frontend: ${msg.text()}`);
  });
  
  try {
    // Đăng nhập với phucnguyen
    console.log('🔐 Đăng nhập với phucnguyen...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    
    // Kiểm tra localStorage trước khi đăng nhập
    const beforeLogin = await page.evaluate(() => {
      return {
        user: localStorage.getItem('user'),
        token: localStorage.getItem('token')
      };
    });
    console.log('📊 Before login:', beforeLogin);
    
    await page.type('input[placeholder="Tên đăng nhập"]', 'phucnguyen');
    await page.type('input[placeholder="Mật khẩu"]', 'phuc123');
    await page.click('button[type="submit"]');
    
    // Đợi redirect
    await page.waitForNavigation({ waitUntil: 'networkidle2' });
    
    // Kiểm tra localStorage sau khi đăng nhập
    const afterLogin = await page.evaluate(() => {
      const user = localStorage.getItem('user');
      const token = localStorage.getItem('token');
      
      return {
        user: user ? JSON.parse(user) : null,
        token: token,
        currentUrl: window.location.href
      };
    });
    
    console.log('📊 After login:', afterLogin);
    
    if (afterLogin.user && afterLogin.user.id) {
      console.log('✅ Login successful!');
      console.log(`   User ID: ${afterLogin.user.id}`);
      console.log(`   Username: ${afterLogin.user.username}`);
      
      // Test permissions API
      const permissionsResponse = await page.evaluate(async (userId) => {
        try {
          const token = localStorage.getItem('token');
          const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          };
          
          if (token) {
            headers['Authorization'] = `Bearer ${token}`;
          }
          
          const response = await fetch(`http://localhost:8000/api/user-management/users/${userId}/permissions`, {
            headers
          });
          
          if (response.ok) {
            return await response.json();
          } else {
            return { error: `HTTP ${response.status}: ${response.statusText}` };
          }
        } catch (error) {
          return { error: error.message };
        }
      }, afterLogin.user.id);
      
      console.log('🔍 Permissions API response:', permissionsResponse);
      
    } else {
      console.log('❌ Login failed - no user data in localStorage');
    }
    
    console.log('⏳ Browser sẽ mở trong 30 giây để kiểm tra...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành debug');
  }
}

debugLogin().catch(console.error);
