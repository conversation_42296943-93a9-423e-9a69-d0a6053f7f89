// Test script để đăng nhập và test Role permissions
const puppeteer = require('puppeteer');

async function testLoginAndRolePermissions() {
  console.log('🚀 Bắt đầu test đăng nhập và Role permissions...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs từ browser
  page.on('console', msg => {
    const type = msg.type();
    const text = msg.text();
    if (text.includes('RolePermissionsPage') || text.includes('Error') || text.includes('DEBUG') || text.includes('handleSave')) {
      console.log(`🔍 [BROWSER ${type.toUpperCase()}] ${text}`);
    }
  });
  
  // Lắng nghe network requests
  page.on('response', response => {
    const url = response.url();
    const status = response.status();
    if (url.includes('/api/') || status >= 400) {
      console.log(`🌐 [${status}] ${url}`);
    }
  });
  
  try {
    console.log('📱 Mở trang login...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Đăng nhập
    console.log('🔐 Đăng nhập với admin/admin123...');
    await page.type('input[placeholder="Tên đăng nhập"]', 'admin');
    await page.type('input[placeholder="Mật khẩu"]', 'admin123');
    
    // Click button đăng nhập
    await page.click('button[type="submit"]');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('✅ Đã đăng nhập, chờ redirect...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Navigate đến user-management
    console.log('🔄 Navigate đến /user-management...');
    await page.goto('http://localhost:5173/user-management', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Chụp screenshot trang user management
    await page.screenshot({ path: 'user_management_logged_in.png', fullPage: true });
    console.log('📸 Đã chụp screenshot: user_management_logged_in.png');
    
    // Tìm tất cả các tab
    console.log('🔍 Tìm kiếm các tabs...');
    const tabs = await page.$$eval('[role="tab"], .ant-tabs-tab, [class*="tab"]', tabs => 
      tabs.map(tab => ({
        text: tab.textContent?.trim(),
        className: tab.className,
        visible: tab.offsetParent !== null
      }))
    );
    console.log('📋 Các tabs tìm thấy:', tabs);
    
    // Tìm và click tab Role permissions
    console.log('🔍 Tìm tab Role permissions...');
    const rolePermTab = await page.evaluate(() => {
      const tabs = Array.from(document.querySelectorAll('[role="tab"], .ant-tabs-tab, [class*="tab"]'));
      return tabs.find(tab => tab.textContent && tab.textContent.includes('Role permissions'));
    });
    
    if (rolePermTab) {
      console.log('✅ Tìm thấy tab Role permissions, click vào...');
      await page.evaluate(() => {
        const tabs = Array.from(document.querySelectorAll('[role="tab"], .ant-tabs-tab, [class*="tab"]'));
        const tab = tabs.find(tab => tab.textContent && tab.textContent.includes('Role permissions'));
        if (tab) tab.click();
      });
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Chụp screenshot sau khi click tab
      await page.screenshot({ path: 'role_permissions_tab.png', fullPage: true });
      console.log('📸 Đã chụp screenshot: role_permissions_tab.png');
      
      // Tìm dropdown role
      console.log('🔍 Tìm dropdown role...');
      const roleDropdown = await page.$('.ant-select');
      if (roleDropdown) {
        console.log('✅ Tìm thấy dropdown role, click để mở...');
        await roleDropdown.click();
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Chọn role đầu tiên
        const firstOption = await page.$('.ant-select-item');
        if (firstOption) {
          console.log('✅ Chọn role đầu tiên...');
          await firstOption.click();
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
      
      // Tìm button Lưu
      console.log('🔍 Tìm button Lưu...');
      const saveButtons = await page.$$eval('button', buttons => {
        return buttons
          .filter(btn => btn.textContent && btn.textContent.includes('Lưu'))
          .map(btn => ({
            text: btn.textContent.trim(),
            className: btn.className,
            disabled: btn.disabled,
            visible: btn.offsetParent !== null
          }));
      });
      
      console.log('💾 Buttons "Lưu" tìm thấy:', saveButtons);
      
      if (saveButtons.length > 0 && !saveButtons[0].disabled) {
        console.log('🖱️ Click button Lưu...');
        await page.evaluate(() => {
          const buttons = Array.from(document.querySelectorAll('button'));
          const saveBtn = buttons.find(btn => btn.textContent && btn.textContent.includes('Lưu'));
          if (saveBtn && !saveBtn.disabled) {
            console.log('RolePermissionsPage: About to click Save button');
            saveBtn.click();
          }
        });
        
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Chụp screenshot sau khi click Save
        await page.screenshot({ path: 'after_save_click.png', fullPage: true });
        console.log('📸 Đã chụp screenshot: after_save_click.png');
      } else {
        console.log('❌ Button Lưu bị disabled hoặc không tìm thấy');
      }
      
      // Test button "Test API"
      console.log('🔍 Tìm button Test API...');
      const testApiBtn = await page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        return buttons.find(btn => btn.textContent && btn.textContent.includes('Test API'));
      });
      
      if (testApiBtn) {
        console.log('🖱️ Click button Test API...');
        await page.evaluate(() => {
          const buttons = Array.from(document.querySelectorAll('button'));
          const testBtn = buttons.find(btn => btn.textContent && btn.textContent.includes('Test API'));
          if (testBtn) testBtn.click();
        });
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
    } else {
      console.log('❌ Không tìm thấy tab Role permissions');
      
      // Liệt kê tất cả text có trong trang
      const allText = await page.evaluate(() => {
        return Array.from(document.querySelectorAll('*'))
          .map(el => el.textContent?.trim())
          .filter(text => text && text.length > 0 && text.length < 100)
          .slice(0, 50);
      });
      console.log('📝 Tất cả text trong trang:', allText);
    }
    
    console.log('⏳ Giữ browser mở 30 giây để quan sát...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
    await page.screenshot({ path: 'login_test_error.png', fullPage: true });
    console.log('📸 Đã chụp screenshot lỗi: login_test_error.png');
  } finally {
    await browser.close();
    console.log('🏁 Test hoàn thành');
  }
}

testLoginAndRolePermissions().catch(console.error);
