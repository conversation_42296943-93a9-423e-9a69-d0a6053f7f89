// Debug test để xem chi tiết logs
const puppeteer = require('puppeteer');

async function debugAutoCheck() {
  console.log('🚀 Debug auto-check...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe tất cả console logs
  page.on('console', msg => {
    console.log(`🔍 Frontend: ${msg.text()}`);
  });
  
  try {
    // Đăng nhập
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    await page.type('input[placeholder="Tên đăng nhập"]', 'admin');
    await page.type('input[placeholder="Mật khẩu"]', 'admin123');
    await page.click('button[type="submit"]');
    await new <PERSON>(resolve => setTimeout(resolve, 3000));
    
    // Navigate đến user-management
    await page.goto('http://localhost:5173/user-management', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Click vào menu Role permissions
    await page.evaluate(() => {
      const menuItems = Array.from(document.querySelectorAll('.ant-menu-item'));
      const rolePermissionsItem = menuItems.find(item => 
        item.textContent && item.textContent.includes('Role permissions')
      );
      
      if (rolePermissionsItem) {
        rolePermissionsItem.click();
      }
    });
    
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('✅ Page loaded, waiting for user to select role...');
    console.log('📝 Hãy chọn role "admin" để xem debug logs...');
    
    await new Promise(resolve => setTimeout(resolve, 60000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành');
  }
}

debugAutoCheck().catch(console.error);
