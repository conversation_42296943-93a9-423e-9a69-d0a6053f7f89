# Test Add Role Fix

## Vấn đề đã được sửa:
**Lỗi**: `Failed to load resource: the server responded with a status of 404 (Not Found)` khi thêm role.

## Nguyên nhân:
Endpoint `/api/user-management/roles/check-name/{name}` không tồn tại trong backend.

## Giải pháp đã thực hiện:

### 1. Thêm endpoint check-role-name trong backend
- **File**: `backend/src/modules/user_management/router.py` dòng 198-207
- **Endpoint**: `GET /api/user-management/roles/check-name/{name}`
- **Chức năng**: Kiểm tra role name có tồn tại không
- **Response**: 
  ```json
  {
    "name": "role_name",
    "exists": true/false,
    "available": false/true
  }
  ```

### 2. Cải thiện function checkRoleName trong frontend
- **File**: `frontend/src/pages/apps/UserManagementPage.tsx` dòng 781-803
- **Chức năng**: 
  - <PERSON><PERSON><PERSON> nhật state `roleNameAvailable` 
  - <PERSON><PERSON> lý lỗi tốt hơn
  - Clear state khi name trống

### 3. Thêm real-time validation cho role name field
- **File**: `frontend/src/pages/apps/UserManagementPage.tsx` dòng 1850-1870
- **Chức năng**:
  - Real-time validation khi gõ
  - Hiển thị trạng thái checking/success/error
  - Validation rules: required, min 2 chars, max 100 chars

### 4. Thêm translation keys
- **File**: `frontend/src/i18n.ts`
- **Keys thêm**:
  - `user.roles.form.nameAvailable`
  - `user.roles.form.nameMinLength`
  - `user.roles.form.nameMaxLength`
  - `user.roles.form.checking`

## Cách test:

### Test Case 1: Endpoint hoạt động
```bash
# Test với role name đã tồn tại
curl -X GET "http://localhost:8000/api/user-management/roles/check-name/admin" -H "Content-Type: application/json"
# Kết quả: {"name":"admin","exists":true,"available":false}

# Test với role name mới
curl -X GET "http://localhost:8000/api/user-management/roles/check-name/newrole" -H "Content-Type: application/json"
# Kết quả: {"name":"newrole","exists":false,"available":true}
```

### Test Case 2: Add Role thành công
1. Mở UserManagementPage
2. Chuyển sang tab "Vai trò"
3. Nhấn "Thêm vai trò"
4. Nhập tên vai trò mới (ví dụ: "editor")
5. **Kết quả**: 
   - Hiển thị "Đang kiểm tra..." khi gõ
   - Hiển thị "Tên vai trò có thể sử dụng" nếu available
   - Có thể lưu thành công

### Test Case 3: Add Role với tên đã tồn tại
1. Nhập tên vai trò đã có (ví dụ: "admin")
2. **Kết quả**: 
   - Hiển thị "Tên vai trò đã tồn tại"
   - Không thể lưu

### Test Case 4: Validation rules
1. Để trống tên vai trò → Hiển thị "Vui lòng nhập tên vai trò"
2. Nhập 1 ký tự → Hiển thị "Tên vai trò phải có ít nhất 2 ký tự"
3. Nhập quá 100 ký tự → Hiển thị "Tên vai trò không được vượt quá 100 ký tự"

### Test Case 5: Real-time validation
1. Nhập tên hợp lệ → Hiển thị success
2. Xóa tên đi → Hiển thị lỗi required ngay lập tức
3. Thay đổi tên → Validation chạy real-time

## Lưu ý:
- Backend phải đang chạy trên port 8000
- Database phải có dữ liệu roles để test
- Không còn lỗi 404 khi thêm role
- Validation chạy real-time như user form 