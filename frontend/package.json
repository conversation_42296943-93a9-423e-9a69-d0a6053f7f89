{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@types/react-router-dom": "^5.3.3", "antd": "^5.26.1", "axios": "^1.10.0", "i18next": "^25.2.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.3", "react-router-dom": "^7.6.2"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/antd": "^1.0.4", "@types/axios": "^0.14.4", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}