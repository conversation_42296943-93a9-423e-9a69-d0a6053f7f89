import { ConfigProvider, App as AntdApp } from 'antd';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import LoginPage from './pages/LoginPage';
import AppsPageWrapper from './pages/apps/AppsPage';
import BasePage from './pages/apps/BasePage';
import UserManagementPage from './pages/apps/UserManagementPage';
import SiteManagementPage from './pages/apps/SiteManagementPage';
import ActivityLogsPage from './pages/apps/ActivityLogsPage';
import { AuthProvider } from './contexts/AuthContext';
import { PermissionProvider } from './contexts/PermissionContext';
import ProtectedRoute from './components/ProtectedRoute';
import './App.css'
import { LanguageProvider } from './contexts/LanguageContext';

// Theme tùy chỉnh hiện đại
const theme = {
  token: {
    colorPrimary: '#667eea',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#f5222d',
    colorInfo: '#1890ff',
    borderRadius: 8,
    wireframe: false,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif',
  },
  components: {
    Card: {
      borderRadiusLG: 16,
      boxShadowTertiary: '0 4px 12px rgba(0,0,0,0.08)',
    },
    Button: {
      borderRadius: 8,
      controlHeight: 40,
    },
    Input: {
      borderRadius: 8,
      controlHeight: 40,
    },
    Select: {
      borderRadius: 8,
      controlHeight: 40,
    },
    Layout: {
      headerBg: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      bodyBg: '#f5f5f5',
    },
  },
};

function App() {
  return (
    <ConfigProvider theme={theme}>
      <AntdApp>
        <LanguageProvider>
          <AuthProvider>
            <PermissionProvider>
              <Router>
              <Routes>
                <Route path="/" element={<LoginPage />} />
                <Route path="/login" element={<LoginPage />} />
                <Route 
                  path="/apps" 
                  element={
                    <ProtectedRoute>
                      <AppsPageWrapper />
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/base" 
                  element={
                    <ProtectedRoute>
                      <BasePage />
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/user-management" 
                  element={
                    <ProtectedRoute>
                      <UserManagementPage />
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/site-management" 
                  element={
                    <ProtectedRoute>
                      <SiteManagementPage />
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/activity-logs" 
                  element={
                    <ProtectedRoute>
                      <ActivityLogsPage />
                    </ProtectedRoute>
                  } 
                />
              </Routes>
            </Router>
            </PermissionProvider>
          </AuthProvider>
        </LanguageProvider>
      </AntdApp>
    </ConfigProvider>
  )
}

export default App
