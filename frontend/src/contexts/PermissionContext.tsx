import React, { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';
import { useAuth } from './AuthContext';
import axios from 'axios';

interface Permission {
  id: number;
  code: string;
  name: string;
  type: 'module' | 'menu' | 'button';
  module_name?: string;
  parent_id?: number;
}

interface PermissionContextType {
  permissions: Permission[];
  loading: boolean;
  hasPermission: (code: string) => boolean;
  hasModulePermission: (moduleName: string) => boolean;
  hasMenuPermission: (menuCode: string) => boolean;
  hasButtonPermission: (buttonCode: string) => boolean;
  refreshPermissions: () => Promise<void>;
}

const PermissionContext = createContext<PermissionContextType | undefined>(undefined);

export const usePermissions = () => {
  const context = useContext(PermissionContext);
  if (context === undefined) {
    throw new Error('usePermissions must be used within a PermissionProvider');
  }
  return context;
};

interface PermissionProviderProps {
  children: ReactNode;
}

export const PermissionProvider: React.FC<PermissionProviderProps> = ({ children }) => {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(false);
  const { user, isAuthenticated } = useAuth();

  const fetchUserPermissions = async () => {
    if (!user || !isAuthenticated) {
      setPermissions([]);
      return;
    }

    setLoading(true);
    try {
      console.log('PermissionContext: Fetching permissions for user:', user.id);

      // Get token from localStorage
      const token = localStorage.getItem('token');
      const headers: any = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await axios.get(`http://localhost:8000/api/user-management/users/${user.id}/permissions`, {
        timeout: 10000,
        headers
      });

      console.log('PermissionContext: User permissions loaded:', response.data);
      setPermissions(response.data);
    } catch (error) {
      console.error('PermissionContext: Error fetching permissions:', error);
      setPermissions([]);
    } finally {
      setLoading(false);
    }
  };

  // Load permissions when user changes
  useEffect(() => {
    fetchUserPermissions();
  }, [user, isAuthenticated]);

  // Helper functions to check permissions
  const hasPermission = (code: string): boolean => {
    return permissions.some(permission => permission.code === code);
  };

  const hasModulePermission = (moduleName: string): boolean => {
    return permissions.some(permission => 
      permission.type === 'module' && permission.module_name === moduleName
    );
  };

  const hasMenuPermission = (menuCode: string): boolean => {
    return permissions.some(permission => 
      permission.type === 'menu' && permission.code === menuCode
    );
  };

  const hasButtonPermission = (buttonCode: string): boolean => {
    return permissions.some(permission => 
      permission.type === 'button' && permission.code === buttonCode
    );
  };

  const refreshPermissions = async () => {
    await fetchUserPermissions();
  };

  const value: PermissionContextType = {
    permissions,
    loading,
    hasPermission,
    hasModulePermission,
    hasMenuPermission,
    hasButtonPermission,
    refreshPermissions,
  };

  return (
    <PermissionContext.Provider value={value}>
      {children}
    </PermissionContext.Provider>
  );
};
