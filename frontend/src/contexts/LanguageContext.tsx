import React, { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';
import i18n from '../i18n';

const LANGUAGES = [
  { code: 'vi', label: 'Tiếng Việt' },
  { code: 'en', label: 'English' }
];

const STORAGE_KEY = 'app_language';

interface LanguageContextType {
  language: string;
  setLanguage: (lang: string) => void;
  languages: typeof LANGUAGES;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider = ({ children }: { children: ReactNode }) => {
  const getInitialLanguage = () => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored && LANGUAGES.some(l => l.code === stored)) return stored;
    }
    return 'vi';
  };

  const [language, setLanguageState] = useState<string>(getInitialLanguage);

  useEffect(() => {
    console.log('LanguageContext - Setting language to:', language);
    localStorage.setItem(STORAGE_KEY, language);
    i18n.changeLanguage(language);
    console.log('LanguageContext - i18n language after change:', i18n.language);
  }, [language]);

  const setLanguage = (lang: string) => {
    console.log('LanguageContext - setLanguage called with:', lang);
    if (LANGUAGES.some(l => l.code === lang)) {
      setLanguageState(lang);
    }
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, languages: LANGUAGES }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const ctx = useContext(LanguageContext);
  if (!ctx) throw new Error('useLanguage must be used within a LanguageProvider');
  return ctx;
}; 