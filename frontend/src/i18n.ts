import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

const resources = {
  vi: {
    translation: {
      'app.title': '<PERSON><PERSON> thống Metis',
      'greeting.hello': '<PERSON>n chào',
      'greeting.goodbye': 'Tạm biệt',
      'login.button': 'Đăng nhập',
      'logout.button': 'Đăng xuất',
      // AppsPage
      'apps.search.placeholder': 'Tìm kiếm ứng dụng...',
      'apps.filter.advanced': 'Bộ lọc nâng cao',
      'apps.filter.title': 'Bộ lọc nâng cao',
      'apps.filter.desc': 'Chọn các tiêu chí để lọc module theo nhu cầu.',
      'apps.filter.category': 'Danh mục',
      'apps.filter.selectAll': 'Chọn tất cả',
      'apps.filter.noCategory': 'Không có danh mục nào',
      'apps.filter.selected': 'Đã chọn {{count}} danh mục: {{categories}}',
      'apps.filter.reset': 'Đặt lại',
      'apps.filter.apply': 'Áp dụng',
      'apps.filter.hint': '💡 <PERSON>ác bộ lọc khác sẽ được phát triển thêm trong tương lai.',
      'apps.showing': 'Hiển thị {{count}} trong tổng số {{total}} ứng dụng',
      'apps.tab.development': 'Module Phát triển',
      'apps.tab.system': 'Module Hệ thống',
      'apps.development.title': 'Module Phát triển',
      'apps.development.desc': 'Các module tùy chọn có thể cài đặt hoặc gỡ cài đặt theo nhu cầu. Những module này mở rộng chức năng của hệ thống.',
      'apps.system.title': 'Module Hệ thống',
      'apps.system.desc': 'Các module tự động cài đặt, ẩn hoặc bắt buộc. Những module này là thành phần cốt lõi của hệ thống.',
      'apps.empty.development': 'Không có module phát triển',
      'apps.empty.system': 'Không có module hệ thống',
      'apps.install': 'Cài đặt',
      'apps.installing': 'Đang cài đặt...',
      'apps.installed': 'Đã cài đặt',
      'apps.uninstall': 'Gỡ cài đặt',
      'apps.uninstalling': 'Đang gỡ cài đặt...',
      'apps.uninstall.confirmTitle': 'Xác nhận gỡ cài đặt',
      'apps.uninstall.button': 'Gỡ cài đặt',
      'apps.uninstall.cancel': 'Hủy',
      'apps.uninstall.warningTitle': 'Bạn có chắc chắn muốn gỡ cài đặt module',
      'apps.uninstall.warningDesc': 'Hành động này sẽ xóa toàn bộ dữ liệu và bảng của module này khỏi database. Không thể hoàn tác!',
      'apps.uninstall.disabledTooltip': 'Không thể gỡ cài đặt vì có module khác đang phụ thuộc hoặc lỗi logic.',
      'apps.config.title': 'Cấu hình Module',
      'apps.config.infoTitle': 'Thông tin cấu hình',
      'apps.config.infoDesc': 'Cấu hình này quyết định cách module hoạt động trong hệ thống. Module "base" là module lõi và không thể thay đổi.',
      // Settings Modal
      'apps.settings.title': 'Cài đặt',
      'apps.settings.close': 'Đóng',
      'apps.settings.systemTitle': 'Cài đặt hệ thống',
      'apps.settings.systemDesc': 'Quản lý cấu hình và thiết lập cho Metis Platform.',
      'apps.settings.moduleManagement': 'Quản lý Module',
      'apps.settings.moduleManagementDesc': 'Xem và cấu hình các module trong hệ thống.',
      'apps.settings.configureModule': 'Cấu hình Module',
      'apps.settings.systemInfo': 'Thông tin hệ thống',
      'apps.settings.version': 'Phiên bản',
      'apps.settings.database': 'Database',
      'apps.settings.installedModules': 'Modules đã cài',
      'apps.settings.systemModules': 'Module Hệ thống',
      'apps.settings.developmentModules': 'Module Phát triển',
      'apps.settings.hiddenModules': 'Modules ẩn',
      // User Profile Modal
      'apps.profile.title': 'Thông tin tài khoản',
      'apps.profile.username': 'Tên đăng nhập',
      'apps.profile.phone': 'Số điện thoại',
      'apps.profile.notUpdated': 'Chưa cập nhật',
      'apps.profile.status': 'Trạng thái',
      'apps.profile.active': 'Hoạt động',
      'apps.profile.inactive': 'Bị khóa',
      // Header
      'apps.header.subtitle': 'App Store & Module Management',
      'apps.header.installed': 'Đã cài đặt',
      'apps.header.refresh': 'Làm mới',
      'apps.header.testConnection': 'Kiểm tra kết nối',
      'apps.header.settings': 'Cài đặt',
      'apps.header.connectionSuccess': 'Kết nối backend thành công!',
      'apps.header.connectionError': 'Không thể kết nối đến backend!',
      // Error messages
      'apps.error.connectionTitle': 'Không thể kết nối đến server',
      'apps.error.retry': 'Thử lại',
      'apps.error.loadModules': 'Không thể tải danh sách module.',
      'apps.error.loadConfigs': 'Không thể tải cấu hình module.',
      // Loading
      'apps.loading.modules': 'Đang tải danh sách module...',
      // Empty states
      'apps.empty.systemDesc': 'Các module hệ thống sẽ hiển thị ở đây',
      'apps.empty.developmentDesc': 'Các module phát triển sẽ hiển thị ở đây',
      // Module Configuration
      'apps.config.close': 'Đóng',
      'apps.config.loading': 'Đang tải cấu hình module...',
      'apps.config.coreModule': 'Core Module',
      'apps.config.description': 'Mô tả',
      'apps.config.author': 'Tác giả',
      'apps.config.version': 'Phiên bản',
      'apps.config.category': 'Danh mục',
      'apps.config.status': 'Trạng thái',
      'apps.config.autoInstall': 'Tự động cài đặt',
      'apps.config.manualInstall': 'Cài đặt thủ công',
      'apps.config.hidden': 'Ẩn',
      'apps.config.visible': 'Hiển thị',
      'apps.config.required': 'Bắt buộc',
      'apps.config.coreModuleTitle': 'Module lõi',
      'apps.config.coreModuleDesc': 'Module này là thành phần cốt lõi của hệ thống và không thể thay đổi cấu hình.',
      // Dependency Status
      'apps.dependency.noDependencies': 'Không có phụ thuộc',
      'apps.dependency.allInstalled': 'Tất cả phụ thuộc đã cài đặt',
      'apps.dependency.missingDependencies': 'Thiếu phụ thuộc: {{deps}}',
      // Notifications
      'apps.notification.installSuccess': 'Cài đặt module {{module}} thành công!',
      'apps.notification.uninstallSuccess': 'Gỡ cài đặt module {{module}} thành công!',
      'apps.notification.modulePageNotCreated': 'Module {{module}} page chưa được tạo',
      'apps.notification.logoutSuccess': 'Đăng xuất thành công!',
      // BasePage
      'base.menu.overview': 'Tổng quan',
      'base.module.title': 'Base Module',
      'base.module.summary': 'Base Module',
      'base.module.description': 'Module lõi chứa các thành phần cơ bản của hệ thống Metis Platform',
      'base.error.loadModule': 'Không thể tải thông tin module Base',
      'base.loading.title': 'Đang tải...',
      'base.overview.version': 'Phiên bản',
      'base.overview.author': 'Tác giả',
      'base.overview.status': 'Trạng thái',
      'base.overview.installed': 'Đã cài đặt',
      'base.overview.notInstalled': 'Chưa cài đặt',
      'base.overview.coreModule': 'Core Module',
      'base.overview.autoInstall': 'Tự động cài đặt',
      'base.stats.title': 'Thống kê hệ thống',
      'base.stats.tables': 'Bảng dữ liệu',
      'base.stats.endpoints': 'API Endpoints',
      'base.stats.users': 'Người dùng',
      'base.stats.permissions': 'Quyền hạn',
      'base.activity.title': 'Hoạt động gần đây',
      'base.activity.time2min': '2 phút trước',
      'base.activity.time15min': '15 phút trước',
      'base.activity.time1hour': '1 giờ trước',
      'base.activity.time2hour': '2 giờ trước',
      'base.activity.adminUpdateConfig': 'Người dùng admin đã cập nhật cấu hình hệ thống',
      'base.activity.systemCreateTable': 'Hệ thống đã tạo bảng dữ liệu mới',
      'base.activity.baseModuleInstalled': 'Module Base đã được cài đặt thành công',
      'base.activity.initDatabase': 'Khởi tạo database schema',
      'base.notification.coreModuleInfo': 'Module Base - Core module của hệ thống',
      'base.header.openAppsPage': 'Mở AppsPage trong tab mới',
      'base.header.closeTab': 'Đóng tab này',
      'base.alert.newTab.title': 'Tab mới',
      'base.alert.newTab.description': 'Bạn đang xem Base Module trong tab mới. Tab này độc lập với AppsPage và có thể đóng riêng biệt.',
      'base.breadcrumb.apps': 'Ứng dụng',
      // Site Management Page
      site: {
        // Menu
        menu: {
          overview: 'Tổng quan',
          list: 'Danh sách',
          add: 'Thêm mới'
        },
        
        // Activity
        activity: {
          time2min: '2 phút trước',
          time15min: '15 phút trước',
          time1hour: '1 giờ trước',
          time2hour: '2 giờ trước',
          adminUpdateConfig: 'Người dùng admin đã cập nhật cấu hình hệ thống',
          systemCreateTable: 'Hệ thống đã tạo bảng dữ liệu mới',
          siteManagementInstalled: 'Module Site Management đã được cài đặt thành công',
          initDatabase: 'Khởi tạo database schema'
        },
        
        // Module
        module: {
          summary: 'Site Management Module',
          description: 'Module quản lý thông tin và khai báo Site.'
        },
        
        // Overview
        overview: {
          version: 'Phiên bản',
          author: 'Tác giả',
          status: 'Trạng thái',
          installed: 'Đã cài đặt',
          notInstalled: 'Chưa cài đặt',
          coreModule: 'Core Module',
          autoInstall: 'Tự động cài đặt',
          systemStats: 'Thống kê hệ thống',
          dataTables: 'Bảng dữ liệu',
          apiEndpoints: 'API Endpoints',
          users: 'Người dùng',
          permissions: 'Quyền hạn',
          recentActivities: 'Hoạt động gần đây'
        },
        
        // Table
        table: {
          code: 'Mã Site',
          name: 'Tên Site',
          description: 'Mô tả',
          noDescription: 'Không có mô tả',
          status: 'Trạng thái',
          active: 'Hoạt động',
          inactive: 'Tạm dừng',
          actions: 'Thao tác',
          edit: 'Chỉnh sửa',
          delete: 'Xóa'
        },
        
        // List
        list: {
          title: 'Danh sách Sites',
          sites: 'sites',
          searchPlaceholder: 'Tìm kiếm theo mã, tên hoặc mô tả...',
          refresh: 'Làm mới',
          addNew: 'Thêm Site mới (Switch to form)',
          noSearchResults: 'Không tìm thấy Sites phù hợp',
          noData: 'Không có dữ liệu Sites',
          tryAgain: 'Thử lại',
          pagination: '{{start}}-{{end}} của {{total}} sites'
        },
        
        // Form
        form: {
          updateTitle: 'Cập nhật Site',
          addTitle: 'Thêm mới Site',
          code: 'Mã Site',
          codeRequired: 'Vui lòng nhập mã Site!',
          codePlaceholder: 'Nhập mã Site (VD: SITE001)',
          name: 'Tên Site',
          nameRequired: 'Vui lòng nhập tên Site!',
          nameMaxLength: 'Tên Site không được quá 250 ký tự!',
          namePlaceholder: 'Nhập tên Site',
          description: 'Mô tả',
          descriptionMaxLength: 'Mô tả không được quá 1000 ký tự!',
          descriptionPlaceholder: 'Nhập mô tả Site (không bắt buộc)',
          parentSite: 'Site cha',
          parentSitePlaceholder: 'Chọn Site cha (không bắt buộc)',
          status: 'Trạng thái',
          activated: 'Kích hoạt',
          deactivated: 'Tạm dừng',
          cancel: 'Hủy bỏ',
          saveAndNew: 'Lưu & Tạo mới',
          saveAndClose: 'Lưu & Đóng'
        },
        
        // Modal
        modal: {
          editTitle: 'Chỉnh sửa Site',
          save: 'Lưu',
          cancel: 'Hủy',
          code: 'Mã Site',
          name: 'Tên Site',
          nameRequired: 'Vui lòng nhập tên Site!',
          nameMaxLength: 'Tên Site không được quá 250 ký tự!',
          namePlaceholder: 'Nhập tên Site',
          description: 'Mô tả',
          descriptionMaxLength: 'Mô tả không được quá 1000 ký tự!',
          descriptionPlaceholder: 'Nhập mô tả Site (không bắt buộc)',
          parentSite: 'Site cha',
          parentSitePlaceholder: 'Chọn Site cha (không bắt buộc)',
          status: 'Trạng thái',
          activated: 'Kích hoạt',
          deactivated: 'Tạm dừng',
          cannotDeleteTitle: 'Không thể xóa Site',
          close: 'Đóng',
          cannotDeleteMessage: 'Không thể xóa Site "{name}"',
          cannotDeleteReason: 'Site này có {count} Site con. Vui lòng xóa tất cả Site con trước khi xóa Site cha.',
          confirmDeleteTitle: 'Xác nhận xóa Site',
          delete: 'Xóa',
          confirmDeleteMessage: 'Bạn có chắc chắn muốn xóa Site "{name}"?',
          confirmDeleteWarning: 'Hành động này không thể hoàn tác. Tất cả dữ liệu liên quan đến Site này sẽ bị xóa vĩnh viễn.'
        },
        
        // Breadcrumb
        breadcrumb: {
          apps: 'Ứng dụng',
          siteManagement: 'Site Management',
          overview: 'Tổng quan',
          list: 'Danh sách',
          add: 'Thêm mới',
          update: 'Cập nhật'
        },
        
        // Header
        header: {
          title: 'Site Management Module',
          subtitle: 'Quản lý thông tin và khai báo Site',
          openAppsPage: 'Mở AppsPage trong tab mới',
          closeTab: 'Đóng tab này'
        },
        
        // Loading
        loading: {
          loading: 'Đang tải...',
          moduleName: 'Site Management Module'
        },
        
        // Info
        info: {
          moduleDescription: 'Module Site Management - Module quản lý thông tin và khai báo Site'
        },
        
        // Error messages
        error: {
          sessionExpired: 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.',
          loadModule: 'Không thể tải thông tin module Site Management',
          loadSites: 'Không thể tải danh sách Sites: {{error}}',
          updateStatus: 'Không thể cập nhật trạng thái',
          deleteSite: 'Không thể xóa Site: {{error}}',
          updateSite: 'Không thể cập nhật Site: {{error}}',
          saveSite: 'Không thể lưu Site: {{error}}',
          checkSiteCode: 'Không thể kiểm tra mã Site'
        },
        
        // Validation messages
        validation: {
          codeRequired: 'Vui lòng nhập mã Site!',
          codeMinLength: 'Mã Site phải có ít nhất 3 ký tự!',
          codeMaxLength: 'Mã Site không được quá 20 ký tự!',
          codeFormat: 'Mã Site chỉ được chứa chữ hoa, số và dấu gạch dưới!',
          codeExists: 'Mã Site đã tồn tại!'
        },
        
        // Notifications
        notification: {
          switchToAddMode: 'Chuyển sang chế độ thêm mới Site',
          updateStatusSuccess: 'Cập nhật trạng thái thành công',
          deleteSuccess: 'Đã xóa Site "{name}" thành công',
          updateSuccess: 'Cập nhật Site thành công',
          createSuccess: 'Tạo Site mới thành công'
        },
        
        // Roles
        roles: {
          title: 'Danh sách Roles',
          searchPlaceholder: 'Tìm kiếm roles...',
          refreshData: 'Tải lại dữ liệu',
          addRole: 'Thêm Role',
          table: {
            id: 'ID',
            name: 'Tên Role',
            description: 'Mô tả',
            status: 'Kích hoạt',
            activated: 'Bật',
            deactivated: 'Tắt',
            edit: 'Chỉnh sửa',
            delete: 'Xóa'
          },
          addModal: {
            title: 'Thêm Role mới'
          },
          editModal: {
            title: 'Chỉnh sửa Role'
          },
          deleteModal: {
            title: 'Xác nhận xóa Role',
            confirmMessage: 'Bạn có chắc chắn muốn xóa role này khỏi hệ thống?',
            roleInfoToDelete: 'Thông tin Role sẽ bị xóa',
            warningTitle: 'Lưu ý quan trọng',
            warningMessage: 'Hành động này không thể hoàn tác. Tất cả dữ liệu liên quan đến role này sẽ bị xóa vĩnh viễn khỏi hệ thống.',
            cancel: 'Hủy bỏ',
            confirmDelete: 'Xác nhận xóa'
          },
          form: {
            name: 'Tên Role',
            nameRequired: 'Vui lòng nhập tên Role',
            nameExists: 'Tên Role đã tồn tại',
            nameAvailable: 'Tên Role có thể sử dụng',
            nameMinLength: 'Tên Role phải có ít nhất 2 ký tự',
            nameMaxLength: 'Tên Role không được vượt quá 100 ký tự',
            namePlaceholder: 'Nhập tên Role',
            description: 'Mô tả',
            descriptionPlaceholder: 'Nhập mô tả (không bắt buộc)',
            status: 'Trạng thái',
            activated: 'Kích hoạt',
            deactivated: 'Không',
            cancel: 'Hủy',
            saveAndNew: 'Lưu & Thêm mới',
            saveAndClose: 'Lưu & Đóng',
            save: 'Lưu thay đổi'
          }
        },
        
        // User Roles
        userRoles: {
          title: 'Danh sách User Roles',
          searchPlaceholder: 'Tìm kiếm user roles...',
          refreshData: 'Tải lại dữ liệu',
          addNew: 'Thêm mới',
          table: {
            userId: 'User ID',
            username: 'Username',
            fullName: 'Họ và tên',
            roleId: 'Role ID',
            roleName: 'Tên Role',
            delete: 'Xóa'
          },
          addModal: {
            title: 'Thêm User Role mới'
          },
          deleteModal: {
            title: 'Xác nhận xóa User Role',
            confirmMessage: 'Bạn có chắc chắn muốn xóa user role này khỏi hệ thống?',
            userRoleInfoToDelete: 'Thông tin User Role sẽ bị xóa',
            warningTitle: 'Important Notice',
            warningMessage: 'This action cannot be undone. User will lose permissions of this role after deletion.',
            cancel: 'Cancel',
            confirmDelete: 'Confirm Delete'
          },
          form: {
            user: 'User',
            userRequired: 'Please select User',
            userPlaceholder: 'Search and select User...',
            role: 'Role',
            roleRequired: 'Please select Role',
            rolePlaceholder: 'Search and select Role...',
            currentRolesTitle: 'Current User Roles',
            noRolesMessage: 'User "{{username}} - {{fullName}}" has no roles yet.',
            hasRolesMessage: 'currently has {{count}} roles',
            cancel: 'Cancel',
            saveAndNew: 'Save & New',
            saveAndClose: 'Save & Close'
          }
        }
      },
      // User Management Page
      'user.menu.overview': 'Tổng quan',
      'user.menu.users': 'Người dùng',
      'user.menu.roles': 'Vai trò',
      'user.menu.userRoles': 'Phân quyền',
      
      // Tab và Breadcrumb
      'user.tab.newTab.title': 'Tab mới',
      'user.tab.newTab.description': 'Bạn đang xem User Management Module trong tab mới. Tab này độc lập với AppsPage và có thể đóng riêng biệt.',
      'user.breadcrumb.apps': 'Ứng dụng',
      'user.breadcrumb.module': 'Module Quản lý người dùng',
      'user.breadcrumb.overview': 'Tổng quan',
      'user.breadcrumb.users': 'Người dùng',
      'user.breadcrumb.roles': 'Vai trò',
      'user.breadcrumb.userRoles': 'Phân quyền',
      
      // Activity
      'user.activity.time5min': '5 phút trước',
      'user.activity.time15min': '15 phút trước',
      'user.activity.time1hour': '1 giờ trước',
      'user.activity.time2hour': '2 giờ trước',
      'user.activity.newUserCreated': 'Người dùng mới "john.doe" đã được tạo',
      'user.activity.roleUpdated': 'Quyền của vai trò "Manager" đã được cập nhật',
      'user.activity.adminLogin': 'Người dùng "admin" đã đăng nhập từ IP mới',
      'user.activity.moduleInstalled': 'Module Quản lý người dùng đã được cài đặt thành công',
      
      // Module
      'user.module.summary': 'Module Quản lý người dùng',
      'user.module.description': 'Module quản lý người dùng và vai trò với quyền hạn chi tiết trong hệ thống Metis Platform',
      
      // Overview
      'user.overview.version': 'Phiên bản',
      'user.overview.author': 'Tác giả',
      'user.overview.status': 'Trạng thái',
      'user.overview.installed': 'Đã cài đặt',
      'user.overview.notInstalled': 'Chưa cài đặt',
      'user.overview.coreModule': 'Module cốt lõi',
      'user.overview.autoInstall': 'Tự động cài đặt',
      'user.overview.userStats': 'Thống kê người dùng',
      'user.overview.totalUsers': 'Tổng số người dùng',
      'user.overview.activeUsers': 'Người dùng hoạt động',
      'user.overview.roles': 'Vai trò',
      'user.overview.permissions': 'Quyền hạn',
      'user.overview.recentActivities': 'Hoạt động gần đây',
      
      // Users
      'user.users.title': 'Quản lý người dùng',
      'user.users.searchPlaceholder': 'Tìm kiếm người dùng...',
      'user.users.refreshData': 'Làm mới dữ liệu',
      'user.users.addUser': 'Thêm người dùng',
      'user.users.pagination': '{{start}}-{{end}} trong tổng số {{total}} người dùng',
      'user.users.editUser': 'Chỉnh sửa người dùng',
      'user.users.addNewUser': 'Thêm người dùng mới',
      'user.users.table.username': 'Tên đăng nhập',
      'user.users.table.fullName': 'Họ và tên',
      'user.users.table.email': 'Email',
      'user.users.table.phoneNumber': 'Số điện thoại',
      'user.users.table.status': 'Trạng thái',
      'user.users.table.active': 'Hoạt động',
      'user.users.table.inactive': 'Không hoạt động',
      'user.users.table.actions': 'Thao tác',
      'user.users.table.edit': 'Sửa',
      'user.users.table.delete': 'Xóa',
      'user.users.deleteModal.title': 'Xác nhận xóa người dùng',
      'user.users.deleteModal.confirmMessage': 'Bạn có chắc chắn muốn xóa người dùng này khỏi hệ thống?',
      'user.users.deleteModal.userInfoToDelete': 'Thông tin người dùng sẽ bị xóa',
      'user.users.deleteModal.warningTitle': 'Lưu ý quan trọng',
      'user.users.deleteModal.warningMessage': 'Hành động này không thể hoàn tác. Tất cả dữ liệu liên quan đến người dùng này sẽ bị xóa vĩnh viễn khỏi hệ thống.',
      'user.users.deleteModal.cancel': 'Hủy',
      'user.users.deleteModal.confirmDelete': 'Xác nhận xóa',
      'user.users.form.username': 'Tên đăng nhập',
      'user.users.form.usernameRequired': 'Vui lòng nhập tên đăng nhập!',
      'user.users.form.usernameMinLength': 'Tên đăng nhập phải có ít nhất 3 ký tự!',
      'user.users.form.usernameMaxLength': 'Tên đăng nhập không được vượt quá 50 ký tự!',
      'user.users.form.usernameFormat': 'Tên đăng nhập chỉ được chứa chữ, số và dấu gạch dưới!',
      'user.users.form.usernamePlaceholder': 'Nhập tên đăng nhập',
      'user.users.form.usernameExists': 'Tên đăng nhập đã tồn tại',
      'user.users.form.usernameAvailable': 'Tên đăng nhập có thể sử dụng',
      'user.users.form.fullName': 'Họ và tên',
      'user.users.form.fullNameRequired': 'Vui lòng nhập họ và tên!',
      'user.users.form.fullNameMinLength': 'Họ và tên phải có ít nhất 2 ký tự!',
      'user.users.form.fullNameMaxLength': 'Họ và tên không được vượt quá 100 ký tự!',
      'user.users.form.fullNamePlaceholder': 'Nhập họ và tên',
      'user.users.form.email': 'Email',
      'user.users.form.emailRequired': 'Email là bắt buộc!',
      'user.users.form.emailInvalid': 'Email không hợp lệ!',
      'user.users.form.emailMaxLength': 'Email không được vượt quá 150 ký tự!',
      'user.users.form.emailPlaceholder': 'Nhập email',
      'user.users.form.emailExists': 'Email đã tồn tại',
      'user.users.form.emailAvailable': 'Email có thể sử dụng',
      'user.users.form.phoneNumber': 'Số điện thoại',
      'user.users.form.phoneNumberInvalid': 'Số điện thoại không hợp lệ!',
      'user.users.form.phoneNumberMaxLength': 'Số điện thoại không được vượt quá 20 ký tự!',
      'user.users.form.phoneNumberPlaceholder': 'Nhập số điện thoại',
      'user.users.form.password': 'Mật khẩu',
      'user.users.form.passwordRequired': 'Vui lòng nhập mật khẩu!',
      'user.users.form.passwordMinLength': 'Mật khẩu phải có ít nhất 8 ký tự!',
      'user.users.form.passwordFormat': 'Mật khẩu phải chứa ít nhất 1 chữ hoa, 1 chữ thường, 1 số và 1 ký tự đặc biệt!',
      'user.users.form.passwordPlaceholder': 'Nhập mật khẩu',
      'user.users.form.confirmPassword': 'Xác nhận mật khẩu',
      'user.users.form.confirmPasswordRequired': 'Vui lòng xác nhận mật khẩu!',
      'user.users.form.confirmPasswordMismatch': 'Xác nhận mật khẩu không khớp!',
      'user.users.form.confirmPasswordPlaceholder': 'Nhập lại mật khẩu',
      'user.users.form.status': 'Trạng thái',
      'user.users.form.activated': 'Đã kích hoạt',
      'user.users.form.deactivated': 'Chưa kích hoạt',
      'user.users.form.cancel': 'Hủy',
      'user.users.form.saveAndNew': 'Lưu & Thêm mới',
      'user.users.form.update': 'Cập nhật',
      'user.users.form.saveAndClose': 'Lưu & Đóng',
      'user.users.form.checking': 'Đang kiểm tra...',
      
      // Roles
      'user.roles.title': 'Danh sách vai trò',
      'user.roles.searchPlaceholder': 'Tìm kiếm vai trò...',
      'user.roles.refreshData': 'Làm mới dữ liệu',
      'user.roles.addRole': 'Thêm vai trò',
      'user.roles.table.id': 'ID',
      'user.roles.table.name': 'Tên vai trò',
      'user.roles.table.description': 'Mô tả',
      'user.roles.table.status': 'Trạng thái',
      'user.roles.table.activated': 'Bật',
      'user.roles.table.deactivated': 'Tắt',
      'user.roles.table.edit': 'Sửa',
      'user.roles.table.delete': 'Xóa',
      'user.roles.addModal.title': 'Thêm vai trò mới',
      'user.roles.editModal.title': 'Chỉnh sửa vai trò',
      'user.roles.deleteModal.title': 'Xác nhận xóa vai trò',
      'user.roles.deleteModal.confirmMessage': 'Bạn có chắc chắn muốn xóa vai trò này khỏi hệ thống?',
      'user.roles.deleteModal.roleInfoToDelete': 'Thông tin vai trò sẽ bị xóa',
      'user.roles.deleteModal.warningTitle': 'Lưu ý quan trọng',
      'user.roles.deleteModal.warningMessage': 'Hành động này không thể hoàn tác. Tất cả dữ liệu liên quan đến vai trò này sẽ bị xóa vĩnh viễn khỏi hệ thống.',
      'user.roles.deleteModal.cancel': 'Hủy',
      'user.roles.deleteModal.confirmDelete': 'Xác nhận xóa',
      'user.roles.form.name': 'Tên vai trò',
      'user.roles.form.nameRequired': 'Vui lòng nhập tên vai trò',
      'user.roles.form.nameExists': 'Tên vai trò đã tồn tại',
      'user.roles.form.nameAvailable': 'Tên vai trò có thể sử dụng',
      'user.roles.form.nameMinLength': 'Tên vai trò phải có ít nhất 2 ký tự',
      'user.roles.form.nameMaxLength': 'Tên vai trò không được vượt quá 100 ký tự',
      'user.roles.form.checking': 'Đang kiểm tra...',
      'user.roles.form.namePlaceholder': 'Nhập tên vai trò',
      'user.roles.form.description': 'Mô tả',
      'user.roles.form.descriptionPlaceholder': 'Nhập mô tả (không bắt buộc)',
      'user.roles.form.status': 'Trạng thái',
      'user.roles.form.activated': 'Đã kích hoạt',
      'user.roles.form.deactivated': 'Chưa kích hoạt',
      'user.roles.form.cancel': 'Hủy',
      'user.roles.form.saveAndNew': 'Lưu & Thêm mới',
      'user.roles.form.saveAndClose': 'Lưu & Đóng',
      'user.roles.form.save': 'Lưu thay đổi',
      
      // User Roles
      'user.userRoles.title': 'Danh sách phân quyền',
      'user.userRoles.searchPlaceholder': 'Tìm kiếm phân quyền...',
      'user.userRoles.refreshData': 'Làm mới dữ liệu',
      'user.userRoles.addNew': 'Thêm mới',
      'user.userRoles.table.userId': 'ID người dùng',
      'user.userRoles.table.username': 'Tên đăng nhập',
      'user.userRoles.table.fullName': 'Họ và tên',
      'user.userRoles.table.roleId': 'ID vai trò',
      'user.userRoles.table.roleName': 'Tên vai trò',
      'user.userRoles.table.delete': 'Xóa',
      'user.userRoles.addModal.title': 'Thêm phân quyền người dùng',
      'user.userRoles.deleteModal.title': 'Xác nhận xóa phân quyền',
      'user.userRoles.deleteModal.confirmMessage': 'Bạn có chắc chắn muốn xóa phân quyền này khỏi hệ thống?',
      'user.userRoles.deleteModal.userRoleInfoToDelete': 'Thông tin phân quyền sẽ bị xóa',
      'user.userRoles.deleteModal.warningTitle': 'Lưu ý quan trọng',
      'user.userRoles.deleteModal.warningMessage': 'Hành động này không thể hoàn tác. Người dùng sẽ mất quyền của vai trò này sau khi xóa.',
      'user.userRoles.deleteModal.cancel': 'Hủy',
      'user.userRoles.deleteModal.confirmDelete': 'Xác nhận xóa',
      'user.userRoles.form.user': 'Người dùng',
      'user.userRoles.form.userRequired': 'Vui lòng chọn người dùng',
      'user.userRoles.form.userPlaceholder': 'Tìm kiếm và chọn người dùng...',
      'user.userRoles.form.role': 'Vai trò',
      'user.userRoles.form.roleRequired': 'Vui lòng chọn vai trò',
      'user.userRoles.form.rolePlaceholder': 'Tìm kiếm và chọn vai trò...',
      'user.userRoles.form.currentRolesTitle': 'Các vai trò hiện tại',
      'user.userRoles.form.noRolesMessage': 'Người dùng "{{username}} - {{fullName}}" chưa có vai trò nào.',
      'user.userRoles.form.hasRolesMessage': 'hiện có {{count}} vai trò',
      'user.userRoles.form.cancel': 'Hủy',
      'user.userRoles.form.saveAndNew': 'Lưu & Thêm mới',
      'user.userRoles.form.saveAndClose': 'Lưu & Đóng',
      
      // Info
      'user.info.moduleDescription': 'Module Quản lý người dùng - Quản lý người dùng và vai trò',
      
      // Warning
      'user.warning.userRoleExists': '⚠️ Người dùng "{{username}} - {{fullName}}" đã có vai trò "{{roleName}}". Vui lòng chọn người dùng hoặc vai trò khác.',
      
      // Error messages
      'user.error.loadModule': 'Không thể tải thông tin module Quản lý người dùng',
      'user.error.loadUsers': 'Không thể tải danh sách người dùng',
      'user.error.loadRoles': 'Không thể tải danh sách vai trò',
      'user.error.loadUserRoles': 'Không thể tải danh sách phân quyền',
      'user.error.refreshData': 'Không thể làm mới dữ liệu',
      'user.error.updateUserStatus': 'Có lỗi khi cập nhật trạng thái người dùng',
      'user.error.deleteUser': 'Có lỗi khi xóa người dùng',
      'user.error.saveUser': 'Không thể lưu người dùng',
      'user.error.validationError': 'Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.',
      'user.error.updateRoleStatus': 'Không thể cập nhật trạng thái vai trò',
      'user.error.checkRoleName': 'Không thể kiểm tra tên vai trò',
      'user.error.roleNameExists': 'Tên vai trò đã tồn tại!',
      'user.error.createRole': 'Không thể tạo vai trò mới',
      'user.error.updateRole': 'Không thể cập nhật vai trò',
      'user.error.deleteRole': 'Không thể xóa vai trò',
      'user.error.refreshRoles': 'Không thể làm mới danh sách vai trò',
      'user.error.refreshUserRoles': 'Không thể làm mới danh sách phân quyền',
      'user.error.deleteUserRole': 'Không thể xóa phân quyền',
      'user.error.selectUserAndRole': 'Vui lòng chọn người dùng và vai trò',
      'user.error.createUserRole': 'Không thể tạo phân quyền',
      'user.error.userRoleExists': 'Người dùng "{{username}} - {{fullName}}" đã có vai trò "{{roleName}}".',
      
      // Notifications
      'user.notification.loadingData': 'Đang tải dữ liệu...',
      'user.notification.dataRefreshed': 'Dữ liệu đã được làm mới thành công',
      'user.notification.userStatusUpdated': 'Người dùng đã được {{action}} thành công',
      'user.notification.activated': 'kích hoạt',
      'user.notification.deactivated': 'hủy kích hoạt',
      'user.notification.userDeleted': 'Người dùng đã được xóa thành công',
      'user.notification.userUpdated': 'Người dùng đã được cập nhật thành công',
      'user.notification.userCreated': 'Người dùng đã được tạo thành công',
      'user.notification.roleStatusUpdated': 'Trạng thái vai trò đã được cập nhật',
      'user.notification.roleCreated': 'Vai trò mới đã được tạo thành công',
      'user.notification.roleUpdated': 'Vai trò đã được cập nhật thành công',
      'user.notification.roleDeleted': 'Vai trò đã được xóa thành công',
      'user.notification.rolesRefreshed': 'Danh sách vai trò đã được làm mới',
      'user.notification.userRolesRefreshed': 'Danh sách phân quyền đã được làm mới',
      'user.notification.userRoleDeleted': 'Phân quyền đã được xóa thành công',
      'user.notification.userRoleCreated': 'Phân quyền đã được tạo thành công',
      // Loading
      'user.loading.title': 'Đang tải...',
      'user.loading.module': 'Module Quản lý người dùng',
      // Header
      'user.header.openAppsPage': 'Mở trang ứng dụng',
      'user.header.closeTab': 'Đóng tab'
    }
  },
  en: {
    translation: {
      'app.title': 'Metis System',
      'greeting.hello': 'Hello',
      'greeting.goodbye': 'Goodbye',
      'login.button': 'Login',
      'logout.button': 'Logout',
      // AppsPage
      'apps.search.placeholder': 'Search applications...',
      'apps.filter.advanced': 'Advanced Filter',
      'apps.filter.title': 'Advanced Filter',
      'apps.filter.desc': 'Select criteria to filter modules as needed.',
      'apps.filter.category': 'Category',
      'apps.filter.selectAll': 'Select all',
      'apps.filter.noCategory': 'No categories',
      'apps.filter.selected': '{{count}} categories selected: {{categories}}',
      'apps.filter.reset': 'Reset',
      'apps.filter.apply': 'Apply',
      'apps.filter.hint': '💡 More filters will be developed in the future.',
      'apps.showing': 'Showing {{count}} of {{total}} applications',
      'apps.tab.development': 'Development Modules',
      'apps.tab.system': 'System Modules',
      'apps.development.title': 'Development Modules',
      'apps.development.desc': 'Optional modules that can be installed or uninstalled as needed. These modules extend system functionality.',
      'apps.system.title': 'System Modules',
      'apps.system.desc': 'Modules that are auto-installed, hidden, or required. These are core components of the system.',
      'apps.empty.development': 'No development modules',
      'apps.empty.system': 'No system modules',
      'apps.install': 'Install',
      'apps.installing': 'Installing...',
      'apps.installed': 'Installed',
      'apps.uninstall': 'Uninstall',
      'apps.uninstalling': 'Uninstalling...',
      'apps.uninstall.confirmTitle': 'Uninstall Confirmation',
      'apps.uninstall.button': 'Uninstall',
      'apps.uninstall.cancel': 'Cancel',
      'apps.uninstall.warningTitle': 'Are you sure you want to uninstall module',
      'apps.uninstall.warningDesc': 'This action will delete all data and tables of this module from the database. This cannot be undone!',
      'apps.uninstall.disabledTooltip': 'Cannot uninstall because another module depends on it or due to logic error.',
      'apps.config.title': 'Module Configuration',
      'apps.config.infoTitle': 'Configuration Info',
      'apps.config.infoDesc': 'This configuration determines how the module operates in the system. The "base" module is core and cannot be changed.',
      // Settings Modal
      'apps.settings.title': 'Settings',
      'apps.settings.close': 'Close',
      'apps.settings.systemTitle': 'System Settings',
      'apps.settings.systemDesc': 'Manage configuration and settings for Metis Platform.',
      'apps.settings.moduleManagement': 'Module Management',
      'apps.settings.moduleManagementDesc': 'View and configure modules in the system.',
      'apps.settings.configureModule': 'Configure Module',
      'apps.settings.systemInfo': 'System Information',
      'apps.settings.version': 'Version',
      'apps.settings.database': 'Database',
      'apps.settings.installedModules': 'Installed Modules',
      'apps.settings.systemModules': 'System Modules',
      'apps.settings.developmentModules': 'Development Modules',
      'apps.settings.hiddenModules': 'Hidden Modules',
      // User Profile Modal
      'apps.profile.title': 'Account Information',
      'apps.profile.username': 'Username',
      'apps.profile.phone': 'Phone',
      'apps.profile.notUpdated': 'Not updated',
      'apps.profile.status': 'Status',
      'apps.profile.active': 'Active',
      'apps.profile.inactive': 'Inactive',
      // Header
      'apps.header.subtitle': 'App Store & Module Management',
      'apps.header.installed': 'Installed',
      'apps.header.refresh': 'Refresh',
      'apps.header.testConnection': 'Test Connection',
      'apps.header.settings': 'Settings',
      'apps.header.connectionSuccess': 'Backend connection successful!',
      'apps.header.connectionError': 'Cannot connect to backend!',
      // Error messages
      'apps.error.connectionTitle': 'Cannot connect to server',
      'apps.error.retry': 'Retry',
      'apps.error.loadModules': 'Cannot load module list.',
      'apps.error.loadConfigs': 'Cannot load module configuration.',
      // Loading
      'apps.loading.modules': 'Loading module list...',
      // Empty states
      'apps.empty.systemDesc': 'System modules will appear here',
      'apps.empty.developmentDesc': 'Development modules will appear here',
      // Module Configuration
      'apps.config.close': 'Close',
      'apps.config.loading': 'Loading module configuration...',
      'apps.config.coreModule': 'Core Module',
      'apps.config.description': 'Description',
      'apps.config.author': 'Author',
      'apps.config.version': 'Version',
      'apps.config.category': 'Category',
      'apps.config.status': 'Status',
      'apps.config.autoInstall': 'Auto Install',
      'apps.config.manualInstall': 'Manual Install',
      'apps.config.hidden': 'Hidden',
      'apps.config.visible': 'Visible',
      'apps.config.required': 'Required',
      'apps.config.coreModuleTitle': 'Core Module',
      'apps.config.coreModuleDesc': 'This module is a core component of the system and cannot have its configuration changed.',
      // Dependency Status
      'apps.dependency.noDependencies': 'No dependencies',
      'apps.dependency.allInstalled': 'All dependencies installed',
      'apps.dependency.missingDependencies': 'Missing dependencies: {{deps}}',
      // Notifications
      'apps.notification.installSuccess': 'Module {{module}} installed successfully!',
      'apps.notification.uninstallSuccess': 'Module {{module}} uninstalled successfully!',
      'apps.notification.modulePageNotCreated': 'Module {{module}} page not created yet',
      'apps.notification.logoutSuccess': 'Logout successful!',
      // BasePage
      'base.menu.overview': 'Overview',
      'base.module.title': 'Base Module',
      'base.module.summary': 'Base Module',
      'base.module.description': 'Core module containing basic components of Metis Platform system',
      'base.error.loadModule': 'Cannot load Base module information',
      'base.loading.title': 'Loading...',
      'base.overview.version': 'Version',
      'base.overview.author': 'Author',
      'base.overview.status': 'Status',
      'base.overview.installed': 'Installed',
      'base.overview.notInstalled': 'Not installed',
      'base.overview.coreModule': 'Core Module',
      'base.overview.autoInstall': 'Auto Install',
      'base.stats.title': 'System Statistics',
      'base.stats.tables': 'Data Tables',
      'base.stats.endpoints': 'API Endpoints',
      'base.stats.users': 'Users',
      'base.stats.permissions': 'Permissions',
      'base.activity.title': 'Recent Activities',
      'base.activity.time2min': '2 minutes ago',
      'base.activity.time15min': '15 minutes ago',
      'base.activity.time1hour': '1 hour ago',
      'base.activity.time2hour': '2 hours ago',
      'base.activity.adminUpdateConfig': 'Admin user updated system configuration',
      'base.activity.systemCreateTable': 'System created new data table',
      'base.activity.baseModuleInstalled': 'Base module installed successfully',
      'base.activity.initDatabase': 'Initialize database schema',
      'base.notification.coreModuleInfo': 'Base Module - Core module of the system',
      'base.header.openAppsPage': 'Open AppsPage in new tab',
      'base.header.closeTab': 'Close this tab',
      'base.alert.newTab.title': 'New Tab',
      'base.alert.newTab.description': 'You are viewing Base Module in a new tab. This tab is independent of AppsPage and can be closed separately.',
      'base.breadcrumb.apps': 'Apps',
      // Site Management Page
      site: {
        // Menu
        menu: {
          overview: 'Overview',
          list: 'List',
          add: 'Add New'
        },
        
        // Activity
        activity: {
          time2min: '2 minutes ago',
          time15min: '15 minutes ago',
          time1hour: '1 hour ago',
          time2hour: '2 hours ago',
          adminUpdateConfig: 'Người dùng admin đã cập nhật cấu hình hệ thống',
          systemCreateTable: 'Hệ thống đã tạo bảng dữ liệu mới',
          siteManagementInstalled: 'Site Management module installed successfully',
          initDatabase: 'Initialize database schema'
        },
        
        // Module
        module: {
          summary: 'Site Management Module',
          description: 'Module for managing site information and declarations.'
        },
        
        // Overview
        overview: {
          version: 'Version',
          author: 'Author',
          status: 'Status',
          installed: 'Installed',
          notInstalled: 'Not Installed',
          coreModule: 'Core Module',
          autoInstall: 'Auto Install',
          systemStats: 'System Statistics',
          dataTables: 'Data Tables',
          apiEndpoints: 'API Endpoints',
          users: 'Users',
          permissions: 'Permissions',
          recentActivities: 'Recent Activities'
        },
        
        // Table
        table: {
          code: 'Site Code',
          name: 'Site Name',
          description: 'Description',
          noDescription: 'No description',
          status: 'Status',
          active: 'Active',
          inactive: 'Inactive',
          actions: 'Actions',
          edit: 'Edit',
          delete: 'Delete'
        },
        
        // List
        list: {
          title: 'Sites List',
          sites: 'sites',
          searchPlaceholder: 'Search by code, name or description...',
          refresh: 'Refresh',
          addNew: 'Add New Site (Switch to form)',
          noSearchResults: 'No matching sites found',
          noData: 'No site data',
          tryAgain: 'Try Again',
          pagination: '{{start}}-{{end}} of {{total}} sites'
        },
        
        // Form
        form: {
          updateTitle: 'Update Site',
          addTitle: 'Add New Site',
          code: 'Site Code',
          codeRequired: 'Please enter site code!',
          codePlaceholder: 'Enter site code (e.g., SITE001)',
          name: 'Site Name',
          nameRequired: 'Please enter site name!',
          nameMaxLength: 'Site name cannot exceed 250 characters!',
          namePlaceholder: 'Enter site name',
          description: 'Description',
          descriptionMaxLength: 'Description cannot exceed 1000 characters!',
          descriptionPlaceholder: 'Enter site description (optional)',
          parentSite: 'Parent Site',
          parentSitePlaceholder: 'Select parent site (optional)',
          status: 'Status',
          activated: 'Activated',
          deactivated: 'Deactivated',
          cancel: 'Cancel',
          saveAndNew: 'Save & New',
          saveAndClose: 'Save & Close'
        },
        
        // Modal
        modal: {
          editTitle: 'Edit Site',
          save: 'Save',
          cancel: 'Cancel',
          code: 'Site Code',
          name: 'Site Name',
          nameRequired: 'Please enter site name!',
          nameMaxLength: 'Site name cannot exceed 250 characters!',
          namePlaceholder: 'Enter site name',
          description: 'Description',
          descriptionMaxLength: 'Description cannot exceed 1000 characters!',
          descriptionPlaceholder: 'Enter site description (optional)',
          parentSite: 'Parent Site',
          parentSitePlaceholder: 'Select parent site (optional)',
          status: 'Status',
          activated: 'Activated',
          deactivated: 'Deactivated',
          cannotDeleteTitle: 'Cannot Delete Site',
          close: 'Close',
          cannotDeleteMessage: 'Cannot delete site "{name}"',
          cannotDeleteReason: 'This site has {count} child sites. Please delete all child sites before deleting the parent site.',
          confirmDeleteTitle: 'Confirm Delete Site',
          delete: 'Delete',
          confirmDeleteMessage: 'Are you sure you want to delete site "{name}"?',
          confirmDeleteWarning: 'This action cannot be undone. All data related to this site will be permanently deleted.'
        },
        
        // Breadcrumb
        breadcrumb: {
          apps: 'Apps',
          siteManagement: 'Site Management',
          overview: 'Overview',
          list: 'List',
          add: 'Add New',
          update: 'Update'
        },
        
        // Header
        header: {
          title: 'Site Management Module',
          subtitle: 'Manage site information and declarations',
          openAppsPage: 'Open AppsPage in new tab',
          closeTab: 'Close this tab'
        },
        
        // Loading
        loading: {
          loading: 'Loading...',
          moduleName: 'Site Management Module'
        },
        
        // Info
        info: {
          moduleDescription: 'Site Management Module - Module for managing site information and declarations'
        },
        
        // Error messages
        error: {
          sessionExpired: 'Session has expired. Please login again.',
          loadModule: 'Cannot load Site Management module information',
          loadSites: 'Cannot load sites list: {{error}}',
          updateStatus: 'Cannot update status',
          deleteSite: 'Cannot delete site: {{error}}',
          updateSite: 'Cannot update site: {{error}}',
          saveSite: 'Cannot save site: {{error}}',
          checkSiteCode: 'Cannot check site code'
        },
        
        // Validation messages
        validation: {
          codeRequired: 'Please enter site code!',
          codeMinLength: 'Site code must be at least 3 characters!',
          codeMaxLength: 'Site code cannot exceed 20 characters!',
          codeFormat: 'Site code can only contain uppercase letters, numbers and underscores!',
          codeExists: 'Site code already exists!'
        },
        
        // Notifications
        notification: {
          switchToAddMode: 'Switched to add new site mode',
          updateStatusSuccess: 'Status updated successfully',
          deleteSuccess: 'Site "{name}" deleted successfully',
          updateSuccess: 'Site updated successfully',
          createSuccess: 'New site created successfully'
        },
        
        // Roles
        roles: {
          title: 'Roles List',
          searchPlaceholder: 'Search roles...',
          refreshData: 'Refresh data',
          addRole: 'Add Role',
          table: {
            id: 'ID',
            name: 'Role Name',
            description: 'Mô tả',
            status: 'Status',
            activated: 'On',
            deactivated: 'Off',
            edit: 'Edit',
            delete: 'Delete'
          },
          addModal: {
            title: 'Add New Role'
          },
          editModal: {
            title: 'Edit Role'
          },
          deleteModal: {
            title: 'Confirm Delete Role',
            confirmMessage: 'Are you sure you want to delete this role from the system?',
            roleInfoToDelete: 'Role information to be deleted',
            warningTitle: 'Important Notice',
            warningMessage: 'This action cannot be undone. All data related to this role will be permanently deleted from the system.',
            cancel: 'Cancel',
            confirmDelete: 'Confirm Delete'
          },
          form: {
            name: 'Role Name',
            nameRequired: 'Please enter role name',
            nameExists: 'Role name already exists',
            nameAvailable: 'Role name is available',
            nameMinLength: 'Role name must be at least 2 characters',
            nameMaxLength: 'Role name cannot exceed 100 characters',
            namePlaceholder: 'Enter role name',
            description: 'Description',
            descriptionPlaceholder: 'Enter description (optional)',
            status: 'Status',
            activated: 'Activated',
            deactivated: 'Deactivated',
            cancel: 'Cancel',
            saveAndNew: 'Save & New',
            saveAndClose: 'Save & Close',
            save: 'Save Changes'
          }
        },
        
        // User Roles
        userRoles: {
          title: 'User Roles List',
          searchPlaceholder: 'Search user roles...',
          refreshData: 'Refresh data',
          addNew: 'Add New',
          table: {
            userId: 'User ID',
            username: 'Username',
            fullName: 'Full Name',
            roleId: 'Role ID',
            roleName: 'Role Name',
            delete: 'Delete'
          },
          addModal: {
            title: 'Add New User Role'
          },
          deleteModal: {
            title: 'Confirm Delete User Role',
            confirmMessage: 'Are you sure you want to delete this user role from the system?',
            userRoleInfoToDelete: 'User Role information to be deleted',
            warningTitle: 'Important Notice',
            warningMessage: 'This action cannot be undone. User will lose permissions of this role after deletion.',
            cancel: 'Cancel',
            confirmDelete: 'Confirm Delete'
          },
          form: {
            user: 'User',
            userRequired: 'Please select User',
            userPlaceholder: 'Search and select User...',
            role: 'Role',
            roleRequired: 'Please select Role',
            rolePlaceholder: 'Search and select Role...',
            currentRolesTitle: 'Current User Roles',
            noRolesMessage: 'User "{{username}} - {{fullName}}" has no roles yet.',
            hasRolesMessage: 'currently has {{count}} roles',
            cancel: 'Cancel',
            saveAndNew: 'Save & New',
            saveAndClose: 'Save & Close'
          }
        }
      },
      // User Management Page
      'user.menu.overview': 'Overview',
      'user.menu.users': 'Users',
      'user.menu.roles': 'Roles',
      'user.menu.userRoles': 'User Roles',
      
      // Tab và Breadcrumb
      'user.tab.newTab.title': 'New Tab',
      'user.tab.newTab.description': 'You are viewing User Management Module in a new tab. This tab is independent of AppsPage and can be closed separately.',
      'user.breadcrumb.apps': 'Apps',
      'user.breadcrumb.module': 'User Management Module ',
      'user.breadcrumb.overview': 'Overview',
      'user.breadcrumb.users': 'Users',
      'user.breadcrumb.roles': 'Roles',
      'user.breadcrumb.userRoles': 'User Roles',
      
      // Activity
      'user.activity.time5min': '5 minutes ago',
      'user.activity.time15min': '15 minutes ago',
      'user.activity.time1hour': '1 hour ago',
      'user.activity.time2hour': '2 hours ago',
      'user.activity.newUserCreated': 'New user "john.doe" has been created',
      'user.activity.roleUpdated': 'Role "Manager" permissions have been updated',
      'user.activity.adminLogin': 'User "admin" logged in from new IP',
      'user.activity.moduleInstalled': 'User Management module has been installed successfully',
      
      // Module
      'user.module.summary': 'User Management Module',
      'user.module.description': 'Module for managing users and roles with detailed permissions in Metis Platform system',
      
      // Overview
      'user.overview.version': 'Version',
      'user.overview.author': 'Author',
      'user.overview.status': 'Status',
      'user.overview.installed': 'Installed',
      'user.overview.notInstalled': 'Not Installed',
      'user.overview.coreModule': 'Core Module',
      'user.overview.autoInstall': 'Auto Install',
      'user.overview.userStats': 'User Statistics',
      'user.overview.totalUsers': 'Total Users',
      'user.overview.activeUsers': 'Active Users',
      'user.overview.roles': 'Roles',
      'user.overview.permissions': 'Permissions',
      'user.overview.recentActivities': 'Recent Activities',
      
      // Users
      'user.users.title': 'User Management',
      'user.users.searchPlaceholder': 'Search users...',
      'user.users.refreshData': 'Refresh data',
      'user.users.addUser': 'Add User',
      'user.users.pagination': '{{start}}-{{end}} of {{total}} users',
      'user.users.editUser': 'Edit User',
      'user.users.addNewUser': 'Add New User',
      'user.users.table.username': 'Username',
      'user.users.table.fullName': 'Full Name',
      'user.users.table.email': 'Email',
      'user.users.table.phoneNumber': 'Phone Number',
      'user.users.table.status': 'Status',
      'user.users.table.active': 'Active',
      'user.users.table.inactive': 'Inactive',
      'user.users.table.actions': 'Actions',
      'user.users.table.edit': 'Edit',
      'user.users.table.delete': 'Delete',
      'user.users.deleteModal.title': 'Confirm Delete User',
      'user.users.deleteModal.confirmMessage': 'Are you sure you want to delete this user from the system?',
      'user.users.deleteModal.userInfoToDelete': 'User information to be deleted',
      'user.users.deleteModal.warningTitle': 'Important Notice',
      'user.users.deleteModal.warningMessage': 'This action cannot be undone. All data related to this user will be permanently deleted from the system.',
      'user.users.deleteModal.cancel': 'Cancel',
      'user.users.deleteModal.confirmDelete': 'Confirm Delete',
      'user.users.form.username': 'Username',
      'user.users.form.usernameRequired': 'Please enter username!',
      'user.users.form.usernameMinLength': 'Username must be at least 3 characters!',
      'user.users.form.usernameMaxLength': 'Username cannot exceed 50 characters!',
      'user.users.form.usernameFormat': 'Username can only contain letters, numbers and underscores!',
      'user.users.form.usernamePlaceholder': 'Enter username',
      'user.users.form.usernameExists': 'Username already exists',
      'user.users.form.usernameAvailable': 'Username is available',
      'user.users.form.fullName': 'Full Name',
      'user.users.form.fullNameRequired': 'Please enter full name!',
      'user.users.form.fullNameMinLength': 'Full name must be at least 2 characters!',
      'user.users.form.fullNameMaxLength': 'Full name cannot exceed 100 characters!',
      'user.users.form.fullNamePlaceholder': 'Enter full name',
      'user.users.form.email': 'Email',
      'user.users.form.emailRequired': 'Email is required!',
      'user.users.form.emailInvalid': 'Invalid email!',
      'user.users.form.emailMaxLength': 'Email cannot exceed 150 characters!',
      'user.users.form.emailPlaceholder': 'Enter email',
      'user.users.form.emailExists': 'Email already exists',
      'user.users.form.emailAvailable': 'Email is available',
      'user.users.form.phoneNumber': 'Phone Number',
      'user.users.form.phoneNumberInvalid': 'Invalid phone number!',
      'user.users.form.phoneNumberMaxLength': 'Phone number cannot exceed 20 characters!',
      'user.users.form.phoneNumberPlaceholder': 'Enter phone number',
      'user.users.form.password': 'Password',
      'user.users.form.passwordRequired': 'Please enter password!',
      'user.users.form.passwordMinLength': 'Password must be at least 8 characters!',
      'user.users.form.passwordFormat': 'Password must contain at least 1 uppercase, 1 lowercase, 1 number and 1 special character!',
      'user.users.form.passwordPlaceholder': 'Enter password',
      'user.users.form.confirmPassword': 'Confirm Password',
      'user.users.form.confirmPasswordRequired': 'Please confirm password!',
      'user.users.form.confirmPasswordMismatch': 'Password confirmation does not match!',
      'user.users.form.confirmPasswordPlaceholder': 'Confirm password',
      'user.users.form.status': 'Status',
      'user.users.form.activated': 'Activated',
      'user.users.form.deactivated': 'Deactivated',
      'user.users.form.cancel': 'Cancel',
      'user.users.form.saveAndNew': 'Save & New',
      'user.users.form.update': 'Update',
      'user.users.form.saveAndClose': 'Save & Close',
      'user.users.form.checking': 'Checking...',
      
      // Roles
      'user.roles.title': 'Roles List',
      'user.roles.searchPlaceholder': 'Search roles...',
      'user.roles.refreshData': 'Refresh data',
      'user.roles.addRole': 'Add Role',
      'user.roles.table.id': 'ID',
      'user.roles.table.name': 'Role Name',
      'user.roles.table.description': 'Description',
      'user.roles.table.status': 'Status',
      'user.roles.table.activated': 'On',
      'user.roles.table.deactivated': 'Off',
      'user.roles.table.edit': 'Edit',
      'user.roles.table.delete': 'Delete',
      'user.roles.addModal.title': 'Add New Role',
      'user.roles.editModal.title': 'Edit Role',
      'user.roles.deleteModal.title': 'Confirm Delete Role',
      'user.roles.deleteModal.confirmMessage': 'Are you sure you want to delete this role from the system?',
      'user.roles.deleteModal.roleInfoToDelete': 'Role information to be deleted',
      'user.roles.deleteModal.warningTitle': 'Important Notice',
      'user.roles.deleteModal.warningMessage': 'This action cannot be undone. All data related to this role will be permanently deleted from the system.',
      'user.roles.deleteModal.cancel': 'Cancel',
      'user.roles.deleteModal.confirmDelete': 'Confirm Delete',
      'user.roles.form.name': 'Role Name',
      'user.roles.form.nameRequired': 'Please enter role name',
      'user.roles.form.nameExists': 'Role name already exists',
      'user.roles.form.nameAvailable': 'Role name is available',
      'user.roles.form.nameMinLength': 'Role name must be at least 2 characters',
      'user.roles.form.nameMaxLength': 'Role name cannot exceed 100 characters',
      'user.roles.form.checking': 'Checking...',
      'user.roles.form.namePlaceholder': 'Enter role name',
      'user.roles.form.description': 'Description',
      'user.roles.form.descriptionPlaceholder': 'Enter description (optional)',
      'user.roles.form.status': 'Status',
      'user.roles.form.activated': 'Activated',
      'user.roles.form.deactivated': 'Deactivated',
      'user.roles.form.cancel': 'Cancel',
      'user.roles.form.saveAndNew': 'Save & New',
      'user.roles.form.saveAndClose': 'Save & Close',
      'user.roles.form.save': 'Save Changes',
      
      // User Roles
      'user.userRoles.title': 'User Roles List',
      'user.userRoles.searchPlaceholder': 'Search user roles...',
      'user.userRoles.refreshData': 'Refresh data',
      'user.userRoles.addNew': 'Add New',
      'user.userRoles.table.userId': 'User ID',
      'user.userRoles.table.username': 'Username',
      'user.userRoles.table.fullName': 'Full Name',
      'user.userRoles.table.roleId': 'Role ID',
      'user.userRoles.table.roleName': 'Role Name',
      'user.userRoles.table.delete': 'Delete',
      'user.userRoles.addModal.title': 'Add New User Role',
      'user.userRoles.deleteModal.title': 'Confirm Delete User Role',
      'user.userRoles.deleteModal.confirmMessage': 'Are you sure you want to delete this user role from the system?',
      'user.userRoles.deleteModal.userRoleInfoToDelete': 'User Role information to be deleted',
      'user.userRoles.deleteModal.warningTitle': 'Important Notice',
      'user.userRoles.deleteModal.warningMessage': 'This action cannot be undone. User will lose permissions of this role after deletion.',
      'user.userRoles.deleteModal.cancel': 'Cancel',
      'user.userRoles.deleteModal.confirmDelete': 'Confirm Delete',
      'user.userRoles.form.user': 'User',
      'user.userRoles.form.userRequired': 'Please select User',
      'user.userRoles.form.userPlaceholder': 'Search and select User...',
      'user.userRoles.form.role': 'Role',
      'user.userRoles.form.roleRequired': 'Please select Role',
      'user.userRoles.form.rolePlaceholder': 'Search and select Role...',
      'user.userRoles.form.currentRolesTitle': 'Current User Roles',
      'user.userRoles.form.noRolesMessage': 'User "{{username}} - {{fullName}}" has no roles yet.',
      'user.userRoles.form.hasRolesMessage': 'currently has {{count}} roles',
      'user.userRoles.form.cancel': 'Cancel',
      'user.userRoles.form.saveAndNew': 'Save & New',
      'user.userRoles.form.saveAndClose': 'Save & Close',
      
      // Info
      'user.info.moduleDescription': 'User Management Module - Manage users and roles',
      
      // Warning
      'user.warning.userRoleExists': '⚠️ User "{{username}} - {{fullName}}" already has role "{{roleName}}". Please select different User or Role.',
      
      // Error messages
      'user.error.loadModule': 'Cannot load User Management module information',
      'user.error.loadUsers': 'Cannot load users list',
      'user.error.loadRoles': 'Cannot load roles list',
      'user.error.loadUserRoles': 'Cannot load user roles list',
      'user.error.refreshData': 'Cannot refresh data',
      'user.error.updateUserStatus': 'Error occurred while updating user status',
      'user.error.deleteUser': 'Error occurred while deleting user',
      'user.error.saveUser': 'Cannot save user',
      'user.error.validationError': 'Invalid data. Please check your information.',
      'user.error.updateRoleStatus': 'Cannot update role status',
      'user.error.checkRoleName': 'Cannot check role name',
      'user.error.roleNameExists': 'Role name already exists!',
      'user.error.createRole': 'Cannot create new role',
      'user.error.updateRole': 'Cannot update role',
      'user.error.deleteRole': 'Cannot delete role',
      'user.error.refreshRoles': 'Cannot refresh roles list',
      'user.error.refreshUserRoles': 'Cannot refresh user roles list',
      'user.error.deleteUserRole': 'Cannot delete user role',
      'user.error.selectUserAndRole': 'Please select User and Role',
      'user.error.createUserRole': 'Cannot create user role',
      'user.error.userRoleExists': 'User "{{username}} - {{fullName}}" already has role "{{roleName}}".',
      
      // Notifications
      'user.notification.loadingData': 'Loading data...',
      'user.notification.dataRefreshed': 'Data has been refreshed successfully',
      'user.notification.userStatusUpdated': 'User has been {{action}} successfully',
      'user.notification.activated': 'activated',
      'user.notification.deactivated': 'deactivated',
      'user.notification.userDeleted': 'User has been deleted successfully',
      'user.notification.userUpdated': 'User has been updated successfully',
      'user.notification.userCreated': 'User has been created successfully',
      'user.notification.roleStatusUpdated': 'Role status has been updated',
      'user.notification.roleCreated': 'New role has been created successfully',
      'user.notification.roleUpdated': 'Role has been updated successfully',
      'user.notification.roleDeleted': 'Role has been deleted successfully',
      'user.notification.rolesRefreshed': 'Roles list has been refreshed',
      'user.notification.userRolesRefreshed': 'User roles list has been refreshed',
      'user.notification.userRoleDeleted': 'User role has been deleted successfully',
      'user.notification.userRoleCreated': 'User role has been created successfully',
      // Loading
      'user.loading.title': 'Loading...',
      'user.loading.module': 'User Management Module',
      // Header
      'user.header.openAppsPage': 'Open Apps Page',
      'user.header.closeTab': 'Close Tab'
    }
  }
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: localStorage.getItem('app_language') || 'vi',
    fallbackLng: 'vi',
    interpolation: {
      escapeValue: false
    },
    debug: true,
    react: {
      useSuspense: false
    }
  });

console.log('i18n initialized with language:', i18n.language);
console.log('Available languages:', Object.keys(resources));
console.log('Sample translation test:', i18n.t('user.menu.overview'));

export default i18n; 