.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-background {
  width: 100%;
  max-width: 450px;
  position: relative;
}

.login-content {
  width: 100%;
}

.login-card {
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: none;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 0 20px;
}

.logo-container {
  margin-bottom: 24px;
}

.logo-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.logo-icon .anticon {
  font-size: 36px;
  color: white;
}

.login-title {
  margin-bottom: 8px !important;
  color: #1a1a1a;
  font-weight: 600;
}

.login-subtitle {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.login-input {
  border-radius: 12px;
  border: 2px solid #f0f0f0;
  transition: all 0.3s ease;
  background: #fafafa;
}

.login-input:hover {
  border-color: #667eea;
  background: white;
}

.login-input:focus,
.login-input.ant-input-focused {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: white;
}

.login-input .ant-input-prefix {
  color: #999;
  margin-right: 8px;
}

.login-button {
  height: 48px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.login-button:active {
  transform: translateY(0);
}

.login-footer {
  text-align: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.footer-text {
  font-size: 12px;
  color: #999;
}

/* Responsive design */
@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }
  
  .login-card {
    border-radius: 16px;
  }
  
  .logo-icon {
    width: 60px;
    height: 60px;
  }
  
  .logo-icon .anticon {
    font-size: 28px;
  }
  
  .login-title {
    font-size: 24px !important;
  }
}

/* Animation for form elements */
.ant-form-item {
  animation: fadeInUp 0.6s ease-out;
}

.ant-form-item:nth-child(1) {
  animation-delay: 0.1s;
}

.ant-form-item:nth-child(2) {
  animation-delay: 0.2s;
}

.ant-form-item:nth-child(3) {
  animation-delay: 0.3s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading state styles */
.login-button.ant-btn-loading {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0.8;
}

/* Error state styles */
.ant-form-item-has-error .login-input {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 3px rgba(255, 77, 79, 0.1);
}

.ant-form-item-has-error .login-input:focus {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 3px rgba(255, 77, 79, 0.1);
} 