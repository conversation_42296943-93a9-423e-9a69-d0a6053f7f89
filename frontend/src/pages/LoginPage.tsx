import React, { useState } from 'react';
import { Form, Input, <PERSON><PERSON>, Card, Typography, App } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import './LoginPage.css';

const { Title, Text } = Typography;

interface LoginForm {
  username: string;
  password: string;
}

interface LoginResponse {
  success: boolean;
  message: string;
  user?: {
    id: string;
    username: string;
    full_name: string;
    email: string;
    phone_number?: string;
    is_active: boolean;
  };
  token?: string;
}

const LoginPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { login, isAuthenticated } = useAuth();
  const { message } = App.useApp();

  // Nếu đã đăng nhập thì chuyển đến AppsPage
  if (isAuthenticated) {
    navigate('/apps');
    return null;
  }

  const onFinish = async (values: LoginForm) => {
    setLoading(true);
    
    try {
      const response = await fetch('/api/user-management/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      const data: LoginResponse = await response.json();

      if (data.success && data.user && data.token) {
        // Sử dụng AuthContext để đăng nhập
        login(data.user, data.token);
        message.success(data.message);
        navigate('/apps'); // Chuyển đến AppsPage
      } else {
        message.error(data.message);
      }
    } catch (error) {
      console.error('Login error:', error);
      message.error('Có lỗi xảy ra khi đăng nhập. Vui lòng thử lại.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="login-content">
          <Card className="login-card">
            <div className="login-header">
              <div className="logo-container">
                <div className="logo-icon">
                  <UserOutlined />
                </div>
              </div>
              <Title level={2} className="login-title">
                Đăng nhập
              </Title>
              <Text type="secondary" className="login-subtitle">
                Vui lòng nhập thông tin đăng nhập của bạn
              </Text>
            </div>

            <Form
              name="login"
              onFinish={onFinish}
              autoComplete="off"
              layout="vertical"
              size="large"
            >
              <Form.Item
                name="username"
                rules={[
                  { required: true, message: 'Vui lòng nhập tên đăng nhập!' },
                  { min: 3, message: 'Tên đăng nhập phải có ít nhất 3 ký tự!' }
                ]}
              >
                <Input
                  prefix={<UserOutlined className="site-form-item-icon" />}
                  placeholder="Tên đăng nhập"
                  className="login-input"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  { required: true, message: 'Vui lòng nhập mật khẩu!' },
                  { min: 6, message: 'Mật khẩu phải có ít nhất 6 ký tự!' }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined className="site-form-item-icon" />}
                  placeholder="Mật khẩu"
                  className="login-input"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={<LoginOutlined />}
                  className="login-button"
                  block
                >
                  Đăng nhập
                </Button>
              </Form.Item>
            </Form>

            <div className="login-footer">
              <Text type="secondary" className="footer-text">
                Hệ thống quản lý Metis AI
              </Text>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default LoginPage; 