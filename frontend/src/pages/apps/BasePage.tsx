import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Col,
  Card,
  Button,
  Typography,
  Layout,
  Space,
  Avatar,
  Breadcrumb,
  Menu,
  Badge,
  Spin,
  message,
  Tag,
  Statistic,
  Alert
} from 'antd';
import {
  ArrowLeftOutlined,
  InfoCircleOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DatabaseOutlined,
  ApiOutlined,
  UserOutlined,
  SafetyOutlined,
  SettingOutlined,
  ClockCircleOutlined,
  AppstoreOutlined,
  CloseOutlined,
  DashboardOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import { logError } from '../../utils/errorHandler';
import './BasePage.css';

const { Header, Content, Sider } = Layout;
const { Title, Text, Paragraph } = Typography;

interface BaseModuleDetail {
  name: string;
  summary: string;
  version: string;
  state: 'installed' | 'uninstalled';
  author?: string;
  category?: string;
  description?: string;
  is_core_module?: boolean;
  auto_install?: boolean;
  hidden?: boolean;
  is_installed?: boolean;
}

interface SystemStats {
  tables: number;
  endpoints: number;
  users: number;
  permissions: number;
}

interface ActivityItem {
  time: string;
  description: string;
  type: 'info' | 'success' | 'warning' | 'error';
}

const BasePage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [collapsed, setCollapsed] = useState(true);
  const [selectedKey, setSelectedKey] = useState('overview');
  const [moduleDetail, setModuleDetail] = useState<BaseModuleDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [systemStats] = useState<SystemStats>({
    tables: 15,
    endpoints: 8,
    users: 3,
    permissions: 5
  });
  const [recentActivities] = useState<ActivityItem[]>([
    {
      time: t('base.activity.time2min'),
      description: t('base.activity.adminUpdateConfig'),
      type: 'info'
    },
    {
      time: t('base.activity.time15min'),
      description: t('base.activity.systemCreateTable'),
      type: 'success'
    },
    {
      time: t('base.activity.time1hour'),
      description: t('base.activity.baseModuleInstalled'),
      type: 'success'
    },
    {
      time: t('base.activity.time2hour'),
      description: t('base.activity.initDatabase'),
      type: 'info'
    }
  ]);

  // Kiểm tra xem có phải tab mới không
  const isNewTab = window.opener || window.history.length <= 1;

  // Kiểm tra xem có phải mobile không
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  // Menu items
  const menuItems = [
    {
      key: 'overview',
      icon: <DashboardOutlined />,
      label: t('base.menu.overview'),
    }
  ];

  // Fetch module details
  const fetchModuleDetails = async () => {
    try {
      console.log('DEBUG: Fetching base module details...');
      const response = await axios.get('http://localhost:8000/api/internal/modules/base', {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      console.log('DEBUG: Base module details received:', response.data);
      
      const moduleData = response.data;
      setModuleDetail({
        name: moduleData.name || 'base',
        summary: moduleData.summary || t('base.module.summary'),
        version: moduleData.version || '1.0.0',
        state: moduleData.is_installed ? 'installed' : 'uninstalled',
        author: moduleData.author || 'Metis AI',
        category: moduleData.category || 'Core',
        description: moduleData.description || t('base.module.description'),
        is_core_module: moduleData.is_core_module || true,
        auto_install: moduleData.auto_install || true,
        hidden: moduleData.hidden || false,
        is_installed: moduleData.is_installed || true
      });
    } catch (error: any) {
      logError('fetchModuleDetails', error);
      message.error(t('base.error.loadModule'));
      
      // Set default data if API fails
      setModuleDetail({
        name: 'base',
        summary: t('base.module.summary'),
        version: '1.0.0',
        state: 'installed',
        author: 'Metis AI',
        category: 'Core',
        description: t('base.module.description'),
        is_core_module: true,
        auto_install: true,
        hidden: false,
        is_installed: true
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchModuleDetails();
    
    // Cập nhật title của tab
    document.title = 'Base Module - Metis Platform';
    
    // Thêm event listener để refresh data khi tab được focus
    const handleFocus = () => {
      console.log('DEBUG: Tab focused, refreshing data...');
      fetchModuleDetails();
    };
    
    // Thêm event listener để xử lý trước khi đóng tab
    const handleBeforeUnload = () => {
      // Có thể thêm logic để lưu trạng thái nếu cần
      console.log('DEBUG: Tab is being closed...');
    };
    
    // Xử lý resize window
    const handleResize = () => {
      const newIsMobile = window.innerWidth <= 768;
      setIsMobile(newIsMobile);
      
      // Luôn giữ collapsed = true cho cả mobile và desktop
      setCollapsed(true);
    };
    
    window.addEventListener('focus', handleFocus);
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const handleMenuClick = (e: any) => {
    setSelectedKey(e.key);
    // Trên mobile, đóng sidebar sau khi click menu
    if (isMobile) {
      setCollapsed(true);
    }
  };

  // Hàm xử lý navigation back
  const handleBackNavigation = () => {
    if (window.opener) {
      // Nếu mở từ tab khác, đóng tab hiện tại
      window.close();
    } else {
      // Nếu không có opener, navigate về AppsPage
      navigate('/apps');
    }
  };

  // Hàm mở AppsPage trong tab mới
  const handleOpenAppsPage = () => {
    window.open('/apps', '_blank');
  };

  // Hàm toggle sidebar
  const handleToggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  // Hàm đóng sidebar khi click overlay
  const handleOverlayClick = () => {
    if (!collapsed) {
      setCollapsed(true);
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <ClockCircleOutlined style={{ color: '#52c41a' }} />;
      case 'warning':
        return <ClockCircleOutlined style={{ color: '#faad14' }} />;
      case 'error':
        return <ClockCircleOutlined style={{ color: '#f5222d' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#1890ff' }} />;
    }
  };

  const renderContent = () => {
    return (
      <div>
        {/* Module Overview Card */}
        <Card className="base-overview-card" style={{ marginBottom: '24px' }}>
          <Row gutter={[24, 16]}>
            <Col xs={24} md={8}>
              <div style={{ textAlign: 'center' }}>
                <Avatar 
                  size={80} 
                  icon={<SettingOutlined />} 
                  className="base-overview-avatar"
                  style={{ 
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    fontSize: '32px'
                  }}
                />
                <Title level={3} style={{ margin: '16px 0 8px 0' }}>
                  {moduleDetail?.summary}
                </Title>
                <Text type="secondary" style={{ fontSize: '14px', fontFamily: 'monospace' }}>
                  {moduleDetail?.name}
                </Text>
              </div>
            </Col>
            <Col xs={24} md={16}>
              <div className="base-overview-stats">
                <Space size="large" wrap>
                  <div>
                    <Text type="secondary">{t('base.overview.version')}</Text>
                    <div><Text strong>v{moduleDetail?.version}</Text></div>
                  </div>
                  <div>
                    <Text type="secondary">{t('base.overview.author')}</Text>
                    <div><Text strong>{moduleDetail?.author}</Text></div>
                  </div>
                  <div>
                    <Text type="secondary">{t('base.overview.status')}</Text>
                    <div>
                      <Tag color={moduleDetail?.state === 'installed' ? 'green' : 'orange'}>
                        {moduleDetail?.state === 'installed' ? t('base.overview.installed') : t('base.overview.notInstalled')}
                      </Tag>
                    </div>
                  </div>
                </Space>
              </div>
              <div className="base-overview-tags" style={{ marginTop: '16px' }}>
                <Space wrap>
                  <Tag color="blue">{moduleDetail?.category}</Tag>
                  {moduleDetail?.is_core_module && (
                    <Tag color="red">{t('base.overview.coreModule')}</Tag>
                  )}
                  {moduleDetail?.auto_install && (
                    <Tag color="green">{t('base.overview.autoInstall')}</Tag>
                  )}
                </Space>
              </div>
              <Paragraph className="base-overview-description" style={{ marginTop: '16px' }}>
                {moduleDetail?.description}
              </Paragraph>
            </Col>
          </Row>
        </Card>

        {/* Statistics and Activities */}
        <Row gutter={[24, 24]}>
          <Col xs={24} md={12}>
            <Card title={t('base.stats.title')} size="small" className="base-card-responsive">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="base-stats-card">
                    <Statistic
                      title={t('base.stats.tables')}
                      value={systemStats.tables}
                      valueStyle={{ color: '#1890ff', fontSize: '24px' }}
                      prefix={<DatabaseOutlined />}
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="base-stats-card">
                    <Statistic
                      title={t('base.stats.endpoints')}
                      value={systemStats.endpoints}
                      valueStyle={{ color: '#52c41a', fontSize: '24px' }}
                      prefix={<ApiOutlined />}
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="base-stats-card">
                    <Statistic
                      title={t('base.stats.users')}
                      value={systemStats.users}
                      valueStyle={{ color: '#faad14', fontSize: '24px' }}
                      prefix={<UserOutlined />}
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="base-stats-card">
                    <Statistic
                      title={t('base.stats.permissions')}
                      value={systemStats.permissions}
                      valueStyle={{ color: '#722ed1', fontSize: '24px' }}
                      prefix={<SafetyOutlined />}
                    />
                  </div>
                </Col>
              </Row>
            </Card>
          </Col>
          <Col xs={24} md={12}>
            <Card title={t('base.activity.title')} size="small" className="base-card-responsive">
              <div className="base-activity-card">
                {recentActivities.map((activity, index) => (
                  <div key={index} className="base-activity-item">
                    <div style={{ display: 'flex', alignItems: 'flex-start', gap: '8px' }}>
                      {getActivityIcon(activity.type)}
                      <div style={{ flex: 1 }}>
                        <Text className="base-activity-time">{activity.time}</Text>
                        <div style={{ marginTop: '4px' }}>{activity.description}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  if (loading) {
    return (
      <Layout style={{ minHeight: '100vh' }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }}>
          <div style={{ textAlign: 'center', color: 'white' }}>
            <Spin size="large" style={{ marginBottom: '16px' }} />
            <div style={{ fontSize: '24px', marginBottom: '16px' }}>{t('base.loading.title')}</div>
            <div>{t('base.module.title')}</div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout className="base-page-layout">
      {/* Header */}
      <Header className="base-page-header">
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          height: '100%'
        }}>
          <Space size="large">
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              {/* Icon menu cho cả mobile và desktop */}
              <Button
                type="text"
                icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                onClick={handleToggleSidebar}
                style={{ 
                  color: 'white',
                  fontSize: '16px',
                  width: '32px',
                  height: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              />
              <Avatar 
                size={32} 
                icon={<SettingOutlined />} 
                style={{ 
                  background: 'rgba(255,255,255,0.2)',
                  border: '1px solid rgba(255,255,255,0.3)',
                  fontSize: '16px'
                }}
              />
              <div>
                <Title level={5} className="base-title-responsive" style={{ color: 'white', margin: 0, fontSize: '16px' }}>
                  {t('base.module.title')}
                </Title>
              </div>
            </div>
          </Space>
          
          <Space>
            <Badge count={1} size="small">
              <Button 
                type="text" 
                icon={<InfoCircleOutlined />} 
                style={{ color: 'white' }}
                onClick={() => message.info(t('base.notification.coreModuleInfo'))}
              />
            </Badge>
            {isNewTab && (
              <>
                <Button 
                  type="text" 
                  icon={<AppstoreOutlined />} 
                  style={{ color: 'white' }}
                  onClick={handleOpenAppsPage}
                  title={t('base.header.openAppsPage')}
                />
                <Button 
                  type="text" 
                  icon={<CloseOutlined />} 
                  style={{ color: 'white' }}
                  onClick={() => window.close()}
                  title={t('base.header.closeTab')}
                />
              </>
            )}
          </Space>
        </div>
      </Header>

      <Layout>
        {/* Sidebar Navigation */}
        <Sider 
          trigger={null} 
          collapsible 
          collapsed={collapsed}
          className={`base-page-sider ${collapsed ? 'collapsed' : ''}`}
          width={280}
          collapsedWidth={80}
          onClick={handleOverlayClick}
        >
          <Menu
            mode="inline"
            selectedKeys={[selectedKey]}
            items={menuItems}
            onClick={handleMenuClick}
            className="base-nav-menu"
          />
        </Sider>

        {/* Main Content */}
        <Layout className="base-page-content">
          <Content className="base-page-main-content" style={{ 
            margin: '24px',
            padding: '24px'
          }}>
            {/* Thông báo tab mới */}
            {isNewTab && (
              <Alert
                message={t('base.alert.newTab.title')}
                description={t('base.alert.newTab.description')}
                type="info"
                showIcon
                closable
                style={{ marginBottom: '16px' }}
              />
            )}
            
            <Breadcrumb className="base-breadcrumb" style={{ marginBottom: '24px' }}>
              <Breadcrumb.Item>
                <Button 
                  type="link" 
                  onClick={handleOpenAppsPage}
                  style={{ padding: 0, height: 'auto', lineHeight: '1.5' }}
                >
                  {t('base.breadcrumb.apps')}
                </Button>
              </Breadcrumb.Item>
              <Breadcrumb.Item>{t('base.module.title')}</Breadcrumb.Item>
              <Breadcrumb.Item>{t('base.menu.overview')}</Breadcrumb.Item>
            </Breadcrumb>
            
            {renderContent()}
          </Content>
        </Layout>
      </Layout>
    </Layout>
  );
};

export default BasePage; 