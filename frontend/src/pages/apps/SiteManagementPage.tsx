import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Col,
  Card,
  Button,
  Typography,
  Layout,
  Space,
  Avatar,
  Breadcrumb,
  Menu,
  Badge,
  Spin,
  message,
  Tag,
  Statistic,
  Table,
  Switch,
  Tooltip,
  Input,
  Form,
  Select,
  Modal
} from 'antd';
import {
  InfoCircleOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DatabaseOutlined,
  ApiOutlined,
  UserOutlined,
  SafetyOutlined,
  SettingOutlined,
  ClockCircleOutlined,
  AppstoreOutlined,
  CloseOutlined,
  UnorderedListOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  PlusOutlined,
  SaveOutlined,
  FormOutlined,
  DashboardOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import { logError, getErrorMessage } from '../../utils/errorHandler';
import SiteParentDropdown from '../../components/SiteParentDropdown';
import { useAuth } from '../../contexts/AuthContext';
import './SiteManagementPage.css';

const { Header, Content, Sider } = Layout;
const { Title, Text, Paragraph } = Typography;

interface SiteManagementModuleDetail {
  name: string;
  summary: string;
  version: string;
  state: 'installed' | 'uninstalled';
  author?: string;
  category?: string;
  description?: string;
  is_core_module?: boolean;
  auto_install?: boolean;
  hidden?: boolean;
  is_installed?: boolean;
}

interface SystemStats {
  tables: number;
  endpoints: number;
  users: number;
  permissions: number;
}

interface ActivityItem {
  time: string;
  description: string;
  type: 'info' | 'success' | 'warning' | 'error';
}

interface Site {
  id: number;
  code: string;
  name: string;
  descriptions?: string;
  is_activated: boolean;
  parent_id?: number;
  children?: Site[];
}

const SiteManagementPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { isAuthenticated, logout, loading: authLoading } = useAuth();
  const [collapsed, setCollapsed] = useState(true);
  const [selectedKey, setSelectedKey] = useState('overview');
  const [moduleDetail, setModuleDetail] = useState<SiteManagementModuleDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [systemStats] = useState<SystemStats>({
    tables: 15,
    endpoints: 8,
    users: 3,
    permissions: 5
  });
  const [recentActivities] = useState<ActivityItem[]>([
    {
      time: t('site.activity.time2min'),
      description: t('site.activity.adminUpdateConfig'),
      type: 'info'
    },
    {
      time: t('site.activity.time15min'),
      description: t('site.activity.systemCreateTable'),
      type: 'success'
    },
    {
      time: t('site.activity.time1hour'),
      description: t('site.activity.siteManagementInstalled'),
      type: 'success'
    },
    {
      time: t('site.activity.time2hour'),
      description: t('site.activity.initDatabase'),
      type: 'info'
    }
  ]);
  const [sites, setSites] = useState<Site[]>([]);
  const [sitesLoading, setSitesLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [filteredSites, setFilteredSites] = useState<Site[]>([]);
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);
  
  // Form states
  const [form] = Form.useForm();
  const [isEditing, setIsEditing] = useState(false);
  const [editingSiteId, setEditingSiteId] = useState<number | null>(null);
  const [formLoading, setFormLoading] = useState(false);

  // Edit modal states
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editForm] = Form.useForm();
  const [editFormLoading, setEditFormLoading] = useState(false);
  const [editingSite, setEditingSite] = useState<Site | null>(null);

  // Delete confirmation modal states
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteSiteId, setDeleteSiteId] = useState<number | null>(null);
  const [deleteSiteName, setDeleteSiteName] = useState<string>('');
  const [deleteLoading, setDeleteLoading] = useState(false);

  // Warning modal states for sites with children
  const [warningModalVisible, setWarningModalVisible] = useState(false);
  const [warningSiteName, setWarningSiteName] = useState<string>('');
  const [warningChildrenCount, setWarningChildrenCount] = useState<number>(0);

  // Kiểm tra xem có phải tab mới không
  const isNewTab = window.opener || window.history.length <= 1;

  // Kiểm tra xem có phải mobile không
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  // Menu items
  const menuItems = [
    {
      key: 'overview',
      icon: <DashboardOutlined />,
      label: t('site.menu.overview'),
    },
    {
      key: 'list',
      icon: <UnorderedListOutlined />,
      label: t('site.menu.list'),
    },
    {
      key: 'add',
      icon: <FormOutlined />,
      label: t('site.menu.add'),
    }
  ];

  const checkAuth = async () => {
    if (authLoading) {
      return null;
    }
    if (!isAuthenticated) {
      await logout();
      message.error('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.');
      navigate('/login');
      return false;
    }
    return true;
  };

  useEffect(() => {
    if (authLoading) return; 
    if (!isAuthenticated) {
      logout().then(() => {
        message.error('Phiên đăng nhập không hợp lệ. Vui lòng đăng nhập lại.');
        navigate('/login');
      });
    }
  }, [authLoading, isAuthenticated, navigate, logout]);

  // Fetch module details
  const fetchModuleDetails = async () => {
    if (!checkAuth()) return;
    try {
      const response = await axios.get('http://localhost:8000/api/internal/modules/site_management', {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      const moduleData = response.data;
      setModuleDetail({
        name: moduleData.name || 'site_management',
        summary: moduleData.summary || t('site.module.summary'),
        version: moduleData.version || '1.0',
        state: moduleData.is_installed ? 'installed' : 'uninstalled',
        author: moduleData.author || 'Metis AI',
        category: moduleData.category || 'Core',
        description: moduleData.description || t('site.module.description'),
        is_core_module: moduleData.is_core_module || true,
        auto_install: moduleData.auto_install || true,
        hidden: moduleData.hidden || false,
        is_installed: moduleData.is_installed || true
      });
    } catch (error: any) {
      logError('fetchModuleDetails', error);
      message.error(t('site.error.loadModule'));
      
      // Set default data if API fails
      setModuleDetail({
        name: 'site_management',
        summary: t('site.module.summary'),
        version: '1.0',
        state: 'installed',
        author: 'Metis AI',
        category: 'Core',
        description: t('site.module.description'),
        is_core_module: true,
        auto_install: true,
        hidden: false,
        is_installed: true
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch sites data
  const fetchSites = async () => {
    if (!checkAuth()) return;
    
    try {
      setSitesLoading(true);
      const response = await axios.get('http://localhost:8000/api/site-management/sites', {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      const sitesData = response.data;
      
      // Chuyển đổi dữ liệu phẳng thành cấu trúc cây
      const sitesMap = new Map<number, Site>();
      const rootSites: Site[] = [];

      // Tạo map cho tất cả sites
      sitesData.forEach((site: Site) => {
        sitesMap.set(site.id, { ...site, children: [] });
      });

      // Xây dựng cấu trúc cây
      sitesData.forEach((site: Site) => {
        const siteWithChildren = sitesMap.get(site.id)!;
        if (site.parent_id) {
          const parent = sitesMap.get(site.parent_id);
          if (parent) {
            parent.children!.push(siteWithChildren);
          }
        } else {
          rootSites.push(siteWithChildren);
        }
      });

      console.log('DEBUG: Tree structure built:', rootSites);
      setSites(rootSites);
      setFilteredSites(rootSites);
      
      // Tự động mở rộng tất cả các nhánh mặc định
      const allSiteIds = getAllSiteIds(rootSites);
      setExpandedRowKeys(allSiteIds);
    } catch (error: any) {
      logError('fetchSites', error);
      const errorMessage = getErrorMessage(error);
      message.error(t('site.error.loadSites', { error: errorMessage }));
      setSites([]);
      setFilteredSites([]);
      setExpandedRowKeys([]);
    } finally {
      setSitesLoading(false);
    }
  };

  // Hàm lấy tất cả ID của sites (bao gồm cả children)
  const getAllSiteIds = (sites: Site[]): React.Key[] => {
    const ids: React.Key[] = [];
    const collectIds = (siteList: Site[]) => {
      siteList.forEach(site => {
        ids.push(site.id);
        if (site.children && site.children.length > 0) {
          collectIds(site.children);
        }
      });
    };
    collectIds(sites);
    return ids;
  };

  // Hàm kiểm tra Site có con hay không
  const hasChildren = (siteId: number, sitesList: Site[]): { hasChildren: boolean; childrenCount: number } => {
    let childrenCount = 0;
    
    const checkChildren = (siteList: Site[]) => {
      siteList.forEach(site => {
        if (site.id === siteId) {
          childrenCount = site.children ? site.children.length : 0;
          return;
        }
        if (site.children && site.children.length > 0) {
          checkChildren(site.children);
        }
      });
    };
    
    checkChildren(sitesList);
    return { hasChildren: childrenCount > 0, childrenCount };
  };

  // Filter sites based on search text
  const filterSites = (sites: Site[], searchText: string): Site[] => {
    if (!searchText.trim()) {
      return sites;
    }

    const searchLower = searchText.toLowerCase();
    
    const filterSite = (site: Site): boolean => {
      const matchesSearch = 
        site.code.toLowerCase().includes(searchLower) ||
        site.name.toLowerCase().includes(searchLower) ||
        (site.descriptions && site.descriptions.toLowerCase().includes(searchLower));
      
      // Check if any children match
      const childrenMatch = site.children && site.children.some(child => filterSite(child));
      
      return matchesSearch || !!childrenMatch;
    };

    const filterSiteWithChildren = (site: Site): Site | null => {
      const matchesSearch = 
        site.code.toLowerCase().includes(searchLower) ||
        site.name.toLowerCase().includes(searchLower) ||
        (site.descriptions && site.descriptions.toLowerCase().includes(searchLower));
      
      // Filter children recursively
      const filteredChildren = site.children 
        ? site.children.map(child => filterSiteWithChildren(child)).filter(Boolean) as Site[]
        : [];
      
      // Include site if it matches or has matching children
      if (matchesSearch || filteredChildren.length > 0) {
        return {
          ...site,
          children: filteredChildren
        };
      }
      
      return null;
    };

    const filtered = sites.map(site => filterSiteWithChildren(site)).filter(Boolean) as Site[];
    
    // Cập nhật expandedRowKeys khi filter
    const allFilteredIds = getAllSiteIds(filtered);
    setExpandedRowKeys(allFilteredIds);
    
    return filtered;
  };

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchText(value);
    const filtered = filterSites(sites, value);
    setFilteredSites(filtered);
  };

  // Handle add new site
  const handleAddNewSite = () => {
    // Reset form state
    setIsEditing(false);
    setEditingSiteId(null);
    
    // Reset form fields
    form.resetFields();
    form.setFieldsValue({
      is_activated: true,
      parent_id: undefined
    });
    
    // Chuyển sang tab form
    setSelectedKey('add');
    
    // Hiển thị thông báo
    message.info(t('site.notification.switchToAddMode'));
  };

  // Toggle site activation
  const handleToggleActivation = async (siteId: number, currentStatus: boolean) => {
    if (!checkAuth()) return;

    try {
      await axios.put(`http://localhost:8000/api/site-management/sites/${siteId}`, {
        is_activated: !currentStatus
      }, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      message.success(t('site.notification.updateStatusSuccess'));
      fetchSites(); // Refresh data
    } catch (error: any) {
      logError('handleToggleActivation', error);
      message.error(t('site.error.updateStatus'));
    }
  };

  // Handle edit site
  const handleEditSite = (site: Site) => {
    if (!checkAuth()) return;
    setEditingSite(site);
    editForm.setFieldsValue({
      code: site.code,
      name: site.name,
      descriptions: site.descriptions,
      is_activated: site.is_activated,
      parent_id: site.parent_id
    });
    setEditModalVisible(true);
  };

  // Handle delete site
  const handleDeleteSite = async (siteId: number, siteName: string) => {
    if (!checkAuth()) return;

    // Kiểm tra xem site có con không
    const { hasChildren: hasChildrenResult, childrenCount } = hasChildren(siteId, sites);
    
    if (hasChildrenResult) {
      // Hiển thị cảnh báo nếu site có con
      setWarningSiteName(siteName);
      setWarningChildrenCount(childrenCount);
      setWarningModalVisible(true);
      return;
    }

    // Nếu không có con, hiển thị modal xác nhận xóa
    setDeleteSiteId(siteId);
    setDeleteSiteName(siteName);
    setDeleteModalVisible(true);
  };

  // Handle confirm delete
  const handleConfirmDelete = async () => {
    if (!checkAuth() || !deleteSiteId) return;

    try {
      setDeleteLoading(true);
      await axios.delete(`http://localhost:8000/api/site-management/sites/${deleteSiteId}`, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      message.success(t('site.notification.deleteSuccess', { name: deleteSiteName }));
      fetchSites(); // Refresh data
    } catch (error: any) {
      logError('handleConfirmDelete', error);
      const errorMessage = getErrorMessage(error);
      message.error(t('site.error.deleteSite', { error: errorMessage }));
    } finally {
      setDeleteLoading(false);
      setDeleteModalVisible(false);
      setDeleteSiteId(null);
      setDeleteSiteName('');
    }
  };

  // Edit modal handling functions
  const handleEditModalCancel = () => {
    setEditModalVisible(false);
    setEditingSite(null);
    editForm.resetFields();
  };

  const handleEditModalSave = async () => {
    if (!checkAuth() || !editingSite) return;

    try {
      setEditFormLoading(true);
      const values = await editForm.validateFields();
      
      await axios.put(`http://localhost:8000/api/site-management/sites/${editingSite.id}`, values, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      message.success(t('site.notification.updateSuccess'));
      fetchSites(); // Refresh data
      setEditModalVisible(false);
      setEditingSite(null);
      editForm.resetFields();
    } catch (error: any) {
      logError('handleEditModalSave', error);
      const errorMessage = getErrorMessage(error);
      message.error(t('site.error.updateSite', { error: errorMessage }));
    } finally {
      setEditFormLoading(false);
    }
  };

  // Form handling functions
  const handleFormCancel = () => {
    form.resetFields();
    setSelectedKey('list');
  };

  const handleFormSave = async (action: 'save' | 'save_and_new') => {
    if (!checkAuth()) return;

    try {
      setFormLoading(true);
      const values = await form.validateFields();
      
      if (isEditing && editingSiteId) {
        // Update existing site
        await axios.put(`http://localhost:8000/api/site-management/sites/${editingSiteId}`, values, {
          timeout: 10000,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });
        message.success(t('site.notification.updateSuccess'));
      } else {
        // Create new site
        await axios.post('http://localhost:8000/api/site-management/sites', values, {
          timeout: 10000,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });
        message.success(t('site.notification.createSuccess'));
      }
      
      if (action === 'save') {
        // Chuyển về tab danh sách
        setSelectedKey('list');
        setIsEditing(false);
        setEditingSiteId(null);
        form.resetFields();
      } else {
        // Reset form để thêm mới tiếp
        form.resetFields();
        form.setFieldsValue({
          is_activated: true,
          parent_id: undefined
        });
        setIsEditing(false);
        setEditingSiteId(null);
      }
      
      fetchSites(); // Refresh data
    } catch (error: any) {
      logError('handleFormSave', error);
      const errorMessage = getErrorMessage(error);
      message.error(t('site.error.saveSite', { error: errorMessage }));
    } finally {
      setFormLoading(false);
    }
  };

  // Check if site code exists
  const checkSiteCode = async (code: string): Promise<boolean> => {
    if (!code) return false;
    
    try {
      const response = await axios.get(`http://localhost:8000/api/site-management/sites/check-code/${code}`, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      return response.data.exists;
    } catch (error: any) {
      logError('checkSiteCode', error);
      message.error(t('site.error.checkSiteCode'));
      return false;
    }
  };

  // Validate site code
  const validateSiteCode = async (_: any, value: string) => {
    if (!value) {
      return Promise.reject(new Error(t('site.validation.codeRequired')));
    }
    
    if (value.length < 3) {
      return Promise.reject(new Error(t('site.validation.codeMinLength')));
    }
    
    if (value.length > 20) {
      return Promise.reject(new Error(t('site.validation.codeMaxLength')));
    }
    
    if (!/^[A-Z0-9_]+$/.test(value)) {
      return Promise.reject(new Error(t('site.validation.codeFormat')));
    }
    
    // Kiểm tra trùng lặp code (chỉ khi không phải đang edit)
    if (!isEditing || (isEditing && editingSiteId)) {
      const exists = await checkSiteCode(value);
      if (exists) {
        return Promise.reject(new Error(t('site.validation.codeExists')));
      }
    }
    
    return Promise.resolve();
  };

  useEffect(() => {
    fetchModuleDetails();
    
    // Fetch sites if list is selected
    if (selectedKey === 'list') {
      fetchSites();
    }
    
    // Fetch sites if add form is selected (for parent site options)
    if (selectedKey === 'add') {
      fetchSites();
    }
    
    // Cập nhật title của tab
    document.title = 'Site Management Module - Metis Platform';
    
    // Thêm event listener để refresh data khi tab được focus
    const handleFocus = () => {
      console.log('DEBUG: Tab focused, refreshing data...');
      fetchModuleDetails();
      if (selectedKey === 'list' || selectedKey === 'add') {
        fetchSites();
      }
    };
    
    // Thêm event listener để xử lý trước khi đóng tab
    const handleBeforeUnload = () => {
      // Có thể thêm logic để lưu trạng thái nếu cần
      console.log('DEBUG: Tab is being closed...');
    };
    
    // Xử lý resize window
    const handleResize = () => {
      const newIsMobile = window.innerWidth <= 768;
      setIsMobile(newIsMobile);
      
      // Luôn giữ collapsed = true cho cả mobile và desktop
      setCollapsed(true);
    };
    
    window.addEventListener('focus', handleFocus);
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('resize', handleResize);
    };
  }, [selectedKey]);

  const handleMenuClick = (e: any) => {
    setSelectedKey(e.key);
    if (e.key === 'add') {
      // Reset form khi chuyển sang trang thêm mới
      setIsEditing(false);
      setEditingSiteId(null);
      form.resetFields();
      form.setFieldsValue({
        is_activated: true,
        parent_id: undefined
      });
    }
  };

  const handleOpenAppsPage = () => {
    navigate('/apps');
  };

  const handleToggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  const handleOverlayClick = () => {
    if (isMobile && !collapsed) {
      setCollapsed(true);
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <ClockCircleOutlined style={{ color: '#52c41a' }} />;
      case 'warning':
        return <InfoCircleOutlined style={{ color: '#faad14' }} />;
      case 'error':
        return <ClockCircleOutlined style={{ color: '#f5222d' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#1890ff' }} />;
    }
  };

  const renderContent = () => {
    switch (selectedKey) {
      case 'overview':
        return renderOverview();
      case 'list':
        return renderSitesList();
      case 'add':
        return renderSiteForm();
      default:
        return renderOverview();
    }
  };

  const renderOverview = () => {
    return (
      <div>
        {/* Module Overview Card */}
        <Card className="site-management-overview-card" style={{ marginBottom: '24px' }}>
          <Row gutter={[24, 16]}>
            <Col xs={24} md={8}>
              <div style={{ textAlign: 'center' }}>
                <Avatar 
                  size={80} 
                  icon={<SettingOutlined />} 
                  className="site-management-overview-avatar"
                  style={{ 
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    fontSize: '32px'
                  }}
                />
                <Title level={3} style={{ margin: '16px 0 8px 0' }}>
                  {moduleDetail?.summary}
                </Title>
                <Text type="secondary" style={{ fontSize: '14px', fontFamily: 'monospace' }}>
                  {moduleDetail?.name}
                </Text>
              </div>
            </Col>
            <Col xs={24} md={16}>
              <div className="site-management-overview-stats">
                <Space size="large" wrap>
                  <div>
                    <Text type="secondary">{t('site.overview.version')}</Text>
                    <div><Text strong>v{moduleDetail?.version}</Text></div>
                  </div>
                  <div>
                    <Text type="secondary">{t('site.overview.author')}</Text>
                    <div><Text strong>{moduleDetail?.author}</Text></div>
                  </div>
                  <div>
                    <Text type="secondary">{t('site.overview.status')}</Text>
                    <div>
                      <Tag color={moduleDetail?.state === 'installed' ? 'green' : 'orange'}>
                        {moduleDetail?.state === 'installed' ? t('site.overview.installed') : t('site.overview.notInstalled')}
                      </Tag>
                    </div>
                  </div>
                </Space>
              </div>
              <div className="site-management-overview-tags" style={{ marginTop: '16px' }}>
                <Space wrap>
                  <Tag color="blue">{moduleDetail?.category}</Tag>
                  {moduleDetail?.is_core_module && (
                    <Tag color="red">{t('site.overview.coreModule')}</Tag>
                  )}
                  {moduleDetail?.auto_install && (
                    <Tag color="green">{t('site.overview.autoInstall')}</Tag>
                  )}
                </Space>
              </div>
              <Paragraph className="site-management-overview-description" style={{ marginTop: '16px' }}>
                {moduleDetail?.description}
              </Paragraph>
            </Col>
          </Row>
        </Card>

        {/* Statistics and Activities */}
        <Row gutter={[24, 24]}>
          <Col xs={24} md={12}>
            <Card title={t('site.overview.systemStats')} size="small" className="site-management-card-responsive">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="site-management-stats-card">
                    <Statistic
                      title={t('site.overview.dataTables')}
                      value={systemStats.tables}
                      valueStyle={{ color: '#1890ff', fontSize: '24px' }}
                      prefix={<DatabaseOutlined />}
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="site-management-stats-card">
                    <Statistic
                      title={t('site.overview.apiEndpoints')}
                      value={systemStats.endpoints}
                      valueStyle={{ color: '#52c41a', fontSize: '24px' }}
                      prefix={<ApiOutlined />}
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="site-management-stats-card">
                    <Statistic
                      title={t('site.overview.users')}
                      value={systemStats.users}
                      valueStyle={{ color: '#faad14', fontSize: '24px' }}
                      prefix={<UserOutlined />}
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="site-management-stats-card">
                    <Statistic
                      title={t('site.overview.permissions')}
                      value={systemStats.permissions}
                      valueStyle={{ color: '#722ed1', fontSize: '24px' }}
                      prefix={<SafetyOutlined />}
                    />
                  </div>
                </Col>
              </Row>
            </Card>
          </Col>
          <Col xs={24} md={12}>
            <Card title={t('site.overview.recentActivities')} size="small" className="site-management-card-responsive">
              <div className="site-management-activity-card">
                {recentActivities.map((activity, index) => (
                  <div key={index} className="site-management-activity-item">
                    <div style={{ display: 'flex', alignItems: 'flex-start', gap: '8px' }}>
                      {getActivityIcon(activity.type)}
                      <div style={{ flex: 1 }}>
                        <Text className="site-management-activity-time">{activity.time}</Text>
                        <div style={{ marginTop: '4px' }}>{activity.description}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  const renderSitesList = () => {
    const columns = [
      {
        title: t('site.table.code'),
        dataIndex: 'code',
        key: 'code',
        width: 120,
        defaultSortOrder: 'ascend' as const,
        sorter: (a: Site, b: Site) => a.code.localeCompare(b.code),
        render: (text: string) => (
          <Text strong style={{ fontFamily: 'monospace', fontSize: '13px' }}>
            {text}
          </Text>
        ),
      },
      {
        title: t('site.table.name'),
        dataIndex: 'name',
        key: 'name',
        width: 200,
        sorter: (a: Site, b: Site) => a.name.localeCompare(b.name),
        render: (text: string) => (
          <Text strong style={{ fontSize: '14px' }}>{text}</Text>
        ),
      },
      {
        title: t('site.table.description'),
        dataIndex: 'descriptions',
        key: 'descriptions',
        width: 250,
        render: (text: string) => (
          <Text type="secondary" style={{ fontSize: '13px' }}>
            {text || t('site.table.noDescription')}
          </Text>
        ),
      },
      {
        title: t('site.table.status'),
        dataIndex: 'is_activated',
        key: 'is_activated',
        width: 150,
        render: (isActivated: boolean, record: Site) => (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Switch
              checked={isActivated}
              onChange={() => handleToggleActivation(record.id, isActivated)}
              size="small"
            />
            <Tag color={isActivated ? 'green' : 'red'}>
              {isActivated ? t('site.table.active') : t('site.table.inactive')}
            </Tag>
          </div>
        ),
      },
      {
        title: t('site.table.actions'),
        key: 'actions',
        width: 100,
        fixed: 'right' as const,
        render: (_: any, record: Site) => (
          <Space size="small">
            <Tooltip title={t('site.table.edit')}>
              <Button
                type="text"
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEditSite(record)}
                style={{ color: '#1890ff' }}
              />
            </Tooltip>
            <Tooltip title={t('site.table.delete')}>
              <Button
                type="text"
                icon={<DeleteOutlined />}
                size="small"
                onClick={() => handleDeleteSite(record.id, record.name)}
                style={{ color: '#ff4d4f' }}
              />
            </Tooltip>
          </Space>
        ),
      },
    ];

    return (
      <div>
        <Card 
          title={
            <Space>
              <UnorderedListOutlined />
              <span>{t('site.list.title')}</span>
              <Tag color="blue">{filteredSites.length} {t('site.list.sites')}</Tag>
            </Space>
          }
          extra={
            <Space className="site-management-action-buttons">
              <Input.Search
                placeholder={t('site.list.searchPlaceholder')}
                allowClear
                style={{ width: 300 }}
                className="site-management-sites-search"
                onSearch={handleSearchChange}
                onChange={(e) => handleSearchChange(e.target.value)}
                value={searchText}
              />
              <Tooltip title={t('site.list.refresh')}>
                <Button 
                  type="primary" 
                  icon={<ReloadOutlined />}
                  onClick={fetchSites}
                  loading={sitesLoading}
                />
              </Tooltip>
              <Tooltip title={t('site.list.addNew')}>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={handleAddNewSite}
                />
              </Tooltip>
            </Space>
          }
          className="site-management-card-responsive"
        >
          {filteredSites.length === 0 && !sitesLoading ? (
            <div style={{ textAlign: 'center', padding: '40px 20px' }}>
              <DatabaseOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: '16px' }} />
              <Text type="secondary">
                {searchText ? t('site.list.noSearchResults') : t('site.list.noData')}
              </Text>
              <br />
              <Button type="link" onClick={fetchSites} style={{ marginTop: '8px' }}>
                {t('site.list.tryAgain')}
              </Button>
            </div>
          ) : (
            <div className="site-management-sites-table-wrapper">
              <Table
                columns={columns}
                dataSource={filteredSites}
                rowKey="id"
                loading={sitesLoading}
                className="site-management-sites-table"
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => 
                    t('site.list.pagination', { start: range[0], end: range[1], total }),
                }}
                expandable={{
                  defaultExpandAllRows: true,
                  expandedRowKeys: expandedRowKeys,
                  onExpandedRowsChange: (expandedRows) => {
                    setExpandedRowKeys([...expandedRows]);
                  },
                  expandRowByClick: true,
                  expandIcon: ({ expanded, onExpand, record }) => {
                    if (record.children && record.children.length > 0) {
                      return (
                        <Button
                          type="text"
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            onExpand(record, e);
                          }}
                          style={{ padding: 0, width: 'auto' }}
                        >
                          {expanded ? '▼' : '▶'}
                        </Button>
                      );
                    }
                    return null;
                  },
                }}
                scroll={{ x: 920, y: 500 }}
                size="middle"
              />
            </div>
          )}
        </Card>
      </div>
    );
  };

  const renderSiteForm = () => {
    return (
      <div>
        <Card 
          title={
            <Space>
              <FormOutlined />
              <span>{isEditing ? t('site.form.updateTitle') : t('site.form.addTitle')}</span>
            </Space>
          }
          className="site-management-card-responsive"
        >
          <Form
            form={form}
            layout="vertical"
            className="site-management-form"
            initialValues={{
              is_activated: true
            }}
            onFinish={() => handleFormSave('save')}
          >
            <Row gutter={24}>
              <Col xs={24} sm={12}>
                <Form.Item
                  label={t('site.form.code')}
                  name="code"
                  rules={[
                    { required: true, message: t('site.form.codeRequired') },
                    { validator: validateSiteCode }
                  ]}
                >
                  <Input 
                    placeholder={t('site.form.codePlaceholder')}
                    disabled={isEditing}
                    style={{ fontFamily: 'monospace' }}
                  />
                </Form.Item>
              </Col>
              
              <Col xs={24} sm={12}>
                <Form.Item
                  label={t('site.form.name')}
                  name="name"
                  rules={[
                    { required: true, message: t('site.form.nameRequired') },
                    { max: 250, message: t('site.form.nameMaxLength') }
                  ]}
                >
                  <Input placeholder={t('site.form.namePlaceholder')} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col xs={24} sm={12}>
                <Form.Item
                  label={t('site.form.description')}
                  name="descriptions"
                  rules={[
                    { max: 1000, message: t('site.form.descriptionMaxLength') }
                  ]}
                >
                  <Input.TextArea 
                    placeholder={t('site.form.descriptionPlaceholder')}
                    rows={3}
                    showCount
                    maxLength={1000}
                  />
                </Form.Item>
              </Col>
              
              <Col xs={24} sm={12}>
                <Form.Item
                  label={t('site.form.parentSite')}
                  name="parent_id"
                >
                  <SiteParentDropdown
                    placeholder={t('site.form.parentSitePlaceholder')}
                    allowClear
                    sites={sites}
                    loading={sitesLoading}
                  />
                </Form.Item>
                
                <Form.Item
                  label={t('site.form.status')}
                  name="is_activated"
                  valuePropName="checked"
                >
                  <Switch 
                    checkedChildren={t('site.form.activated')} 
                    unCheckedChildren={t('site.form.deactivated')}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row>
              <Col span={24}>
                <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
                  <Space 
                    direction={isMobile ? 'vertical' : 'horizontal'} 
                    style={{ width: isMobile ? '100%' : 'auto' }}
                    className="site-management-form-buttons"
                  >
                    <Button 
                      onClick={handleFormCancel}
                      style={{ width: isMobile ? '100%' : 'auto' }}
                    >
                      {t('site.form.cancel')}
                    </Button>
                    <Button 
                      type="primary" 
                      icon={<SaveOutlined />}
                      loading={formLoading}
                      onClick={() => handleFormSave('save_and_new')}
                      style={{ width: isMobile ? '100%' : 'auto' }}
                    >
                      {t('site.form.saveAndNew')}
                    </Button>
                    <Button 
                      type="primary" 
                      icon={<SaveOutlined />}
                      loading={formLoading}
                      onClick={() => handleFormSave('save')}
                      style={{ width: isMobile ? '100%' : 'auto' }}
                    >
                      {t('site.form.saveAndClose')}
                    </Button>
                  </Space>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Card>
      </div>
    );
  };

  if (loading) {
    return (
      <Layout style={{ minHeight: '100vh' }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }}>
          <div style={{ textAlign: 'center', color: 'white' }}>
            <Spin size="large" style={{ marginBottom: '16px' }} />
            <div style={{ fontSize: '24px', marginBottom: '16px' }}>{t('site.loading.loading')}</div>
            <div>{t('site.loading.moduleName')}</div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout className="site-management-page-layout">
      {/* Header */}
      <Header className="site-management-page-header">
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          height: '100%'
        }}>
          <Space size="large">
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              {/* Icon menu cho cả mobile và desktop */}
              <Button
                type="text"
                icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                onClick={handleToggleSidebar}
                style={{ 
                  color: 'white',
                  fontSize: '16px',
                  width: '32px',
                  height: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              />
              <Avatar 
                size={32} 
                icon={<SettingOutlined />} 
                style={{ 
                  background: 'rgba(255,255,255,0.2)',
                  border: '1px solid rgba(255,255,255,0.3)',
                  fontSize: '16px'
                }}
              />
              <div>
                <Title level={5} className="site-management-title-responsive" style={{ color: 'white', margin: 0, fontSize: '16px' }}>
                  {t('site.header.title')}
                </Title>
                <Text className="site-management-subtitle-responsive" style={{ color: 'rgba(255,255,255,0.8)', fontSize: '12px', display: 'block' }}>
                  {t('site.header.subtitle')}
                </Text>
              </div>
            </div>
          </Space>
          
          <Space>
            <Badge count={1} size="small">
              <Button
                type="text"
                icon={<InfoCircleOutlined />}
                onClick={() => message.info(t('site.info.moduleDescription'))}
                style={{ color: '#1890ff' }}
              />
            </Badge>
            {isNewTab && (
              <>
                <Button 
                  type="text" 
                  icon={<AppstoreOutlined />} 
                  style={{ color: 'white' }}
                  onClick={handleOpenAppsPage}
                  title={t('site.header.openAppsPage')}
                />
                <Button 
                  type="text" 
                  icon={<CloseOutlined />} 
                  style={{ color: 'white' }}
                  onClick={() => window.close()}
                  title={t('site.header.closeTab')}
                />
              </>
            )}
          </Space>
        </div>
      </Header>

      <Layout>
        {/* Sidebar Navigation */}
        <Sider 
          trigger={null} 
          collapsible 
          collapsed={collapsed}
          className={`site-management-page-sider ${collapsed ? 'collapsed' : ''}`}
          width={280}
          collapsedWidth={80}
          onClick={handleOverlayClick}
        >
          <Menu
            mode="inline"
            selectedKeys={[selectedKey]}
            items={menuItems}
            onClick={handleMenuClick}
            className="site-management-nav-menu"
          />
        </Sider>

        {/* Main Content */}
        <Layout className={`site-management-page-content ${collapsed ? 'collapsed' : ''}`}>
          <Content className="site-management-page-main-content" style={{ 
            margin: '24px',
            padding: '24px'
          }}>
            
            
            <Breadcrumb className="site-management-breadcrumb" style={{ marginBottom: '24px' }}>
              <Breadcrumb.Item>
                <Button 
                  type="link" 
                  onClick={handleOpenAppsPage}
                  style={{ padding: 0, height: 'auto', lineHeight: '1.5' }}
                >
                  {t('site.breadcrumb.apps')}
                </Button>
              </Breadcrumb.Item>
              <Breadcrumb.Item>{t('site.breadcrumb.siteManagement')}</Breadcrumb.Item>
              <Breadcrumb.Item>
                {selectedKey === 'overview' ? t('site.breadcrumb.overview') : 
                 selectedKey === 'list' ? t('site.breadcrumb.list') : 
                 selectedKey === 'add' ? (isEditing ? t('site.breadcrumb.update') : t('site.breadcrumb.add')) : t('site.breadcrumb.overview')}
              </Breadcrumb.Item>
            </Breadcrumb>
            
            {renderContent()}
          </Content>
        </Layout>
      </Layout>

      {/* Edit Site Modal */}
      <Modal
        title={
          <Space>
            <EditOutlined />
            <span>{t('site.modal.editTitle')}</span>
          </Space>
        }
        open={editModalVisible}
        onOk={handleEditModalSave}
        onCancel={handleEditModalCancel}
        confirmLoading={editFormLoading}
        okText={t('site.modal.save')}
        cancelText={t('site.modal.cancel')}
        width={600}
        centered
        destroyOnClose
      >
        <Form
          form={editForm}
          layout="vertical"
          style={{ marginTop: '16px' }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label={t('site.modal.code')}
                name="code"
              >
                <Input 
                  disabled
                  style={{ fontFamily: 'monospace', backgroundColor: '#f5f5f5' }}
                />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                label={t('site.modal.name')}
                name="name"
                rules={[
                  { required: true, message: t('site.modal.nameRequired') },
                  { max: 250, message: t('site.modal.nameMaxLength') }
                ]}
              >
                <Input placeholder={t('site.modal.namePlaceholder')} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label={t('site.modal.description')}
                name="descriptions"
                rules={[
                  { max: 1000, message: t('site.modal.descriptionMaxLength') }
                ]}
              >
                <Input.TextArea 
                  placeholder={t('site.modal.descriptionPlaceholder')}
                  rows={3}
                  showCount
                  maxLength={1000}
                />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                label={t('site.modal.parentSite')}
                name="parent_id"
              >
                <SiteParentDropdown
                  placeholder={t('site.modal.parentSitePlaceholder')}
                  allowClear
                  sites={sites}
                  loading={sitesLoading}
                  disabled
                  style={{ backgroundColor: '#f5f5f5' }}
                />
              </Form.Item>
              
              <Form.Item
                label={t('site.modal.status')}
                name="is_activated"
                valuePropName="checked"
              >
                <Switch 
                  checkedChildren={t('site.modal.activated')} 
                  unCheckedChildren={t('site.modal.deactivated')}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* Warning Modal for Sites with Children */}
      <Modal
        title={t('site.modal.cannotDeleteTitle')}
        open={warningModalVisible}
        onOk={() => {
          setWarningModalVisible(false);
          setWarningSiteName('');
          setWarningChildrenCount(0);
        }}
        okText={t('site.modal.close')}
        centered
        cancelButtonProps={{ style: { display: 'none' } }}
      >
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <div style={{ fontSize: '48px', color: '#faad14', marginBottom: '16px' }}>
            ⚠️
          </div>
          <p style={{ fontSize: '16px', marginBottom: '12px' }}>
            {t('site.modal.cannotDeleteMessage', { name: warningSiteName })}
          </p>
          <p style={{ color: '#666', fontSize: '14px' }}>
            {t('site.modal.cannotDeleteReason', { count: warningChildrenCount })}
          </p>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        title={t('site.modal.confirmDeleteTitle')}
        open={deleteModalVisible}
        onOk={handleConfirmDelete}
        onCancel={() => {
          setDeleteModalVisible(false);
          setDeleteSiteId(null);
          setDeleteSiteName('');
        }}
        confirmLoading={deleteLoading}
        okText={t('site.modal.delete')}
        cancelText={t('site.modal.cancel')}
        okType="danger"
        centered
      >
        <p>{t('site.modal.confirmDeleteMessage', { name: deleteSiteName })}</p>
        <p style={{ color: '#ff4d4f', fontSize: '12px' }}>
          {t('site.modal.confirmDeleteWarning')}
        </p>
      </Modal>
    </Layout>
  );
};

export default SiteManagementPage; 