import React, { useEffect, useState } from 'react';
import { Card, Row, Col, Tree, Button, message, Spin } from 'antd';
import axios from 'axios';
import SearchRoleDropdown from '../../components/SearchRoleDropdown';

const RolePermissionsPage: React.FC = () => {
  const [roles, setRoles] = useState<any[]>([]);
  const [selectedRole, setSelectedRole] = useState<number | null>(null);
  const [treeData, setTreeData] = useState<any[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // Load roles
  useEffect(() => {
    console.log('RolePermissionsPage: Loading roles...');
    axios.get('/api/user-management/roles').then(res => {
      console.log('RolePermissionsPage: Roles loaded:', res.data);
      setRoles(res.data);
    }).catch(error => {
      console.error('RolePermissionsPage: Error loading roles:', error);
    });
  }, []);

  // Load permission tree
  useEffect(() => {
    console.log('RolePermissionsPage: Loading permission tree...');
    axios.get('/api/user-management/permissions/tree').then(res => {
      console.log('RolePermissionsPage: Permission tree loaded:', res.data);
      setTreeData(res.data);
    }).catch(error => {
      console.error('RolePermissionsPage: Error loading permission tree:', error);
    });
  }, []);

  // Load checked permissions when role changes
  useEffect(() => {
    if (selectedRole) {
      console.log('RolePermissionsPage: Loading permissions for role:', selectedRole);
      setLoading(true);
      axios.get(`/api/user-management/roles/${selectedRole}/permissions`).then(res => {
        console.log('RolePermissionsPage: Role permissions loaded:', res.data);
        setCheckedKeys(res.data);
      }).catch(error => {
        console.error('RolePermissionsPage: Error loading role permissions:', error);
      }).finally(() => setLoading(false));
    } else {
      console.log('RolePermissionsPage: No role selected, clearing checked keys');
      setCheckedKeys([]);
    }
  }, [selectedRole]);

  const handleSave = async () => {
    console.log('RolePermissionsPage: handleSave called');

    if (!selectedRole) {
      console.log('RolePermissionsPage: No role selected');
      message.warning('Vui lòng chọn role trước khi lưu!');
      return;
    }

    console.log('RolePermissionsPage: Starting save process...');
    setSaving(true);

    try {
      // Lọc và chuyển đổi checkedKeys thành mảng số nguyên
      const permissionIds = checkedKeys
        .map(key => {
          const num = Number(key);
          return isNaN(num) ? null : num;
        })
        .filter(id => id !== null) as number[];

      console.log('RolePermissionsPage: Saving role permissions:', {
        role_id: selectedRole,
        permission_ids: permissionIds,
        original_checked_keys: checkedKeys
      });

      const response = await axios.post('/api/user-management/role-permissions', {
        role_id: selectedRole,
        permission_ids: permissionIds
      });

      console.log('RolePermissionsPage: Save successful:', response.data);
      message.success(response.data.message || 'Lưu phân quyền thành công!');
    } catch (error: any) {
      console.error('RolePermissionsPage: Error saving role permissions:', error);
      const errorMessage = error.response?.data?.detail || 'Lưu phân quyền thất bại!';
      message.error(errorMessage);
    } finally {
      console.log('RolePermissionsPage: Save process completed');
      setSaving(false);
    }
  };

  // Test function để kiểm tra API
  const handleTestAPI = async () => {
    console.log('RolePermissionsPage: Testing API connection...');
    try {
      const response = await axios.get('/api/user-management/roles');
      console.log('RolePermissionsPage: API test successful:', response.data);
      message.success('API hoạt động bình thường!');
    } catch (error: any) {
      console.error('RolePermissionsPage: API test failed:', error);
      message.error('API không hoạt động: ' + (error.message || 'Unknown error'));
    }
  };

  return (
    <Card title="Phân quyền cho Role" style={{ minHeight: 500 }}>
      <Row gutter={32}>
        <Col span={6}>
          <div style={{ marginBottom: 16 }}>Chọn Role:</div>
          {/* <Select
            style={{ width: '100%' }}
            placeholder="Chọn role"
            value={selectedRole ?? undefined}
            onChange={setSelectedRole}
            showSearch
            optionFilterProp="children"
          >
            {roles.map(role => (
              <Option key={role.id} value={role.id}>{role.name}</Option>
            ))}
          </Select> */}
          <SearchRoleDropdown
            value={selectedRole ?? undefined}
            onChange={v => setSelectedRole(v ?? null)}
            roles={roles}
            loading={false}
            allowClear={true}
            placeholder="Tìm kiếm và chọn vai trò"
            style={{ width: '100%' }}
          />
        </Col>
        <Col span={18}>
          <div style={{ marginBottom: 16 }}>Danh sách quyền:</div>
          <div style={{ maxHeight: 400, overflow: 'auto', border: '1px solid #eee', borderRadius: 6, padding: 8, background: '#fafbfc' }}>
            {loading ? <Spin /> : (
              <Tree
                checkable
                treeData={treeData}
                checkedKeys={checkedKeys}
                onCheck={setCheckedKeys as any}
                defaultExpandAll
                titleRender={(nodeData) => (
                  <span>
                    <span style={{ color: '#888', fontSize: 12 }}>{nodeData.code}</span><br />
                    <span style={{ fontWeight: 500 }}>{nodeData.name}</span>
                  </span>
                )}
              />
            )}
          </div>
          <div style={{ marginTop: 24, display: 'flex', gap: 12 }}>
            <Button type="primary" onClick={handleSave} loading={saving} disabled={!selectedRole}>
              Lưu
            </Button>
            <Button onClick={handleTestAPI}>
              Test API
            </Button>
          </div>
        </Col>
      </Row>
    </Card>
  );
};

export default RolePermissionsPage;
