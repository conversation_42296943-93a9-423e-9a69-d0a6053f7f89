import React, { useEffect, useState } from 'react';
import { Card, Row, Col, Tree, But<PERSON>, message, Spin } from 'antd';
import axios from 'axios';
import SearchRoleDropdown from '../../components/SearchRoleDropdown';

const RolePermissionsPage: React.FC = () => {
  const [roles, setRoles] = useState<any[]>([]);
  const [selectedRole, setSelectedRole] = useState<number | null>(null);
  const [treeData, setTreeData] = useState<any[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // Load roles
  useEffect(() => {
    axios.get('/api/user-management/roles').then(res => {
      setRoles(res.data);
    });
  }, []);

  // Load permission tree
  useEffect(() => {
    axios.get('/api/user-management/permissions/tree').then(res => {
      setTreeData(res.data);
    });
  }, []);

  // Load checked permissions when role changes
  useEffect(() => {
    if (selectedRole) {
      setLoading(true);
      axios.get(`/api/user-management/roles/${selectedRole}/permissions`).then(res => {
        setCheckedKeys(res.data);
      }).finally(() => setLoading(false));
    } else {
      setCheckedKeys([]);
    }
  }, [selectedRole]);

  const handleSave = async () => {
    if (!selectedRole) return;
    setSaving(true);
    try {
      await axios.post('/api/user-management/role-permissions', {
        role_id: selectedRole,
        permission_ids: checkedKeys.map(Number)
      });
      message.success('Lưu phân quyền thành công!');
    } catch {
      message.error('Lưu phân quyền thất bại!');
    }
    setSaving(false);
  };

  return (
    <Card title="Phân quyền cho Role" style={{ minHeight: 500 }}>
      <Row gutter={32}>
        <Col span={6}>
          <div style={{ marginBottom: 16 }}>Chọn Role:</div>
          {/* <Select
            style={{ width: '100%' }}
            placeholder="Chọn role"
            value={selectedRole ?? undefined}
            onChange={setSelectedRole}
            showSearch
            optionFilterProp="children"
          >
            {roles.map(role => (
              <Option key={role.id} value={role.id}>{role.name}</Option>
            ))}
          </Select> */}
          <SearchRoleDropdown
            value={selectedRole ?? undefined}
            onChange={v => setSelectedRole(v ?? null)}
            roles={roles}
            loading={false}
            allowClear={true}
            placeholder="Tìm kiếm và chọn vai trò"
            style={{ width: '100%' }}
          />
        </Col>
        <Col span={18}>
          <div style={{ marginBottom: 16 }}>Danh sách quyền:</div>
          <div style={{ maxHeight: 400, overflow: 'auto', border: '1px solid #eee', borderRadius: 6, padding: 8, background: '#fafbfc' }}>
            {loading ? <Spin /> : (
              <Tree
                checkable
                treeData={treeData}
                checkedKeys={checkedKeys}
                onCheck={setCheckedKeys as any}
                defaultExpandAll
                titleRender={(nodeData) => (
                  <span>
                    <span style={{ color: '#888', fontSize: 12 }}>{nodeData.code}</span><br />
                    <span style={{ fontWeight: 500 }}>{nodeData.name}</span>
                  </span>
                )}
              />
            )}
          </div>
          <Button type="primary" style={{ marginTop: 24 }} onClick={handleSave} loading={saving} disabled={!selectedRole}>
            Lưu
          </Button>
        </Col>
      </Row>
    </Card>
  );
};

export default RolePermissionsPage;
