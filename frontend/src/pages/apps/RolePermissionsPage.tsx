import React, { useEffect, useState } from 'react';
import { <PERSON>, Row, Col, Tree, Button, message, Spin } from 'antd';
import axios from 'axios';
import SearchRoleDropdown from '../../components/SearchRoleDropdown';
import { logError, getErrorMessage } from '../../utils/errorHandler';

const RolePermissionsPage: React.FC = () => {
  const [roles, setRoles] = useState<any[]>([]);
  const [selectedRole, setSelectedRole] = useState<number | null>(null);
  const [treeData, setTreeData] = useState<any[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // Load roles
  useEffect(() => {
    console.log('RolePermissionsPage: Loading roles...');
    axios.get('http://localhost:8000/api/user-management/roles', {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    }).then(res => {
      console.log('RolePermissionsPage: Roles loaded:', res.data);
      console.log('RolePermissionsPage: Roles count:', res.data.length);
      console.log('RolePermissionsPage: First role:', res.data[0]);
      setRoles(res.data);
    }).catch(error => {
      console.error('RolePermissionsPage: Error loading roles:', error);
      logError('RolePermissionsPage: Load roles', error);
      message.error(getErrorMessage(error));
    });
  }, []);

  // Load permission tree
  useEffect(() => {
    console.log('RolePermissionsPage: Loading permission tree...');
    axios.get('http://localhost:8000/api/user-management/permissions/tree', {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    }).then(res => {
      console.log('RolePermissionsPage: Permission tree loaded:', res.data);
      setTreeData(res.data);
    }).catch(error => {
      console.error('RolePermissionsPage: Error loading permission tree:', error);
      logError('RolePermissionsPage: Load permission tree', error);
      message.error(getErrorMessage(error));
    });
  }, []);

  // Load checked permissions when role changes
  useEffect(() => {
    if (selectedRole) {
      console.log('RolePermissionsPage: Loading permissions for role:', selectedRole);
      setLoading(true);
      axios.get(`http://localhost:8000/api/user-management/roles/${selectedRole}/permissions`, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }).then(res => {
        console.log('RolePermissionsPage: Role permissions loaded:', res.data);
        setCheckedKeys(res.data);
      }).catch(error => {
        console.error('RolePermissionsPage: Error loading role permissions:', error);
        logError('RolePermissionsPage: Load role permissions', error);
        message.error(getErrorMessage(error));
      }).finally(() => setLoading(false));
    } else {
      console.log('RolePermissionsPage: No role selected, clearing checked keys');
      setCheckedKeys([]);
    }
  }, [selectedRole]);

  const handleSave = async () => {
    console.log('RolePermissionsPage: handleSave called');

    if (!selectedRole) {
      console.log('RolePermissionsPage: No role selected');
      message.warning('Vui lòng chọn role trước khi lưu!');
      return;
    }

    console.log('RolePermissionsPage: Starting save process...');
    setSaving(true);

    try {
      // Lọc và chuyển đổi checkedKeys thành mảng số nguyên
      const permissionIds = checkedKeys
        .map(key => {
          const num = Number(key);
          return isNaN(num) ? null : num;
        })
        .filter(id => id !== null) as number[];

      console.log('RolePermissionsPage: Saving role permissions:', {
        role_id: selectedRole,
        permission_ids: permissionIds,
        original_checked_keys: checkedKeys
      });

      const response = await axios.post('http://localhost:8000/api/user-management/role-permissions', {
        role_id: selectedRole,
        permission_ids: permissionIds
      }, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      console.log('RolePermissionsPage: Save successful:', response.data);
      message.success(response.data.message || 'Lưu phân quyền thành công!');
    } catch (error: any) {
      console.error('RolePermissionsPage: Error saving role permissions:', error);
      logError('RolePermissionsPage: Save role permissions', error);
      const errorMessage = getErrorMessage(error);
      message.error(errorMessage);
    } finally {
      console.log('RolePermissionsPage: Save process completed');
      setSaving(false);
    }
  };

  // Test function để kiểm tra API
  const handleTestAPI = async () => {
    console.log('RolePermissionsPage: Testing API connection...');
    try {
      const response = await axios.get('http://localhost:8000/api/user-management/roles', {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      console.log('RolePermissionsPage: API test successful:', response.data);
      message.success('API hoạt động bình thường!');
    } catch (error: any) {
      console.error('RolePermissionsPage: API test failed:', error);
      logError('RolePermissionsPage: Test API', error);
      message.error('API không hoạt động: ' + getErrorMessage(error));
    }
  };

  return (
    <Card title="Phân quyền cho Role" style={{ minHeight: 500 }}>
      <Row gutter={32}>
        <Col span={6}>
          <div style={{ marginBottom: 16 }}>Chọn Role:</div>
          {/* <Select
            style={{ width: '100%' }}
            placeholder="Chọn role"
            value={selectedRole ?? undefined}
            onChange={setSelectedRole}
            showSearch
            optionFilterProp="children"
          >
            {roles.map(role => (
              <Option key={role.id} value={role.id}>{role.name}</Option>
            ))}
          </Select> */}
          <SearchRoleDropdown
            value={selectedRole ?? undefined}
            onChange={v => {
              console.log('RolePermissionsPage: Role selected:', v);
              setSelectedRole(v ?? null);
            }}
            roles={roles}
            loading={false}
            allowClear={true}
            placeholder="Tìm kiếm và chọn vai trò"
            style={{ width: '100%' }}
          />
          {/* Debug info */}
          <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
            Debug: {roles.length} roles loaded, selected: {selectedRole}
          </div>
        </Col>
        <Col span={18}>
          <div style={{ marginBottom: 16 }}>Danh sách quyền:</div>
          <div style={{ maxHeight: 400, overflow: 'auto', border: '1px solid #eee', borderRadius: 6, padding: 8, background: '#fafbfc' }}>
            {loading ? <Spin /> : (
              <Tree
                checkable
                treeData={treeData}
                checkedKeys={checkedKeys}
                onCheck={setCheckedKeys as any}
                defaultExpandAll
                titleRender={(nodeData) => (
                  <span>
                    <span style={{ color: '#888', fontSize: 12 }}>{nodeData.code}</span><br />
                    <span style={{ fontWeight: 500 }}>{nodeData.name}</span>
                  </span>
                )}
              />
            )}
          </div>
          <div style={{ marginTop: 24, display: 'flex', gap: 12 }}>
            <Button type="primary" onClick={handleSave} loading={saving} disabled={!selectedRole}>
              Lưu
            </Button>
            <Button onClick={handleTestAPI}>
              Test API
            </Button>
          </div>
        </Col>
      </Row>
    </Card>
  );
};

export default RolePermissionsPage;
