import React, { useState, useEffect, useMemo } from 'react';
import {
  <PERSON>,
  <PERSON>,
  Card,
  Button,
  Typography,
  Layout,
  Space,
  Avatar,
  Breadcrumb,
  Menu,
  Badge,
  Spin,
  Tag,
  Statistic,
  Alert,
  Table,
  Switch,
  Modal,
  Form,
  Input,
  App
} from 'antd';
import {
  InfoCircleOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  SafetyOutlined,
  ClockCircleOutlined,
  AppstoreOutlined,
  CloseOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  DashboardOutlined,
  TeamOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import { logError } from '../../utils/errorHandler';
import './UserManagementPage.css';
import SearchUserDropdown from '../../components/SearchUserDropdown';
import SearchRoleDropdown from '../../components/SearchRoleDropdown';
import { useLanguage } from '../../contexts/LanguageContext';
import RolePermissionsPage from './RolePermissionsPage';

const { Header, Content, Sider } = Layout;
const { Title, Text, Paragraph } = Typography;

interface UserManagementModuleDetail {
  name: string;
  summary: string;
  version: string;
  state: 'installed' | 'uninstalled';
  author?: string;
  category?: string;
  description?: string;
  is_core_module?: boolean;
  auto_install?: boolean;
  hidden?: boolean;
  is_installed?: boolean;
}

interface UserStats {
  total_users: number;
  active_users: number;
  roles: number;
  permissions: number;
}

interface ActivityItem {
  time: string;
  description: string;
  type: 'info' | 'success' | 'warning' | 'error';
}

interface User {
  id: string;
  username: string;
  full_name: string;
  email: string;
  phone_number?: string;
  is_active: boolean;
}

interface Role {
  id: number;
  name: string;
  description?: string;
  is_activated: boolean;
}

interface UserRole {
  user_id: string;
  role_id: number;
  username: string;
  full_name: string;
  role_name: string;
}

const UserManagementPage: React.FC = () => {
  const { t, i18n, ready } = useTranslation();
  const { language } = useLanguage();
  const { message } = App.useApp();
  
  // Debug logging
  console.log('UserManagementPage - Current language:', language);
  console.log('UserManagementPage - Translation test:', t('user.menu.overview'));
  console.log('UserManagementPage - Is i18n ready:', ready);
  
  // Force re-render when language changes
  useEffect(() => {
    console.log('UserManagementPage - Language changed to:', language);
    console.log('UserManagementPage - i18n language:', i18n.language);
    console.log('UserManagementPage - Translation test after change:', t('user.menu.overview'));
  }, [language, i18n.language, t]);
  
  // Wait for i18n to be ready
  if (!ready || !i18n.isInitialized || i18n.language !== language) {
    console.log('UserManagementPage - Waiting for i18n to be ready:', { ready, isInitialized: i18n.isInitialized, i18nLanguage: i18n.language, contextLanguage: language });
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" />
      </div>
    );
  }

  const [collapsed, setCollapsed] = useState(true);
  const [selectedKey, setSelectedKey] = useState('overview');
  const [moduleDetail, setModuleDetail] = useState<UserManagementModuleDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [userStats] = useState<UserStats>({
    total_users: 12,
    active_users: 8,
    roles: 4,
    permissions: 15
  });
  const [recentActivities] = useState<ActivityItem[]>([
    {
      time: t('user.activity.time5min'),
      description: t('user.activity.newUserCreated'),
      type: 'success'
    },
    {
      time: t('user.activity.time15min'),
      description: t('user.activity.roleUpdated'),
      type: 'info'
    },
    {
      time: t('user.activity.time1hour'),
      description: t('user.activity.adminLogin'),
      type: 'warning'
    },
    {
      time: t('user.activity.time2hour'),
      description: t('user.activity.moduleInstalled'),
      type: 'success'
    }
  ]);

  // States cho Users
  const [users, setUsers] = useState<User[]>([]);
  const [usersLoading, setUsersLoading] = useState(false);
  const [userModalVisible, setUserModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [userForm] = Form.useForm();
  const [usernameChecking, setUsernameChecking] = useState(false);
  const [emailChecking, setEmailChecking] = useState(false);
  const [usernameAvailable, setUsernameAvailable] = useState<boolean | null>(null);
  const [emailAvailable, setEmailAvailable] = useState<boolean | null>(null);
  
  // States cho search và refresh
  const [searchText, setSearchText] = useState('');
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  
  // States cho delete confirmation
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);

  // Roles state
  const [roles, setRoles] = useState<Role[]>([]);
  const [rolesLoading, setRolesLoading] = useState(false);

  // UserRoles state
  const [userRoles, setUserRoles] = useState<UserRole[]>([]);
  const [userRolesLoading, setUserRolesLoading] = useState(false);
  const [filteredUserRoles, setFilteredUserRoles] = useState<UserRole[]>([]);
  const [deleteUserRoleModalVisible, setDeleteUserRoleModalVisible] = useState(false);
  const [userRoleToDelete, setUserRoleToDelete] = useState<UserRole | null>(null);

  // Add UserRole state
  const [addUserRoleModalVisible, setAddUserRoleModalVisible] = useState(false);
  const [addUserRoleLoading, setAddUserRoleLoading] = useState(false);
  const [addUserRoleForm] = Form.useForm();
  const [selectedUserId, setSelectedUserId] = useState<string | undefined>(undefined);
  const [selectedRoleId, setSelectedRoleId] = useState<number | undefined>(undefined);

  // Popup Add Role
  const [addRoleModalVisible, setAddRoleModalVisible] = useState(false);
  const [addRoleLoading, setAddRoleLoading] = useState(false);
  const [addRoleForm] = Form.useForm();
  const [roleNameChecking, setRoleNameChecking] = useState(false);
  const [roleNameAvailable, setRoleNameAvailable] = useState<boolean | null>(null);
  const [addRoleAction, setAddRoleAction] = useState<'save' | 'save_and_new'>('save');
  
  // Popup Edit Role
  const [editRoleModalVisible, setEditRoleModalVisible] = useState(false);
  const [editRoleLoading, setEditRoleLoading] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [editRoleForm] = Form.useForm();
  const [editRoleNameChecking, setEditRoleNameChecking] = useState(false);
  const [editRoleNameAvailable, setEditRoleNameAvailable] = useState<boolean | null>(null);
  
  // Popup Delete Role
  const [deleteRoleModalVisible, setDeleteRoleModalVisible] = useState(false);
  const [roleToDelete, setRoleToDelete] = useState<Role | null>(null);
  
  // Search và filter roles
  const [roleSearchText, setRoleSearchText] = useState('');
  const [filteredRoles, setFilteredRoles] = useState<Role[]>([]);
  
  // Sort roles
  const [sortedInfo, setSortedInfo] = useState<any>({
    columnKey: 'id',
    order: 'descend'
  });

  // Sort user roles
  const [userRoleSortedInfo, setUserRoleSortedInfo] = useState<any>({
    columnKey: 'username',
    order: 'ascend'
  });

  // Kiểm tra xem có phải tab mới không
  const isNewTab = window.opener || window.history.length <= 1;

  // Kiểm tra xem có phải mobile không
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  // Menu items
  const menuItems = useMemo(() => [
    {
      key: 'overview',
      icon: <DashboardOutlined />,
      label: t('user.menu.overview'),
    },
    {
      key: 'users',
      icon: <UserOutlined />,
      label: t('user.menu.users'),
    },
    {
      key: 'roles',
      icon: <SafetyOutlined />,
      label: t('user.menu.roles'),
    },
    {
      key: 'user-roles',
      icon: <TeamOutlined />,
      label: t('user.menu.userRoles'),
    },
    {
      key: 'role-permissions',
      icon: <SafetyOutlined />,
      label: 'Role permissions',
    }
  ], [t, language]);

  // Fetch module details
  const fetchModuleDetails = async () => {
    try {
      const response = await axios.get('http://localhost:8000/api/internal/modules/user_management', {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      const moduleData = response.data;
      setModuleDetail({
        name: moduleData.name || 'user_management',
        summary: moduleData.summary || t('user.module.summary'),
        version: moduleData.version || '1.0',
        state: moduleData.is_installed ? 'installed' : 'uninstalled',
        author: moduleData.author || 'Metis AI',
        category: moduleData.category || 'Core',
        description: moduleData.description || t('user.module.description'),
        is_core_module: moduleData.is_core_module || true,
        auto_install: moduleData.auto_install || true,
        hidden: moduleData.hidden || false,
        is_installed: moduleData.is_installed || true
      });
    } catch (error: any) {
      logError('fetchModuleDetails', error);
      message.error(t('user.error.loadModule'));
      
      // Set default data if API fails
      setModuleDetail({
        name: 'user_management',
        summary: t('user.module.summary'),
        version: '1.0',
        state: 'installed',
        author: 'Metis AI',
        category: 'Core',
        description: t('user.module.description'),
        is_core_module: true,
        auto_install: true,
        hidden: false,
        is_installed: true
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch users
  const fetchUsers = async () => {
    try {
      setUsersLoading(true);
      const response = await axios.get('http://localhost:8000/api/user-management/users', {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      const usersData = response.data;
      setUsers(usersData);
      setFilteredUsers(usersData);
    } catch (error: any) {
      logError('fetchUsers', error);
      message.error(t('user.error.loadUsers'));
    } finally {
      setUsersLoading(false);
    }
  };

  // Fetch roles
  const fetchRoles = async () => {
    try {
      setRolesLoading(true);
      const response = await axios.get('http://localhost:8000/api/user-management/roles', {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      const rolesData = response.data;
      setRoles(rolesData);
      setFilteredRoles(rolesData);
    } catch (error: any) {
      logError('fetchRoles', error);
      message.error(t('user.error.loadRoles'));
    } finally {
      setRolesLoading(false);
    }
  };

  // Fetch user roles
  const fetchUserRoles = async () => {
    try {
      setUserRolesLoading(true);
      const response = await axios.get('http://localhost:8000/api/user-management/user-roles', {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      const userRolesData = response.data;
      setUserRoles(userRolesData);
      setFilteredUserRoles(userRolesData);
    } catch (error: any) {
      logError('fetchUserRoles', error);
      message.error(t('user.error.loadUserRoles'));
    } finally {
      setUserRolesLoading(false);
    }
  };

  // Handle search
  const handleSearch = (value: string) => {
    setSearchText(value);
    if (!value.trim()) {
      setFilteredUsers(users);
    } else {
      const filtered = users.filter(user => 
        user.username.toLowerCase().includes(value.toLowerCase()) ||
        user.full_name.toLowerCase().includes(value.toLowerCase()) ||
        user.email.toLowerCase().includes(value.toLowerCase()) ||
        (user.phone_number && user.phone_number.includes(value))
      );
      setFilteredUsers(filtered);
    }
  };

  // Handle refresh
  const handleRefresh = async () => {
    try {
      message.loading(t('user.notification.loadingData'), 1);
      
      await Promise.all([
        fetchUsers(),
        fetchRoles(),
        fetchUserRoles()
      ]);
      
      message.success(t('user.notification.dataRefreshed'));
    } catch (error: any) {
      logError('handleRefresh', error);
      message.error(t('user.error.refreshData'));
    }
  };

  // Update filtered users when users change
  useEffect(() => {
    if (searchText.trim()) {
      const filtered = users.filter(user => 
        user.username.toLowerCase().includes(searchText.toLowerCase()) ||
        user.full_name.toLowerCase().includes(searchText.toLowerCase()) ||
        user.email.toLowerCase().includes(searchText.toLowerCase()) ||
        (user.phone_number && user.phone_number.includes(searchText))
      );
      setFilteredUsers(filtered);
    } else {
      setFilteredUsers(users);
    }
  }, [users, searchText]);

  // Toggle user active status
  const handleToggleUserActive = async (userId: string, currentStatus: boolean) => {
    try {
      await axios.put(`http://localhost:8000/api/user-management/users/${userId}`, {
        is_active: !currentStatus
      }, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      message.success(t('user.notification.userStatusUpdated', { 
        action: !currentStatus ? t('user.notification.activated') : t('user.notification.deactivated') 
      }));
      fetchUsers(); // Refresh data
    } catch (error: any) {
      logError('handleToggleUserActive', error);
      message.error(t('user.error.updateUserStatus'));
    }
  };

  // Handle delete click
  const handleDeleteClick = (user: User) => {
    setUserToDelete(user);
    setDeleteModalVisible(true);
  };

  // Handle confirm delete
  const handleConfirmDelete = async () => {
    if (!userToDelete) return;
    
    try {
      await handleDeleteUser(userToDelete.id);
      setDeleteModalVisible(false);
      setUserToDelete(null);
    } catch (error: any) {
      logError('handleConfirmDelete', error);
    }
  };

  // Handle cancel delete
  const handleCancelDelete = () => {
    setDeleteModalVisible(false);
    setUserToDelete(null);
  };

  // Delete user
  const handleDeleteUser = async (userId: string) => {
    try {
      await axios.delete(`http://localhost:8000/api/user-management/users/${userId}`, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      message.success(t('user.notification.userDeleted'));
      fetchUsers(); // Refresh data
    } catch (error: any) {
      logError('handleDeleteUser', error);
      message.error(t('user.error.deleteUser'));
      throw error;
    }
  };

  // Edit user
  const handleEditUser = (user: User) => {
    setEditingUser(user);
    userForm.setFieldsValue({
      username: user.username,
      full_name: user.full_name,
      email: user.email,
      phone_number: user.phone_number
    });
    setUserModalVisible(true);
  };

  // Handle save user
  const handleSaveUser = async (values: any, action: 'save' | 'save_and_new' = 'save') => {
    try {
      if (editingUser) {
        // Update existing user
        await axios.put(`http://localhost:8000/api/user-management/users/${editingUser.id}`, values, {
          timeout: 10000,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });
        message.success(t('user.notification.userUpdated'));
      } else {
        // Create new user
        await axios.post('http://localhost:8000/api/user-management/users', values, {
          timeout: 10000,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });
        message.success(t('user.notification.userCreated'));
      }
      
      if (action === 'save') {
        setUserModalVisible(false);
        setEditingUser(null);
        userForm.resetFields();
      } else {
        // Save and new - reset form for new entry
        userForm.resetFields();
        setEditingUser(null);
      }
      
      fetchUsers(); // Refresh data
    } catch (error: any) {
      logError('handleSaveUser', error);
      
      // Xử lý lỗi validation từ backend
      if (error.response?.status === 422) {
        const validationErrors = error.response?.data?.detail;
        if (Array.isArray(validationErrors)) {
          // Hiển thị từng lỗi validation
          validationErrors.forEach((err: any) => {
            message.error(`${err.loc?.join('.')}: ${err.msg}`);
          });
        } else if (typeof validationErrors === 'string') {
          message.error(validationErrors);
        } else {
          message.error(t('user.error.validationError'));
        }
      } else if (error.response?.status === 400) {
        // Lỗi business logic (username/email đã tồn tại)
        const errorMessage = error.response?.data?.detail || t('user.error.saveUser');
        message.error(errorMessage);
      } else {
        // Lỗi khác
        const errorMessage = error.response?.data?.detail || t('user.error.saveUser');
        message.error(errorMessage);
      }
    }
  };

  // Handle form submit
  const handleFormSubmit = async (values: any) => {
    await handleSaveUser(values, 'save');
  };

  // Handle save and new
  const handleSaveAndNew = async () => {
    try {
      const values = await userForm.validateFields();
      await handleSaveUser(values, 'save_and_new');
    } catch (error: any) {
      logError('handleSaveAndNew', error);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    setUserModalVisible(false);
    setEditingUser(null);
    userForm.resetFields();
    setUsernameAvailable(null);
    setEmailAvailable(null);
  };

  // Check username availability
  const checkUsername = async (username: string) => {
    if (!username || username.length < 3) return;
    
    setUsernameChecking(true);
    try {
      const response = await axios.get(`http://localhost:8000/api/user-management/users/check-username/${username}`, {
        timeout: 5000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      setUsernameAvailable(response.data.available);
    } catch (error: any) {
      logError('checkUsername', error);
      setUsernameAvailable(null);
    } finally {
      setUsernameChecking(false);
    }
  };

  // Check email availability
  const checkEmail = async (email: string) => {
    if (!email || !email.includes('@')) return;
    
    setEmailChecking(true);
    try {
      const response = await axios.get(`http://localhost:8000/api/user-management/users/check-email/${email}`, {
        timeout: 5000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      setEmailAvailable(response.data.available);
    } catch (error: any) {
      logError('checkEmail', error);
      setEmailAvailable(null);
    } finally {
      setEmailChecking(false);
    }
  };

  // Handle field validation with debounce
  const handleFieldValidation = (fieldName: string, value: string) => {
    // Clear validation status for availability checks
    if (fieldName === 'username') {
      if (!value || value.length < 3) {
        setUsernameAvailable(null);
      } else if (!editingUser) {
        checkUsername(value);
      }
    } else if (fieldName === 'email') {
      if (!value || !value.includes('@')) {
        setEmailAvailable(null);
      } else if (!editingUser) {
        checkEmail(value);
      }
    }
    
    // Trigger form validation
    userForm.validateFields([fieldName]);
    
    // Special handling for confirmPassword - also validate password field
    if (fieldName === 'confirmPassword') {
      userForm.validateFields(['password']);
    }
  };

  // Create new user
  const handleCreateUser = async (values: any) => {
    try {
      const response = await axios.post('http://localhost:8000/api/user-management/users', values, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      // Add new user to local state
      const updatedUsers = [...users, response.data];
      setUsers(updatedUsers);
      message.success('User đã được tạo thành công');
      
      return response.data;
    } catch (error: any) {
      logError('handleCreateUser', error);
      const errorMessage = error.response?.data?.detail || 'Không thể tạo user';
      message.error(errorMessage);
      throw error;
    }
  };

  // Table columns for users
  const userColumns = useMemo(() => [
    {
      title: t('user.users.table.username'),
      dataIndex: 'username',
      key: 'username',
      width: 120,
    },
    {
      title: t('user.users.table.fullName'),
      dataIndex: 'full_name',
      key: 'full_name',
      width: 150,
    },
    {
      title: t('user.users.table.email'),
      dataIndex: 'email',
      key: 'email',
      width: 200,
    },
    {
      title: t('user.users.table.phoneNumber'),
      dataIndex: 'phone_number',
      key: 'phone_number',
      width: 120,
    },
    {
      title: t('user.users.table.status'),
      dataIndex: 'is_active',
      key: 'is_active',
      width: 100,
      render: (isActive: boolean, record: User) => (
        <Switch
          checked={isActive}
          onChange={() => handleToggleUserActive(record.id, isActive)}
          checkedChildren={t('user.users.table.active')}
          unCheckedChildren={t('user.users.table.inactive')}
        />
      ),
    },
    {
      title: t('user.users.table.actions'),
      key: 'actions',
      width: 100,
      render: (_: any, record: User) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditUser(record)}
            title={t('user.users.table.edit')}
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteClick(record)}
            title={t('user.users.table.delete')}
          />
        </Space>
      ),
    },
  ], [t, language]);

  // Toggle role activated status
  const handleToggleRoleActivated = async (roleId: number, current: boolean) => {
    try {
      await axios.put(`http://localhost:8000/api/user-management/roles/${roleId}`, {
        is_activated: !current
      }, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      message.success(t('user.notification.roleStatusUpdated'));
      fetchRoles(); // Refresh data
    } catch (error: any) {
      logError('handleToggleRoleActivated', error);
      message.error(t('user.error.updateRoleStatus'));
    }
  };

  // Check role name availability
  const checkRoleName = async (name: string) => {
    if (!name) {
      setRoleNameAvailable(null);
      return false;
    }
    
    // Clear previous state before checking
    setRoleNameAvailable(null);
    
    try {
      setRoleNameChecking(true);
      const response = await axios.get(`http://localhost:8000/api/user-management/roles/check-name/${name}`, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      const isAvailable = response.data.available;
      setRoleNameAvailable(isAvailable);
      return isAvailable;
    } catch (error: any) {
      logError('checkRoleName', error);
      message.error(t('user.error.checkRoleName'));
      setRoleNameAvailable(null);
      return false;
    } finally {
      setRoleNameChecking(false);
    }
  };

  // Reset add role form and state
  const resetAddRoleForm = () => {
    addRoleForm.resetFields();
    setRoleNameAvailable(null);
    setRoleNameChecking(false);
    setAddRoleAction('save');
  };

  // Add role
  const handleAddRole = async (values: any, action: 'save' | 'save_and_new') => {
    try {
      setAddRoleLoading(true);
      
      // Use current validation state instead of re-checking
      if (roleNameAvailable === false) {
        message.error(t('user.error.roleNameExists'));
        return;
      }
      
      // If validation state is null or checking, do a final check
      if (roleNameAvailable === null || roleNameChecking) {
        const isAvailable = await checkRoleName(values.name);
        if (!isAvailable) {
          message.error(t('user.error.roleNameExists'));
          return;
        }
      }
      
      await axios.post('http://localhost:8000/api/user-management/roles', values, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      message.success(t('user.notification.roleCreated'));
      
      if (action === 'save') {
        setAddRoleModalVisible(false);
        resetAddRoleForm();
      } else {
        // Save and new - reset form for new entry
        resetAddRoleForm();
      }
      
      fetchRoles(); // Refresh data
    } catch (error: any) {
      logError('handleAddRole', error);
      const errorMessage = error.response?.data?.detail || t('user.error.createRole');
      message.error(errorMessage);
    } finally {
      setAddRoleLoading(false);
    }
  };

  // Edit role functions
  const handleEditRole = (role: Role) => {
    setEditingRole(role);
    editRoleForm.setFieldsValue({
      name: role.name,
      description: role.description,
      is_activated: role.is_activated
    });
    setEditRoleNameAvailable(null);
    setEditRoleModalVisible(true);
  };

  const checkEditRoleName = async (name: string, currentRoleId: number) => {
    setEditRoleNameChecking(true);
    try {
      // Kiểm tra trùng lặp trên client (loại trừ role hiện tại)
      if (roles.some(r => r.id !== currentRoleId && r.name.trim().toLowerCase() === name.trim().toLowerCase())) {
        setEditRoleNameAvailable(false);
        setEditRoleNameChecking(false);
        return false;
      }
      setEditRoleNameAvailable(true);
      setEditRoleNameChecking(false);
      return true;
    } catch {
      setEditRoleNameAvailable(null);
      setEditRoleNameChecking(false);
      return false;
    }
  };

  // Save edit role
  const handleSaveEditRole = async (values: any) => {
    if (!editingRole) return;
    
    try {
      setEditRoleLoading(true);
      
      // Check if role name already exists (excluding current role)
      const isAvailable = await checkEditRoleName(values.name, editingRole.id);
      if (!isAvailable) {
        message.error(t('user.error.roleNameExists'));
        return;
      }
      
      await axios.put(`http://localhost:8000/api/user-management/roles/${editingRole.id}`, values, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      message.success(t('user.notification.roleUpdated'));
      setEditRoleModalVisible(false);
      setEditingRole(null);
      editRoleForm.resetFields();
      fetchRoles(); // Refresh data
    } catch (error: any) {
      logError('handleSaveEditRole', error);
      const errorMessage = error.response?.data?.detail || t('user.error.updateRole');
      message.error(errorMessage);
    } finally {
      setEditRoleLoading(false);
    }
  };

  // Delete role functions
  const handleDeleteRoleClick = (role: Role) => {
    setRoleToDelete(role);
    setDeleteRoleModalVisible(true);
  };

  // Confirm delete role
  const handleConfirmDeleteRole = async () => {
    if (!roleToDelete) return;
    
    try {
      await axios.delete(`http://localhost:8000/api/user-management/roles/${roleToDelete.id}`, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      message.success(t('user.notification.roleDeleted'));
      setDeleteRoleModalVisible(false);
      setRoleToDelete(null);
      fetchRoles(); // Refresh data
    } catch (error: any) {
      logError('handleConfirmDeleteRole', error);
      message.error(t('user.error.deleteRole'));
    }
  };

  // Cancel delete role
  const handleCancelDeleteRole = () => {
    setDeleteRoleModalVisible(false);
    setRoleToDelete(null);
  };

  // Search và filter roles
  const handleRoleSearch = (value: string) => {
    setRoleSearchText(value);
    if (!value.trim()) {
      setFilteredRoles(roles);
    } else {
      const filtered = roles.filter(role => 
        role.name.toLowerCase().includes(value.toLowerCase()) ||
        (role.description && role.description.toLowerCase().includes(value.toLowerCase()))
      );
      setFilteredRoles(filtered);
    }
  };

  // Refresh roles
  const handleRefreshRoles = async () => {
    try {
      setRolesLoading(true);
      await fetchRoles();
      message.success(t('user.notification.rolesRefreshed'));
    } catch (error: any) {
      logError('handleRefreshRoles', error);
      message.error(t('user.error.refreshRoles'));
    } finally {
      setRolesLoading(false);
    }
  };

  // Update filtered roles when roles change
  useEffect(() => {
    if (roleSearchText.trim()) {
      const filtered = roles.filter(role => 
        role.name.toLowerCase().includes(roleSearchText.toLowerCase()) ||
        (role.description && role.description.toLowerCase().includes(roleSearchText.toLowerCase()))
      );
      setFilteredRoles(filtered);
    } else {
      setFilteredRoles(roles);
    }
  }, [roles, roleSearchText]);

  // Handle table sort
  const handleTableChange = (_pagination: any, _filters: any, sorter: any) => {
    setSortedInfo(sorter);
  };

  // Get sorted and filtered roles
  const getSortedRoles = () => {
    let sortedRoles = [...filteredRoles];
    
    if (sortedInfo.columnKey) {
      sortedRoles.sort((a: any, b: any) => {
        let aValue = a[sortedInfo.columnKey];
        let bValue = b[sortedInfo.columnKey];
        
        // Handle string comparison
        if (typeof aValue === 'string') {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }
        
        if (sortedInfo.order === 'ascend') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });
    }
    
    return sortedRoles;
  };

  // Handle user role table sort
  const handleUserRoleTableChange = (_pagination: any, _filters: any, sorter: any) => {
    setUserRoleSortedInfo(sorter);
  };

  // Get sorted user roles
  const getSortedUserRoles = () => {
    if (!userRoleSortedInfo.columnKey || !userRoleSortedInfo.order) {
      return filteredUserRoles;
    }

    return [...filteredUserRoles].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (userRoleSortedInfo.columnKey) {
        case 'user_id':
          aValue = a.user_id;
          bValue = b.user_id;
          break;
        case 'username':
          aValue = a.username.toLowerCase();
          bValue = b.username.toLowerCase();
          break;
        case 'full_name':
          aValue = a.full_name.toLowerCase();
          bValue = b.full_name.toLowerCase();
          break;
        case 'role_id':
          aValue = a.role_id;
          bValue = b.role_id;
          break;
        case 'role_name':
          aValue = a.role_name.toLowerCase();
          bValue = b.role_name.toLowerCase();
          break;
        default:
          return 0;
      }

      if (userRoleSortedInfo.order === 'ascend') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  };

  // Handle refresh user roles
  const handleRefreshUserRoles = async () => {
    try {
      setUserRolesLoading(true);
      await fetchUserRoles();
      message.success(t('user.notification.userRolesRefreshed'));
    } catch (error: any) {
      logError('handleRefreshUserRoles', error);
      message.error(t('user.error.refreshUserRoles'));
    } finally {
      setUserRolesLoading(false);
    }
  };

  // Handle user role search
  const handleUserRoleSearch = (value: string) => {
    if (!value.trim()) {
      setFilteredUserRoles(userRoles);
    } else {
      const filtered = userRoles.filter(userRole =>
        userRole.username.toLowerCase().includes(value.toLowerCase()) ||
        userRole.full_name.toLowerCase().includes(value.toLowerCase()) ||
        userRole.role_name.toLowerCase().includes(value.toLowerCase())
      );
      setFilteredUserRoles(filtered);
    }
  };

  // Handle delete user role
  const handleDeleteUserRoleClick = (userRole: UserRole) => {
    setUserRoleToDelete(userRole);
    setDeleteUserRoleModalVisible(true);
  };

  // Handle confirm delete user role
  const handleConfirmDeleteUserRole = async () => {
    if (!userRoleToDelete) return;
    
    try {
      await axios.delete(`http://localhost:8000/api/user-management/user-roles/${userRoleToDelete.user_id}/${userRoleToDelete.role_id}`, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      message.success(t('user.notification.userRoleDeleted'));
      await fetchUserRoles();
      setDeleteUserRoleModalVisible(false);
      setUserRoleToDelete(null);
    } catch (error: any) {
      logError('handleConfirmDeleteUserRole', error);
      message.error(t('user.error.deleteUserRole'));
    }
  };

  // Handle cancel delete user role
  const handleCancelDeleteUserRole = () => {
    setDeleteUserRoleModalVisible(false);
    setUserRoleToDelete(null);
  };

  // Kiểm tra user role đã tồn tại
  const checkUserRoleExists = (userId: string, roleId: number): boolean => {
    return userRoles.some(userRole => 
      userRole.user_id === userId && userRole.role_id === roleId
    );
  };

  // Lấy thông tin user và role để hiển thị trong cảnh báo
  const getUserRoleInfo = (userId: string, roleId: number) => {
    const user = users.find(u => u.id === userId);
    const role = roles.find(r => r.id === roleId);
    return {
      username: user?.username || 'Unknown',
      fullName: user?.full_name || 'Unknown',
      roleName: role?.name || 'Unknown'
    };
  };

  // State cho lỗi role đã tồn tại
  const [userRoleExistsError, setUserRoleExistsError] = useState<string | null>(null);

  const handleAddUserRole = async (action: 'save' | 'save_and_new') => {
    if (!selectedUserId || !selectedRoleId) {
      message.error(t('user.error.selectUserAndRole'));
      return;
    }

    // Kiểm tra user role đã tồn tại
    if (checkUserRoleExists(selectedUserId, selectedRoleId)) {
      const { username, fullName, roleName } = getUserRoleInfo(selectedUserId, selectedRoleId);
      setUserRoleExistsError(`User "${username} - ${fullName}" đã có vai trò "${roleName}".`);
      addUserRoleForm.setFields([
        {
          name: 'role_id',
          errors: [`User "${username} - ${fullName}" đã có vai trò "${roleName}".`],
        },
      ]);
      return;
    } else {
      setUserRoleExistsError(null);
      addUserRoleForm.setFields([
        {
          name: 'role_id',
          errors: [],
        },
      ]);
    }

    setAddUserRoleLoading(true);
    try {
      await axios.post('http://localhost:8000/api/user-management/user-roles', {
        user_id: selectedUserId,
        role_id: selectedRoleId
      });

      message.success('Đã thêm user role thành công');
      
      // Refresh danh sách user roles
      await fetchUserRoles();

      if (action === 'save_and_new') {
        // Reset form để thêm mới
        setSelectedUserId(undefined);
        setSelectedRoleId(undefined);
        addUserRoleForm.resetFields();
        setUserRoleExistsError(null);
      } else {
        // Đóng modal
        setAddUserRoleModalVisible(false);
        setSelectedUserId(undefined);
        setSelectedRoleId(undefined);
        addUserRoleForm.resetFields();
        setUserRoleExistsError(null);
      }
    } catch (error: any) {
      logError('Error adding user role:', error);
      if (error.response?.data?.detail) {
        message.error(error.response.data.detail);
      } else {
        message.error('Không thể thêm user role');
      }
    } finally {
      setAddUserRoleLoading(false);
    }
  };

  const handleSaveUserRole = async () => {
    await handleAddUserRole('save');
  };

  const handleSaveAndNewUserRole = async () => {
    await handleAddUserRole('save_and_new');
  };

  const handleCancelAddUserRole = () => {
    setAddUserRoleModalVisible(false);
    setSelectedUserId(undefined);
    setSelectedRoleId(undefined);
    addUserRoleForm.resetFields();
  };

  // Reset lỗi khi chọn lại user hoặc role
  useEffect(() => {
    setUserRoleExistsError(null);
    addUserRoleForm.setFields([
      {
        name: 'role_id',
        errors: [],
      },
    ]);
  }, [selectedUserId, selectedRoleId]);

  useEffect(() => {
    const fetchData = async () => {
      await fetchModuleDetails();
      await fetchUsers();
      await fetchRoles();
    };
    fetchData();
  }, []);

  // Hiển thị cảnh báo real-time khi chọn User hoặc Role đã tồn tại
  useEffect(() => {
    if (selectedUserId && selectedRoleId) {
      if (checkUserRoleExists(selectedUserId, selectedRoleId)) {
        const { username, fullName, roleName } = getUserRoleInfo(selectedUserId, selectedRoleId);
        message.warning(
          t('user.warning.userRoleExists', { username, fullName, roleName }),
          5
        );
      }
    }
  }, [selectedUserId, selectedRoleId, userRoles, users, roles]);

  useEffect(() => {
    if (selectedKey === 'user-roles') {
      fetchUserRoles();
    }
  }, [selectedKey]);

  useEffect(() => {
    // Cập nhật title của tab
    document.title = 'User Management Module - Metis Platform';
    
    // Thêm event listener để refresh data khi tab được focus
    const handleFocus = () => {
      console.log('DEBUG: Tab focused, refreshing data...');
      fetchModuleDetails();
      if (selectedKey === 'users') {
        fetchUsers();
      }
      if (selectedKey === 'roles') {
        fetchRoles();
      }
      if (selectedKey === 'user-roles') {
        fetchUserRoles();
      }
    };
    
    // Thêm event listener để xử lý trước khi đóng tab
    const handleBeforeUnload = () => {
      // Có thể thêm logic để lưu trạng thái nếu cần
      console.log('DEBUG: Tab is being closed...');
    };
    
    // Xử lý resize window
    const handleResize = () => {
      const newIsMobile = window.innerWidth <= 768;
      setIsMobile(newIsMobile);
      
      // Luôn giữ collapsed = true cho cả mobile và desktop
      setCollapsed(true);
    };
    
    window.addEventListener('focus', handleFocus);
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('resize', handleResize);
    };
  }, [selectedKey]);

  const handleMenuClick = (e: any) => {
    console.log('DEBUG: handleMenuClick, key:', e.key);
    setSelectedKey(e.key);
    // Trên mobile, đóng sidebar sau khi click menu
    if (isMobile) {
      setCollapsed(true);
    }
    
    // Load users data when users menu is selected
    if (e.key === 'users') {
      console.log('DEBUG: Loading users...');
      fetchUsers();
    }
    
    // Load roles data when roles menu is selected
    if (e.key === 'roles') {
      console.log('DEBUG: Loading roles...');
      fetchRoles();
    }
  };

  // Hàm mở AppsPage trong tab mới
  const handleOpenAppsPage = () => {
    window.open('/apps', '_blank');
  };

  // Hàm toggle sidebar
  const handleToggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  // Hàm đóng sidebar khi click overlay
  const handleOverlayClick = () => {
    if (!collapsed) {
      setCollapsed(true);
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <ClockCircleOutlined style={{ color: '#52c41a' }} />;
      case 'warning':
        return <ClockCircleOutlined style={{ color: '#faad14' }} />;
      case 'error':
        return <ClockCircleOutlined style={{ color: '#f5222d' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#1890ff' }} />;
    }
  };

  const renderOverviewContent = () => {
    return (
      <div>
        {/* Module Overview Card */}
        <Card className="user-management-overview-card" style={{ marginBottom: '24px' }}>
          <Row gutter={[24, 16]}>
            <Col xs={24} md={8}>
              <div style={{ textAlign: 'center' }}>
                <Avatar 
                  size={80} 
                  icon={<UserOutlined />} 
                  className="user-management-overview-avatar"
                  style={{ 
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    fontSize: '32px'
                  }}
                />
                <Title level={3} style={{ margin: '16px 0 8px 0' }}>
                  {moduleDetail?.summary}
                </Title>
                <Text type="secondary" style={{ fontSize: '14px', fontFamily: 'monospace' }}>
                  {moduleDetail?.name}
                </Text>
              </div>
            </Col>
            <Col xs={24} md={16}>
              <div className="user-management-overview-stats">
                <Space size="large" wrap>
                  <div>
                    <Text type="secondary">{t('user.overview.version')}</Text>
                    <div><Text strong>v{moduleDetail?.version}</Text></div>
                  </div>
                  <div>
                    <Text type="secondary">{t('user.overview.author')}</Text>
                    <div><Text strong>{moduleDetail?.author}</Text></div>
                  </div>
                  <div>
                    <Text type="secondary">{t('user.overview.status')}</Text>
                    <div>
                      <Tag color={moduleDetail?.state === 'installed' ? 'green' : 'orange'}>
                        {moduleDetail?.state === 'installed' ? t('user.overview.installed') : t('user.overview.notInstalled')}
                      </Tag>
                    </div>
                  </div>
                </Space>
              </div>
              <div className="user-management-overview-tags" style={{ marginTop: '16px' }}>
                <Space wrap>
                  <Tag color="blue">{moduleDetail?.category}</Tag>
                  {moduleDetail?.is_core_module && (
                    <Tag color="red">{t('user.overview.coreModule')}</Tag>
                  )}
                  {moduleDetail?.auto_install && (
                    <Tag color="green">{t('user.overview.autoInstall')}</Tag>
                  )}
                </Space>
              </div>
              <Paragraph className="user-management-overview-description" style={{ marginTop: '16px' }}>
                {moduleDetail?.description}
              </Paragraph>
            </Col>
          </Row>
        </Card>

        {/* Statistics and Activities */}
        <Row gutter={[24, 24]}>
          <Col xs={24} md={12}>
            <Card title={t('user.overview.userStats')} size="small" className="user-management-card-responsive">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div className="user-management-stats-card">
                    <Statistic
                      title={t('user.overview.totalUsers')}
                      value={userStats.total_users}
                      valueStyle={{ color: '#1890ff', fontSize: '24px' }}
                      prefix={<UserOutlined />}
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="user-management-stats-card">
                    <Statistic
                      title={t('user.overview.activeUsers')}
                      value={userStats.active_users}
                      valueStyle={{ color: '#52c41a', fontSize: '24px' }}
                      prefix={<UserOutlined />}
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="user-management-stats-card">
                    <Statistic
                      title={t('user.overview.roles')}
                      value={userStats.roles}
                      valueStyle={{ color: '#faad14', fontSize: '24px' }}
                      prefix={<SafetyOutlined />}
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="user-management-stats-card">
                    <Statistic
                      title={t('user.overview.permissions')}
                      value={userStats.permissions}
                      valueStyle={{ color: '#722ed1', fontSize: '24px' }}
                      prefix={<SafetyOutlined />}
                    />
                  </div>
                </Col>
              </Row>
            </Card>
          </Col>
          <Col xs={24} md={12}>
            <Card title={t('user.overview.recentActivities')} size="small" className="user-management-card-responsive">
              <div className="user-management-activity-card">
                {recentActivities.map((activity, index) => (
                  <div key={index} className="user-management-activity-item">
                    <div style={{ display: 'flex', alignItems: 'flex-start', gap: '8px' }}>
                      {getActivityIcon(activity.type)}
                      <div style={{ flex: 1 }}>
                        <Text className="user-management-activity-time">{activity.time}</Text>
                        <div style={{ marginTop: '4px' }}>{activity.description}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  const renderUsersContent = () => {
    return (
      <div>
        <Card 
          title={t('user.users.title')} 
          extra={
            <Space>
              <Input.Search
                placeholder={t('user.users.searchPlaceholder')}
                allowClear
                style={{ width: 250 }}
                onSearch={handleSearch}
                onChange={(e) => handleSearch(e.target.value)}
                value={searchText}
              />
              <Button 
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={usersLoading}
                title={t('user.users.refreshData')}
              />
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => {
                  setEditingUser(null);
                  userForm.resetFields();
                  setUsernameAvailable(null);
                  setEmailAvailable(null);
                  setUserModalVisible(true);
                }}
              >
                {t('user.users.addUser')}
              </Button>
            </Space>
          }
        >
          <Table
            columns={userColumns}
            dataSource={filteredUsers}
            rowKey="id"
            loading={usersLoading}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => t('user.users.pagination', { start: range[0], end: range[1], total }),
            }}
            scroll={{ x: 800 }}
          />
        </Card>

        {/* User Edit Modal */}
        <Modal
          title={editingUser ? t('user.users.editUser') : t('user.users.addNewUser')}
          open={userModalVisible}
          onCancel={handleCancel}
          footer={null}
          width={700}
        >
          <Form
            form={userForm}
            layout="vertical"
            onFinish={handleFormSubmit}
            initialValues={{
              is_active: true
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="username"
                  label={t('user.users.form.username')}
                  rules={[
                    { required: true, message: t('user.users.form.usernameRequired') },
                    { min: 3, message: t('user.users.form.usernameMinLength') },
                    { max: 50, message: t('user.users.form.usernameMaxLength') },
                    { pattern: /^[a-zA-Z0-9_]+$/, message: t('user.users.form.usernameFormat') }
                  ]}
                  validateStatus={usernameChecking ? 'validating' : usernameAvailable === false ? 'error' : usernameAvailable === true ? 'success' : undefined}
                  help={usernameChecking ? t('user.users.form.checking') : usernameAvailable === false ? t('user.users.form.usernameExists') : usernameAvailable === true ? t('user.users.form.usernameAvailable') : undefined}
                >
                  <Input 
                    placeholder={t('user.users.form.usernamePlaceholder')} 
                    onChange={(e) => {
                      handleFieldValidation('username', e.target.value);
                    }}
                    onBlur={(e) => {
                      handleFieldValidation('username', e.target.value);
                    }}
                    disabled={editingUser !== null}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="full_name"
                  label={t('user.users.form.fullName')}
                  rules={[
                    { required: true, message: t('user.users.form.fullNameRequired') },
                    { min: 2, message: t('user.users.form.fullNameMinLength') },
                    { max: 100, message: t('user.users.form.fullNameMaxLength') }
                  ]}
                >
                  <Input 
                    placeholder={t('user.users.form.fullNamePlaceholder')} 
                    onChange={(e) => {
                      handleFieldValidation('full_name', e.target.value);
                    }}
                    onBlur={(e) => {
                      handleFieldValidation('full_name', e.target.value);
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="email"
                  label={t('user.users.form.email')}
                  rules={[
                    { required: true, message: t('user.users.form.emailRequired') },
                    { type: 'email', message: t('user.users.form.emailInvalid') },
                    { max: 150, message: t('user.users.form.emailMaxLength') }
                  ]}
                  validateStatus={emailChecking ? 'validating' : emailAvailable === false ? 'error' : emailAvailable === true ? 'success' : undefined}
                  help={emailChecking ? t('user.users.form.checking') : emailAvailable === false ? t('user.users.form.emailExists') : emailAvailable === true ? t('user.users.form.emailAvailable') : undefined}
                >
                  <Input 
                    placeholder={t('user.users.form.emailPlaceholder')} 
                    onChange={(e) => {
                      handleFieldValidation('email', e.target.value);
                    }}
                    onBlur={(e) => {
                      handleFieldValidation('email', e.target.value);
                    }}
                    disabled={editingUser !== null}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="phone_number"
                  label={t('user.users.form.phoneNumber')}
                  rules={[
                    { pattern: /^[0-9+\-\s()]*$/, message: t('user.users.form.phoneNumberInvalid') },
                    { max: 20, message: t('user.users.form.phoneNumberMaxLength') }
                  ]}
                >
                  <Input 
                    placeholder={t('user.users.form.phoneNumberPlaceholder')} 
                    onChange={(e) => {
                      handleFieldValidation('phone_number', e.target.value);
                    }}
                    onBlur={(e) => {
                      handleFieldValidation('phone_number', e.target.value);
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>

            {!editingUser && (
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="password"
                    label={t('user.users.form.password')}
                    rules={[
                      { required: true, message: t('user.users.form.passwordRequired') },
                      { min: 8, message: t('user.users.form.passwordMinLength') },
                      { 
                        pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
                        message: t('user.users.form.passwordFormat')
                      }
                    ]}
                  >
                    <Input.Password 
                      placeholder={t('user.users.form.passwordPlaceholder')} 
                      onChange={(e) => {
                        handleFieldValidation('password', e.target.value);
                      }}
                      onBlur={(e) => {
                        handleFieldValidation('password', e.target.value);
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="confirmPassword"
                    label={t('user.users.form.confirmPassword')}
                    rules={[
                      { required: true, message: t('user.users.form.confirmPasswordRequired') },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (!value || getFieldValue('password') === value) {
                            return Promise.resolve();
                          }
                          return Promise.reject(new Error(t('user.users.form.confirmPasswordMismatch')));
                        },
                      }),
                    ]}
                  >
                    <Input.Password 
                      placeholder={t('user.users.form.confirmPasswordPlaceholder')} 
                      onChange={(e) => {
                        handleFieldValidation('confirmPassword', e.target.value);
                      }}
                      onBlur={(e) => {
                        handleFieldValidation('confirmPassword', e.target.value);
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>
            )}

            <Form.Item
              name="is_active"
              label={t('user.users.form.status')}
              valuePropName="checked"
            >
              <Switch 
                checkedChildren={t('user.users.form.activated')} 
                unCheckedChildren={t('user.users.form.deactivated')}
              />
            </Form.Item>
            
            <Form.Item wrapperCol={{ offset: 6, span: 16 }}>
              <Space>
                <Button
                  onClick={handleCancel}
                >
                  {t('user.users.form.cancel')}
                </Button>
                {!editingUser && (
                  <Button type="primary" onClick={handleSaveAndNew}>
                    {t('user.users.form.saveAndNew')}
                  </Button>
                )}
                <Button type="primary" htmlType="submit">
                  {editingUser ? t('user.users.form.update') : t('user.users.form.saveAndClose')}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    );
  };

  const renderRolesContent = () => {
    console.log('DEBUG: renderRolesContent, roles:', roles);
    return (
      <Card
        title={t('user.roles.title')}
        bordered={false}
        extra={
          <Space>
            <Input.Search
              placeholder={t('user.roles.searchPlaceholder')}
              allowClear
              style={{ width: 250 }}
              onSearch={handleRoleSearch}
              onChange={(e) => handleRoleSearch(e.target.value)}
            />
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefreshRoles}
              loading={rolesLoading}
              title={t('user.roles.refreshData')}
            >
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                resetAddRoleForm();
                setAddRoleModalVisible(true);
              }}
            >
              {t('user.roles.addRole')}
            </Button>
          </Space>
        }
      >
        <Table
          dataSource={getSortedRoles()}
          rowKey="id"
          loading={rolesLoading}
          pagination={false}
          onChange={handleTableChange}
        >
          <Table.Column 
            title={t('user.roles.table.id')} 
            dataIndex="id" 
            key="id" 
            width={60}
            sorter={(a, b) => a.id - b.id}
            sortOrder={sortedInfo.columnKey === 'id' ? sortedInfo.order : null}
          />
          <Table.Column 
            title={t('user.roles.table.name')} 
            dataIndex="name" 
            key="name"
            sorter={(a, b) => a.name.localeCompare(b.name)}
            sortOrder={sortedInfo.columnKey === 'name' ? sortedInfo.order : null}
          />
          <Table.Column 
            title={t('user.roles.table.description')} 
            dataIndex="description" 
            key="description"
            sorter={(a, b) => (a.description || '').localeCompare(b.description || '')}
            sortOrder={sortedInfo.columnKey === 'description' ? sortedInfo.order : null}
          />
          <Table.Column
            title={t('user.roles.table.status')}
            dataIndex="is_activated"
            key="is_activated"
            width={120}
            sorter={(a, b) => Number(a.is_activated) - Number(b.is_activated)}
            sortOrder={sortedInfo.columnKey === 'is_activated' ? sortedInfo.order : null}
            render={(value, record: any) => (
              <Switch
                checked={value}
                onChange={() => handleToggleRoleActivated(record.id, value)}
                checkedChildren={t('user.roles.table.activated')}
                unCheckedChildren={t('user.roles.table.deactivated')}
              />
            )}
          />
          <Table.Column
            title=""
            key="edit"
            width={60}
            render={(_, record: any) => (
              <Button 
                type="text" 
                icon={<EditOutlined />} 
                onClick={() => handleEditRole(record)}
                title={t('user.roles.table.edit')}
              />
            )}
          />
          <Table.Column
            title=""
            key="delete"
            width={60}
            render={(_, record: any) => (
              <Button 
                type="text" 
                icon={<DeleteOutlined />} 
                onClick={() => handleDeleteRoleClick(record)}
                title={t('user.roles.table.delete')}
                danger
              />
            )}
          />
        </Table>
        {/* Modal Thêm Role */}
        <Modal
          title={t('user.roles.addModal.title')}
          open={addRoleModalVisible}
          onCancel={() => { setAddRoleModalVisible(false); resetAddRoleForm(); }}
          footer={null}
          destroyOnClose
          width={480}
          centered
        >
          <Form
            form={addRoleForm}
            layout="vertical"
            onFinish={(values) => handleAddRole(values, addRoleAction)}
            initialValues={{ is_activated: true }}
          >
            <Form.Item
              label={t('user.roles.form.name')}
              name="name"
              hasFeedback
              validateStatus={roleNameChecking ? 'validating' : roleNameAvailable === false ? 'error' : roleNameAvailable === true ? 'success' : undefined}
              help={roleNameChecking ? t('user.roles.form.checking') : roleNameAvailable === false ? t('user.roles.form.nameExists') : roleNameAvailable === true ? t('user.roles.form.nameAvailable') : undefined}
              rules={[
                { required: true, message: t('user.roles.form.nameRequired') },
                { min: 2, message: t('user.roles.form.nameMinLength') },
                { max: 100, message: t('user.roles.form.nameMaxLength') }
              ]}
            >
              <Input 
                placeholder={t('user.roles.form.namePlaceholder')} 
                autoComplete="off" 
                onChange={(e) => {
                  checkRoleName(e.target.value);
                }}
                onBlur={(e) => {
                  checkRoleName(e.target.value);
                }}
              />
            </Form.Item>
            <Form.Item label={t('user.roles.form.description')} name="description">
              <Input.TextArea placeholder={t('user.roles.form.descriptionPlaceholder')} autoSize={{ minRows: 2, maxRows: 4 }} />
            </Form.Item>
            <Form.Item label={t('user.roles.form.status')} name="is_activated" valuePropName="checked">
              <Switch checkedChildren={t('user.roles.form.activated')} unCheckedChildren={t('user.roles.form.deactivated')} />
            </Form.Item>
            <Form.Item>
              <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
                <Button
                  onClick={() => { setAddRoleModalVisible(false); resetAddRoleForm(); }}
                  disabled={addRoleLoading}
                >
                  {t('user.roles.form.cancel')}
                </Button>
                <Button
                  type="primary"
                  loading={addRoleLoading}
                  onClick={() => {
                    setAddRoleAction('save_and_new');
                    addRoleForm.submit();
                  }}
                >
                  {t('user.roles.form.saveAndNew')}
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={addRoleLoading}
                  onClick={() => {
                    setAddRoleAction('save');
                  }}
                >
                  {t('user.roles.form.saveAndClose')}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>

        {/* Modal Edit Role */}
        <Modal
          title={t('user.roles.editModal.title')}
          open={editRoleModalVisible}
          onCancel={() => { setEditRoleModalVisible(false); setEditingRole(null); editRoleForm.resetFields(); setEditRoleNameAvailable(null); }}
          footer={null}
          destroyOnClose
          width={480}
          centered
        >
          <Form
            form={editRoleForm}
            layout="vertical"
            onFinish={handleSaveEditRole}
            initialValues={{ is_activated: true }}
          >
            <Form.Item
              label={t('user.roles.form.name')}
              name="name"
            >
              <Input placeholder={t('user.roles.form.namePlaceholder')} autoComplete="off" disabled />
            </Form.Item>
            <Form.Item label={t('user.roles.form.description')} name="description">
              <Input.TextArea placeholder={t('user.roles.form.descriptionPlaceholder')} autoSize={{ minRows: 2, maxRows: 4 }} />
            </Form.Item>
            <Form.Item label={t('user.roles.form.status')} name="is_activated" valuePropName="checked">
              <Switch checkedChildren={t('user.roles.form.activated')} unCheckedChildren={t('user.roles.form.deactivated')} />
            </Form.Item>
            <Form.Item>
              <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
                <Button
                  onClick={() => { setEditRoleModalVisible(false); setEditingRole(null); editRoleForm.resetFields(); setEditRoleNameAvailable(null); }}
                  disabled={editRoleLoading}
                >
                  {t('user.roles.form.cancel')}
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={editRoleLoading}
                >
                  {t('user.roles.form.save')}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </Card>
    );
  };

  const renderUserRolesContent = () => {
    return (
      <Card
        title={t('user.userRoles.title')}
        bordered={false}
        extra={
          <Space>
            <Input.Search
              placeholder={t('user.userRoles.searchPlaceholder')}
              allowClear
              style={{ width: 250 }}
              onSearch={handleUserRoleSearch}
              onChange={(e) => handleUserRoleSearch(e.target.value)}
            />
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefreshUserRoles}
              loading={userRolesLoading}
              title={t('user.userRoles.refreshData')}
            >
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setAddUserRoleModalVisible(true)}
            >
              {t('user.userRoles.addNew')}
            </Button>
          </Space>
        }
      >
        <Table
          dataSource={getSortedUserRoles()}
          rowKey={(record) => `${record.user_id}-${record.role_id}`}
          loading={userRolesLoading}
          pagination={false}
          onChange={handleUserRoleTableChange}
          sortDirections={['ascend', 'descend']}
        >
          <Table.Column 
            title={t('user.userRoles.table.userId')} 
            dataIndex="user_id" 
            key="user_id" 
            width={280}
            sorter={true}
            sortDirections={['ascend', 'descend']}
            render={(value) => (
              <Text code style={{ fontSize: '12px' }}>
                {value}
              </Text>
            )}
          />
          <Table.Column 
            title={t('user.userRoles.table.username')} 
            dataIndex="username" 
            key="username"
            width={150}
            sorter={true}
            sortDirections={['ascend', 'descend']}
          />
          <Table.Column 
            title={t('user.userRoles.table.fullName')} 
            dataIndex="full_name" 
            key="full_name"
            width={200}
            sorter={true}
            sortDirections={['ascend', 'descend']}
          />
          <Table.Column 
            title={t('user.userRoles.table.roleId')} 
            dataIndex="role_id" 
            key="role_id"
            width={80}
            sorter={true}
            sortDirections={['ascend', 'descend']}
            render={(value) => (
              <Tag color="blue">{value}</Tag>
            )}
          />
          <Table.Column 
            title={t('user.userRoles.table.roleName')} 
            dataIndex="role_name" 
            key="role_name"
            width={150}
            sorter={true}
            sortDirections={['ascend', 'descend']}
            render={(value) => (
              <Tag color="green">{value}</Tag>
            )}
          />
          <Table.Column
            title=""
            key="delete"
            width={60}
            render={(_, record: UserRole) => (
              <Button 
                type="text" 
                icon={<DeleteOutlined />} 
                onClick={() => handleDeleteUserRoleClick(record)}
                title={t('user.userRoles.table.delete')}
                danger
              />
            )}
          />
        </Table>

        {/* Modal Delete UserRole Confirmation */}
        <Modal
          title={
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: '8px', 
              color: '#d32f2f'
            }}>
              <ExclamationCircleOutlined style={{ fontSize: '18px' }} />
              <span style={{ fontWeight: 'bold' }}>{t('user.userRoles.deleteModal.title')}</span>
            </div>
          }
          open={deleteUserRoleModalVisible}
          onCancel={handleCancelDeleteUserRole}
          footer={null}
          width={500}
          centered
        >
          {userRoleToDelete && (
            <div>
              <div style={{ 
                fontSize: '14px', 
                color: '#666', 
                marginBottom: '20px',
                lineHeight: '1.5'
              }}>
                {t('user.userRoles.deleteModal.confirmMessage')}
              </div>

              <div style={{ 
                backgroundColor: '#fafafa',
                padding: '16px',
                borderRadius: '8px',
                border: '1px solid #e8e8e8',
                marginBottom: '20px'
              }}>
                <div style={{ 
                  fontWeight: 'bold', 
                  color: '#d32f2f', 
                  marginBottom: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px'
                }}>
                  <UserOutlined style={{ fontSize: '14px' }} />
                  {t('user.userRoles.deleteModal.userRoleInfoToDelete')}:
                </div>
                <div style={{ fontSize: '13px', lineHeight: '1.6' }}>
                  <div><strong>{t('user.userRoles.table.username')}:</strong> {userRoleToDelete.username}</div>
                  <div><strong>{t('user.userRoles.table.fullName')}:</strong> {userRoleToDelete.full_name}</div>
                  <div><strong>{t('user.userRoles.table.roleName')}:</strong> {userRoleToDelete.role_name}</div>
                  <div><strong>{t('user.userRoles.table.userId')}:</strong> <Text code style={{ fontSize: '11px' }}>{userRoleToDelete.user_id}</Text></div>
                  <div><strong>{t('user.userRoles.table.roleId')}:</strong> {userRoleToDelete.role_id}</div>
                </div>
              </div>

              <div style={{ 
                padding: '12px 16px', 
                backgroundColor: '#fff3cd', 
                border: '1px solid #ffeaa7',
                borderRadius: '6px',
                fontSize: '13px',
                color: '#856404',
                display: 'flex',
                alignItems: 'flex-start',
                gap: '8px',
                marginBottom: '24px'
              }}>
                <ExclamationCircleOutlined style={{ fontSize: '14px', marginTop: '1px' }} />
                <div>
                  <strong>{t('user.userRoles.deleteModal.warningTitle')}:</strong> {t('user.userRoles.deleteModal.warningMessage')}
                </div>
              </div>

              <div style={{ textAlign: 'right' }}>
                <Space>
                  <Button onClick={handleCancelDeleteUserRole}>
                    {t('user.userRoles.deleteModal.cancel')}
                  </Button>
                  <Button 
                    type="primary" 
                    danger 
                    onClick={handleConfirmDeleteUserRole}
                    style={{ fontWeight: 'bold' }}
                  >
                    {t('user.userRoles.deleteModal.confirmDelete')}
                  </Button>
                </Space>
              </div>
            </div>
          )}
        </Modal>

        {/* Modal Add UserRole */}
        <Modal
          title={t('user.userRoles.addModal.title')}
          open={addUserRoleModalVisible}
          onCancel={handleCancelAddUserRole}
          footer={null}
          destroyOnClose
          width={500}
          centered
        >
          <Form
            form={addUserRoleForm}
            layout="vertical"
            onFinish={handleSaveUserRole}
          >
            <Form.Item
              label={t('user.userRoles.form.user')}
              name="user_id"
              rules={[{ required: true, message: t('user.userRoles.form.userRequired') }]}
            >
              <SearchUserDropdown
                value={selectedUserId}
                onChange={setSelectedUserId}
                placeholder={t('user.userRoles.form.userPlaceholder')}
                users={users}
                loading={usersLoading}
              />
            </Form.Item>
            
            <Form.Item
              label={t('user.userRoles.form.role')}
              name="role_id"
              rules={[{ required: true, message: t('user.userRoles.form.roleRequired') }]}
              validateStatus={userRoleExistsError ? 'error' : undefined}
              help={userRoleExistsError}
            >
              <SearchRoleDropdown
                value={selectedRoleId}
                onChange={setSelectedRoleId}
                placeholder={t('user.userRoles.form.rolePlaceholder')}
                roles={roles}
                loading={rolesLoading}
              />
            </Form.Item>

            {/* Hiển thị thông tin user roles hiện tại của user được chọn */}
            {selectedUserId && (
              <div style={{ 
                marginBottom: '16px', 
                padding: '12px', 
                backgroundColor: '#f6f8fa', 
                borderRadius: '6px',
                border: '1px solid #e1e4e8'
              }}>
                <div style={{ fontSize: '14px', fontWeight: '500', marginBottom: '8px', color: '#24292e' }}>
                  📋 {t('user.userRoles.form.currentRolesTitle')}:
                </div>
                {(() => {
                  const userRolesOfSelectedUser = userRoles.filter(ur => ur.user_id === selectedUserId);
                  const selectedUser = users.find(u => u.id === selectedUserId);
                  
                  if (userRolesOfSelectedUser.length === 0) {
                    return (
                      <div style={{ color: '#6a737d', fontSize: '13px' }}>
                        <em>{t('user.userRoles.form.noRolesMessage', { username: selectedUser?.username, fullName: selectedUser?.full_name })}</em>
                      </div>
                    );
                  }
                  
                  return (
                    <div>
                      <div style={{ color: '#6a737d', fontSize: '13px', marginBottom: '8px' }}>
                        <strong>{selectedUser?.username} - {selectedUser?.full_name}</strong> {t('user.userRoles.form.hasRolesMessage', { count: userRolesOfSelectedUser.length })}:
                      </div>
                      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '6px' }}>
                        {userRolesOfSelectedUser.map((userRole, index) => (
                          <Tag 
                            key={index} 
                            color="blue" 
                            style={{ fontSize: '12px', margin: 0 }}
                          >
                            {userRole.role_name}
                          </Tag>
                        ))}
                      </div>
                    </div>
                  );
                })()}
              </div>
            )}

            <Form.Item>
              <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
                <Button
                  onClick={handleCancelAddUserRole}
                  disabled={addUserRoleLoading}
                >
                  {t('user.userRoles.form.cancel')}
                </Button>
                <Button
                  type="primary"
                  loading={addUserRoleLoading}
                  onClick={handleSaveAndNewUserRole}
                >
                  {t('user.userRoles.form.saveAndNew')}
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={addUserRoleLoading}
                >
                  {t('user.userRoles.form.saveAndClose')}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </Card>
    );
  };

  const renderContent = () => {
    console.log('DEBUG: renderContent, selectedKey:', selectedKey);
    switch (selectedKey) {
      case 'users':
        return renderUsersContent();
      case 'roles':
        return renderRolesContent();
      case 'user-roles':
        return renderUserRolesContent();
      case 'role-permissions':
        return <RolePermissionsPage />;
      default:
        return renderOverviewContent();
    }
  };

  if (loading) {
    return (
      <Layout style={{ minHeight: '100vh' }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }}>
          <div style={{ textAlign: 'center', color: 'white' }}>
            <Spin size="large" style={{ marginBottom: '16px' }} />
            <div style={{ fontSize: '24px', marginBottom: '16px' }}>{t('user.loading.title')}</div>
            <div>{t('user.loading.module')}</div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout className="user-management-page-layout" key={language}>
      {/* Header */}
      <Header className="user-management-page-header">
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          height: '100%'
        }}>
          <Space size="large">
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              {/* Icon menu cho cả mobile và desktop */}
              <Button
                type="text"
                icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                onClick={handleToggleSidebar}
                style={{ 
                  color: 'white',
                  fontSize: '16px',
                  width: '32px',
                  height: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              />
              <Avatar 
                size={32} 
                icon={<UserOutlined />} 
                style={{ 
                  background: 'rgba(255,255,255,0.2)',
                  border: '1px solid rgba(255,255,255,0.3)',
                  fontSize: '16px'
                }}
              />
              <div>
                <Title level={5} className="user-management-title-responsive" style={{ color: 'white', margin: 0, fontSize: '16px' }}>
                  {t('user.breadcrumb.module')}
                </Title>
              </div>
            </div>
          </Space>
          
          <Space>
            <Badge count={1} size="small">
              <Button 
                type="text" 
                icon={<InfoCircleOutlined />} 
                style={{ color: 'white' }}
                onClick={() => message.info(t('user.info.moduleDescription'))}
              />
            </Badge>
            {isNewTab && (
              <>
                <Button 
                  type="text" 
                  icon={<AppstoreOutlined />} 
                  style={{ color: 'white' }}
                  onClick={handleOpenAppsPage}
                  title={t('user.header.openAppsPage')}
                />
                <Button 
                  type="text" 
                  icon={<CloseOutlined />} 
                  style={{ color: 'white' }}
                  onClick={() => window.close()}
                  title={t('user.header.closeTab')}
                />
              </>
            )}
          </Space>
        </div>
      </Header>

      <Layout>
        {/* Sidebar Navigation */}
        <Sider 
          trigger={null} 
          collapsible 
          collapsed={collapsed}
          className={`user-management-page-sider ${collapsed ? 'collapsed' : ''}`}
          width={280}
          collapsedWidth={80}
          onClick={handleOverlayClick}
        >
          <Menu
            mode="inline"
            selectedKeys={[selectedKey]}
            items={menuItems}
            onClick={handleMenuClick}
            className="user-management-nav-menu"
          />
        </Sider>

        {/* Main Content */}
        <Layout className="user-management-page-content">
          <Content className="user-management-page-main-content" style={{ 
            margin: '24px',
            padding: '24px'
          }}>
            {/* Thông báo tab mới */}
            {isNewTab && (
              <Alert
                message={t('user.tab.newTab.title')}
                description={t('user.tab.newTab.description')}
                type="info"
                showIcon
                closable
                style={{ marginBottom: '16px' }}
              />
            )}
            
            <Breadcrumb className="user-management-breadcrumb" style={{ marginBottom: '24px' }}>
              <Breadcrumb.Item>
                <Button 
                  type="link" 
                  onClick={handleOpenAppsPage}
                  style={{ padding: 0, height: 'auto', lineHeight: '1.5' }}
                >
                  {t('user.breadcrumb.apps')}
                </Button>
              </Breadcrumb.Item>
              <Breadcrumb.Item>{t('user.breadcrumb.module')}</Breadcrumb.Item>
              <Breadcrumb.Item>
                {(() => {
                  console.log('DEBUG: Breadcrumb selectedKey:', selectedKey);
                  switch (selectedKey) {
                    case 'users':
                      return t('user.breadcrumb.users');
                    case 'roles':
                      return t('user.breadcrumb.roles');
                    case 'user-roles':
                      return t('user.breadcrumb.userRoles');
                    default:
                      return t('user.breadcrumb.overview');
                  }
                })()}
              </Breadcrumb.Item>
            </Breadcrumb>
            
            {renderContent()}
          </Content>
        </Layout>
      </Layout>

      {/* Delete Confirmation Modal */}
      <Modal
        title={
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '8px', 
            color: '#d32f2f'
          }}>
            <ExclamationCircleOutlined style={{ fontSize: '18px' }} />
            <span style={{ fontWeight: 'bold' }}>{t('user.users.deleteModal.title')}</span>
          </div>
        }
        open={deleteModalVisible}
        onCancel={handleCancelDelete}
        footer={null}
        width={500}
        centered
      >
        {userToDelete && (
          <div>
            <div style={{ 
              fontSize: '14px', 
              color: '#666', 
              marginBottom: '20px',
              lineHeight: '1.5'
            }}>
              {t('user.users.deleteModal.confirmMessage')}
            </div>

            <div style={{ 
              backgroundColor: '#fafafa',
              padding: '16px',
              borderRadius: '8px',
              border: '1px solid #e8e8e8',
              marginBottom: '20px'
            }}>
              <div style={{ 
                fontWeight: 'bold', 
                color: '#d32f2f', 
                marginBottom: '12px',
                display: 'flex',
                alignItems: 'center',
                gap: '6px'
              }}>
                <UserOutlined style={{ fontSize: '14px' }} />
                {t('user.users.deleteModal.userInfoToDelete')}:
              </div>
              <div style={{ fontSize: '13px', lineHeight: '1.6' }}>
                <div><strong>{t('user.users.table.username')}:</strong> {userToDelete.username}</div>
                <div><strong>{t('user.users.table.fullName')}:</strong> {userToDelete.full_name}</div>
                {userToDelete.email && <div><strong>{t('user.users.table.email')}:</strong> {userToDelete.email}</div>}
                {userToDelete.phone_number && <div><strong>{t('user.users.table.phoneNumber')}:</strong> {userToDelete.phone_number}</div>}
                <div><strong>{t('user.users.table.status')}:</strong> {userToDelete.is_active ? t('user.users.table.active') : t('user.users.table.inactive')}</div>
              </div>
            </div>

            <div style={{ 
              padding: '12px 16px', 
              backgroundColor: '#fff3cd', 
              border: '1px solid #ffeaa7',
              borderRadius: '6px',
              fontSize: '13px',
              color: '#856404',
              display: 'flex',
              alignItems: 'flex-start',
              gap: '8px',
              marginBottom: '24px'
            }}>
              <ExclamationCircleOutlined style={{ fontSize: '14px', marginTop: '1px' }} />
              <div>
                <strong>{t('user.users.deleteModal.warningTitle')}:</strong> {t('user.users.deleteModal.warningMessage')}
              </div>
            </div>

            <div style={{ textAlign: 'right' }}>
              <Space>
                <Button onClick={handleCancelDelete}>
                  {t('user.users.deleteModal.cancel')}
                </Button>
                <Button 
                  type="primary" 
                  danger 
                  onClick={handleConfirmDelete}
                  style={{ fontWeight: 'bold' }}
                >
                  {t('user.users.deleteModal.confirmDelete')}
                </Button>
              </Space>
            </div>
          </div>
        )}
      </Modal>
    </Layout>
  );
};

export default UserManagementPage;