.activity-logs-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.activity-logs-page .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-logs-page .ant-statistic-title {
  font-size: 14px;
  color: #666;
}

.activity-logs-page .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

.activity-logs-page .ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.activity-logs-page .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  color: #333;
}

.activity-logs-page .ant-table-tbody > tr:hover > td {
  background-color: #f0f8ff;
}

.activity-logs-page .ant-tag {
  font-weight: 500;
  border-radius: 4px;
}

.activity-logs-page .ant-btn-text {
  border-radius: 4px;
}

.activity-logs-page .ant-btn-text:hover {
  background-color: #f0f0f0;
}

.activity-logs-page .ant-descriptions-item-label {
  font-weight: 600;
  color: #333;
}

.activity-logs-page .ant-modal-content {
  border-radius: 8px;
}

.activity-logs-page .ant-modal-header {
  border-radius: 8px 8px 0 0;
}

.activity-logs-page .ant-input-affix-wrapper {
  border-radius: 6px;
}

.activity-logs-page .ant-select-selector {
  border-radius: 6px;
}

.activity-logs-page .ant-picker {
  border-radius: 6px;
}

.activity-logs-page .ant-btn {
  border-radius: 6px;
}

.activity-logs-page .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.activity-logs-page .ant-statistic-content-prefix {
  margin-right: 8px;
}

.activity-logs-page .ant-table-pagination {
  margin: 16px 0;
}

.activity-logs-page .ant-empty {
  margin: 40px 0;
}

.activity-logs-page .ant-spin-container {
  min-height: 200px;
}

/* Responsive design */
@media (max-width: 768px) {
  .activity-logs-page {
    padding: 16px;
  }
  
  .activity-logs-page .ant-col {
    margin-bottom: 16px;
  }
  
  .activity-logs-page .ant-table {
    font-size: 12px;
  }
  
  .activity-logs-page .ant-table-thead > tr > th,
  .activity-logs-page .ant-table-tbody > tr > td {
    padding: 8px 4px;
  }
}

/* Custom scrollbar */
.activity-logs-page .ant-table-body::-webkit-scrollbar {
  height: 8px;
}

.activity-logs-page .ant-table-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.activity-logs-page .ant-table-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.activity-logs-page .ant-table-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation for loading states */
.activity-logs-page .ant-spin-dot {
  animation: antSpinMove 1.2s infinite linear;
}

@keyframes antSpinMove {
  100% {
    transform: rotate(360deg);
  }
}

/* Hover effects for cards */
.activity-logs-page .ant-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.3s ease;
}

/* Custom styles for statistics cards */
.activity-logs-page .ant-statistic {
  text-align: center;
}

.activity-logs-page .ant-statistic-content-value {
  color: #1890ff;
}

.activity-logs-page .ant-statistic-content-value.ant-statistic-content-value-decimal {
  font-size: 20px;
}

/* Filter section styling */
.activity-logs-page .ant-card-body {
  padding: 16px;
}

.activity-logs-page .ant-row {
  margin-bottom: 0;
}

.activity-logs-page .ant-col {
  margin-bottom: 8px;
}

/* Table row styling */
.activity-logs-page .ant-table-tbody > tr > td {
  vertical-align: top;
}

.activity-logs-page .ant-table-tbody > tr > td:last-child {
  text-align: center;
}

/* Modal styling */
.activity-logs-page .ant-modal-body {
  max-height: 70vh;
  overflow-y: auto;
}

.activity-logs-page .ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: #fafafa;
}

.activity-logs-page .ant-textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

/* Tag colors for different actions */
.activity-logs-page .ant-tag-green {
  background-color: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

.activity-logs-page .ant-tag-blue {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.activity-logs-page .ant-tag-red {
  background-color: #fff2f0;
  border-color: #ffccc7;
  color: #ff4d4f;
}

.activity-logs-page .ant-tag-orange {
  background-color: #fff7e6;
  border-color: #ffd591;
  color: #fa8c16;
}

.activity-logs-page .ant-tag-purple {
  background-color: #f9f0ff;
  border-color: #d3adf7;
  color: #722ed1;
}

.activity-logs-page .ant-tag-gray {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  color: #666;
} 