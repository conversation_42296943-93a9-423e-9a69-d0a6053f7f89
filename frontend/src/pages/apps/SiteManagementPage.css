/* SiteManagementPage.css - Responsive Design cho Site Management Module */

/* Common Styles */
.site-management-page-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.site-management-page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 64px !important;
  padding: 0 24px !important;
  flex-shrink: 0;
}

.site-management-page-header .ant-typography {
  color: white !important;
  margin: 0 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.site-management-page-header .ant-avatar {
  flex-shrink: 0;
}

.site-management-page-sider {
  background: #fff;
  box-shadow: 2px 0 8px rgba(0,0,0,0.1);
  position: fixed;
  top: 64px;
  left: 0;
  height: calc(100vh - 64px);
  overflow: auto;
  padding-top: 0 !important;
  z-index: 999;
}

.site-management-page-content {
  background: #f5f5f5;
  margin-top: 64px;
  margin-left: 280px;
  min-height: calc(100vh - 64px);
  transition: margin-left 0.2s ease;
  flex: 1;
}

.site-management-page-main-content {
  background: #fff;
  border-radius: 8px;
  min-height: calc(100vh - 112px);
  overflow: auto;
  margin: 24px;
  padding: 24px;
}

/* Collapsed state */
.site-management-page-sider.collapsed {
  width: 80px !important;
}

.site-management-page-content.collapsed {
  margin-left: 80px;
}

/* Mobile First Approach */
@media (max-width: 768px) {
  .site-management-page-header {
    padding: 0 16px !important;
    height: 56px !important;
  }
  
  .site-management-page-header .ant-typography {
    font-size: 14px !important;
  }
  
  .site-management-page-header .site-management-title-responsive {
    font-size: 14px !important;
    line-height: 1.2 !important;
  }
  
  .site-management-page-header .ant-avatar {
    width: 28px !important;
    height: 28px !important;
  }
  
  .site-management-page-sider {
    position: fixed !important;
    top: 56px !important;
    left: 0;
    z-index: 999;
    height: calc(100vh - 56px) !important;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    width: 280px !important;
    background: #fff !important;
    box-shadow: 2px 0 8px rgba(0,0,0,0.15) !important;
    padding-top: 0 !important;
  }
  
  /* Khi sidebar mở (collapsed = false) */
  .site-management-page-sider:not(.collapsed) {
    transform: translateX(0);
  }
  
  /* Khi sidebar đóng (collapsed = true) */
  .site-management-page-sider.collapsed {
    transform: translateX(-100%);
  }
  
  .site-management-page-content {
    margin-top: 56px !important;
    margin-left: 0 !important;
  }
  
  .site-management-page-content.collapsed {
    margin-left: 0 !important;
  }
  
  .site-management-page-main-content {
    margin: 16px !important;
    padding: 16px !important;
  }
  
  .site-management-breadcrumb {
    margin-bottom: 16px !important;
  }
  
  .site-management-overview-avatar {
    margin-bottom: 12px !important;
  }
  
  .site-management-overview-stats {
    margin-bottom: 12px !important;
  }
  
  .site-management-overview-stats .ant-space {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .site-management-overview-stats .ant-space-item {
    margin-bottom: 8px !important;
  }
  
  /* Mobile menu toggle button */
  .site-management-nav-toggle {
    height: 48px !important;
    padding: 0 12px !important;
  }
  
  .site-management-nav-toggle .ant-btn {
    width: 32px !important;
    height: 32px !important;
    font-size: 14px !important;
  }
  
  .site-management-nav-toggle.collapsed {
    justify-content: center !important;
    padding: 0 !important;
  }
  
  .site-management-nav-toggle.expanded {
    justify-content: flex-start !important;
    padding: 0 12px !important;
  }
  
  /* Mobile overlay - chỉ hiển thị khi sidebar mở */
  .site-management-page-sider::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }
  
  /* Chỉ hiển thị overlay khi sidebar mở */
  .site-management-page-sider:not(.collapsed)::before {
    opacity: 1;
    pointer-events: auto;
  }
  
  /* Mobile menu items */
  .site-management-nav-menu .ant-menu-item {
    height: 48px !important;
    line-height: 48px !important;
    margin: 4px 8px !important;
    border-radius: 8px !important;
  }
  
  .site-management-nav-menu .ant-menu-item:hover {
    background: #f0f7ff !important;
    color: #1890ff !important;
  }
  
  .site-management-nav-menu .ant-menu-item-selected {
    background: #e6f7ff !important;
    color: #1890ff !important;
    font-weight: 600 !important;
  }
}

/* Tablet Styles */
@media (min-width: 769px) and (max-width: 1024px) {
  .site-management-page-header .site-management-title-responsive {
    font-size: 15px !important;
  }
  
  .site-management-page-header .site-management-subtitle-responsive {
    font-size: 11px !important;
  }
  
  .site-management-page-header .ant-avatar {
    width: 30px !important;
    height: 30px !important;
  }
  
  .site-management-page-content {
    margin-left: 240px;
  }
  
  .site-management-page-content.collapsed {
    margin-left: 80px;
  }
  
  .site-management-page-sider {
    width: 240px !important;
  }
  
  .site-management-page-sider.collapsed {
    width: 80px !important;
  }
}

@media (min-width: 1025px) {
  .site-management-page-sider {
    width: 280px !important;
  }
  
  .site-management-page-sider.collapsed {
    width: 80px !important;
  }
  
  .site-management-page-content {
    margin-left: 280px;
  }
  
  .site-management-page-content.collapsed {
    margin-left: 80px;
  }
  
  .site-management-page-header .site-management-title-responsive {
    font-size: 16px !important;
  }
  
  .site-management-page-header .site-management-subtitle-responsive {
    font-size: 12px !important;
  }
  
  .site-management-page-header .ant-avatar {
    width: 32px !important;
    height: 32px !important;
  }
}

.site-management-overview-card {
  margin-bottom: 24px;
}

.site-management-overview-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin-bottom: 16px;
}

.site-management-overview-stats {
  margin-bottom: 16px;
}

.site-management-overview-tags {
  margin-bottom: 16px;
}

.site-management-overview-description {
  font-size: 14px;
  line-height: 1.6;
}

.site-management-stats-card {
  text-align: center;
}

.site-management-stats-number {
  font-size: 24px;
  font-weight: bold;
}

.site-management-stats-label {
  font-size: 12px;
  color: #666;
}

.site-management-activity-card {
  font-size: 12px;
}

.site-management-activity-item {
  margin-bottom: 8px;
}

.site-management-activity-time {
  color: #666;
  margin-bottom: 4px;
}

/* Navigation Menu Styles */
.site-management-nav-menu {
  border-right: 0;
  height: calc(100% - 65px);
  overflow: auto;
}

.site-management-nav-toggle {
  display: none !important;
}

/* Breadcrumb Styles */
.site-management-breadcrumb {
  margin-bottom: 24px;
  line-height: 1.5;
}

.site-management-breadcrumb .ant-breadcrumb-item {
  display: flex;
  align-items: center;
}

.site-management-breadcrumb .ant-breadcrumb-link {
  display: flex;
  align-items: center;
  height: auto;
  line-height: 1.5;
}

.site-management-breadcrumb .ant-btn {
  display: inline-flex;
  align-items: center;
  height: auto;
  line-height: 1.5;
  padding: 0;
  border: none;
  background: none;
  color: #1890ff;
  text-decoration: none;
}

.site-management-breadcrumb .ant-btn:hover {
  color: #40a9ff;
  text-decoration: underline;
}

/* Responsive Menu Toggle */
.mobile-menu-toggle {
  display: none;
}

@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: block;
  }
  
  .desktop-menu-toggle {
    display: none;
  }
}

/* Content Area Responsive */
.site-management-content-area {
  transition: margin-left 0.3s ease;
}

@media (max-width: 768px) {
  .site-management-content-area {
    margin-left: 0 !important;
  }
}

/* Card Responsive */
.site-management-card-responsive {
  margin-bottom: 16px;
}

@media (max-width: 768px) {
  .site-management-card-responsive {
    margin-bottom: 12px;
  }
}

/* Button Responsive */
.site-management-button-responsive {
  height: 36px;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .site-management-button-responsive {
    height: 32px;
    font-size: 12px;
  }
}

/* Typography Responsive */
.site-management-title-responsive {
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (max-width: 768px) {
  .site-management-title-responsive {
    font-size: 14px;
  }
}

.site-management-subtitle-responsive {
  font-size: 12px;
  color: rgba(255,255,255,0.8);
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}

@media (max-width: 768px) {
  .site-management-subtitle-responsive {
    font-size: 11px;
    margin-top: 1px;
  }
}

/* Site Management Module Specific Styles */
.site-management-module-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.site-management-module-sider {
  border-right: 1px solid #f0f0f0;
}

.site-management-module-content {
  background: #fafafa;
}

.site-management-module-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.site-management-module-card:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.1);
  transition: box-shadow 0.3s ease;
}

/* System Configuration Styles */
.system-config-section {
  border-left: 4px solid #1890ff;
  padding-left: 12px;
}

.system-config-title {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 8px;
}

.system-config-item {
  margin-bottom: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

/* User Management Section */
.user-management-section {
  border-left: 4px solid #52c41a;
  padding-left: 12px;
}

.user-management-header {
  font-size: 16px;
  font-weight: 600;
  color: #52c41a;
  margin-bottom: 8px;
}

/* Data Management Grid */
.data-management-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.data-management-card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Development Tools Section */
.dev-tools-section {
  border-left: 4px solid #faad14;
  padding-left: 12px;
}

.dev-tools-header {
  font-size: 16px;
  font-weight: 600;
  color: #faad14;
  margin-bottom: 8px;
}

/* Documentation Section */
.documentation-section {
  border-left: 4px solid #722ed1;
  padding-left: 12px;
}

.documentation-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #262626;
}

/* Sites Table Styles */
.site-management-sites-table {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.site-management-sites-table .ant-table-thead > tr > th {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 600;
  color: #262626;
}

.site-management-sites-table .ant-table-tbody > tr > td {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 16px;
}

/* Removed hover effect for better performance */
/* .site-management-sites-table .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
} */

.site-management-sites-table .ant-table-row-expand-icon {
  border: 1px solid #d9d9d9;
  background: #fff;
}

.site-management-sites-table .ant-table-row-expand-icon-expanded {
  background: #1890ff;
  border-color: #1890ff;
  color: #fff;
}

.site-management-sites-table .ant-switch {
  min-width: 60px;
}

.site-management-sites-table .ant-btn-text {
  padding: 4px 8px;
  height: 28px;
  border-radius: 4px;
}

/* Mobile responsive for sites table */
@media (max-width: 768px) {
  .site-management-sites-table .ant-table {
    font-size: 12px;
  }
  
  .site-management-sites-table .ant-table-thead > tr > th,
  .site-management-sites-table .ant-table-tbody > tr > td {
    padding: 8px 12px;
  }
  
  .site-management-sites-table .ant-table-column-title {
    font-size: 12px;
  }
  
  .site-management-sites-table .ant-switch {
    min-width: 50px;
    height: 20px;
  }
  
  .site-management-sites-table .ant-switch-inner {
    font-size: 10px;
  }
  
  .site-management-sites-table .ant-btn-text {
    padding: 2px 6px;
    height: 24px;
    font-size: 12px;
  }
  
  .site-management-sites-table .ant-table-expand-icon-col {
    width: 40px;
  }
  
  .site-management-sites-table .ant-table-selection-col {
    width: 40px;
  }
  
  /* Mobile responsive for search and buttons */
  .site-management-card-responsive .ant-card-extra {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .site-management-card-responsive .ant-card-extra .ant-space {
    flex-direction: column;
    width: 100%;
  }
  
  .site-management-card-responsive .ant-input-search {
    width: 100% !important;
  }
  
  .site-management-card-responsive .ant-space-item {
    width: 100%;
  }
  
  .site-management-card-responsive .ant-btn {
    width: 100%;
  }
}

/* Search and Action Buttons Styling */
.site-management-sites-search .ant-input {
  border-radius: 6px;
}

.site-management-sites-search .ant-input-search-button {
  border-radius: 0 6px 6px 0;
  height: 32px;
}

.site-management-action-buttons .ant-btn {
  border-radius: 6px;
  height: 32px;
  width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Enhanced table sorting */
.site-management-sites-table .ant-table-column-sorter {
  color: #1890ff;
}

.site-management-sites-table .ant-table-column-sorter-up.active,
.site-management-sites-table .ant-table-column-sorter-down.active {
  color: #1890ff;
}

/* Switch styling improvements */
.site-management-sites-table .ant-switch-checked {
  background-color: #52c41a;
}

.site-management-sites-table .ant-switch:not(.ant-switch-checked) {
  background-color: #d9d9d9;
}

/* Override global card hover effects for sites table */
.site-management-sites-table,
.site-management-sites-table:hover {
  transform: none !important;
  box-shadow: none !important;
  transition: none !important;
}

/* Override card hover effects for the sites list card */
.site-management-card-responsive,
.site-management-card-responsive:hover {
  transform: none !important;
  box-shadow: none !important;
  transition: none !important;
}

/* Override table container hover effects */
.site-management-sites-table .ant-table,
.site-management-sites-table .ant-table:hover {
  transform: none !important;
  box-shadow: none !important;
  transition: none !important;
}

/* Override table wrapper hover effects */
.site-management-sites-table .ant-table-wrapper,
.site-management-sites-table .ant-table-wrapper:hover {
  transform: none !important;
  box-shadow: none !important;
  transition: none !important;
}

/* Strong override for all card hover effects in sites management */
.site-management-page-main-content .ant-card,
.site-management-page-main-content .ant-card:hover,
.site-management-page-main-content .ant-card:focus,
.site-management-page-main-content .ant-card:active {
  transform: none !important;
  box-shadow: none !important;
  transition: none !important;
  border: 1px solid #f0f0f0 !important;
}

/* Override for the specific sites list card */
.site-management-card-responsive.ant-card,
.site-management-card-responsive.ant-card:hover,
.site-management-card-responsive.ant-card:focus,
.site-management-card-responsive.ant-card:active {
  transform: none !important;
  box-shadow: none !important;
  transition: none !important;
  border: 1px solid #f0f0f0 !important;
}

/* Form styles */
.site-management-form {
  max-width: 100%;
}

.site-management-form .ant-form-item {
  margin-bottom: 24px;
}

.site-management-form .ant-form-item-label {
  font-weight: 500;
  color: #262626;
}

.site-management-form .ant-input,
.site-management-form .ant-select-selector,
.site-management-form .ant-input-number {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.site-management-form .ant-input:focus,
.site-management-form .ant-select-focused .ant-select-selector,
.site-management-form .ant-input-number-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.site-management-form .ant-input[disabled] {
  background-color: #f5f5f5;
  color: #bfbfbf;
}

.site-management-form .ant-switch {
  min-width: 44px;
}

.site-management-form .ant-form-item-explain-error {
  font-size: 12px;
  margin-top: 4px;
}

.site-management-form .ant-btn {
  height: 36px;
  border-radius: 6px;
  font-weight: 500;
}

.site-management-form .ant-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.site-management-form .ant-btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Enhanced focus styles for better accessibility */
.site-management-form .ant-btn:focus,
.site-management-form .ant-btn:focus-visible {
  outline: 3px solid #40a9ff !important;
  outline-offset: 2px !important;
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3) !important;
  z-index: 1;
  position: relative;
}

.site-management-form .ant-btn-primary:focus,
.site-management-form .ant-btn-primary:focus-visible {
  outline: 3px solid #91d5ff !important;
  outline-offset: 2px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.4), 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

/* Focus styles for other form elements */
.site-management-form .ant-input:focus,
.site-management-form .ant-input:focus-visible {
  outline: 2px solid #40a9ff !important;
  outline-offset: 1px !important;
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.site-management-form .ant-select:focus .ant-select-selector,
.site-management-form .ant-select-focused .ant-select-selector {
  outline: 2px solid #40a9ff !important;
  outline-offset: 1px !important;
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.site-management-form .ant-switch:focus,
.site-management-form .ant-switch:focus-visible {
  outline: 2px solid #40a9ff !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* Mobile responsive for form */
@media (max-width: 768px) {
  .site-management-form .ant-form-item {
    margin-bottom: 16px;
  }
  
  .site-management-form .ant-row {
    margin: 0 !important;
  }
  
  .site-management-form .ant-col {
    padding: 0 !important;
    margin-bottom: 16px;
  }
  
  .site-management-form .ant-form-item:last-child {
    margin-bottom: 0;
  }
  
  .site-management-form .ant-form-item .ant-form-item-control-input {
    min-height: 32px;
  }
  
  .site-management-form .ant-btn {
    height: 40px;
    font-size: 14px;
  }
  
  .site-management-form .ant-form-item-control-input-content {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    flex-wrap: wrap;
  }
  
  .site-management-form .ant-form-item-control-input-content .ant-btn {
    flex: 1;
    min-width: 120px;
  }
}

.site-management-sites-table-wrapper {
  max-height: calc(100vh - 246px); /* 64px header + 24px breadcrumb + 60px card title/extra + 50px pagination + 48px margin/padding */
  overflow-y: auto;
}

@media (max-width: 768px) {
  .site-management-sites-table-wrapper {
    max-height: calc(100vh - 238px); /* 56px header + 16px breadcrumb + 60px card title/extra + 50px pagination + 56px margin/padding */
  }
}

/* Form buttons responsive design */
.site-management-form-buttons {
  width: 100%;
}

.site-management-form-buttons .ant-space-item {
  width: auto; /* Reset to auto for desktop */
}

/* Modal responsive design */
@media (max-width: 768px) {
  .ant-modal {
    margin: 16px !important;
    max-width: calc(100vw - 32px) !important;
  }
  
  .ant-modal-content {
    border-radius: 8px !important;
  }
  
  .ant-modal-header {
    padding: 16px 20px !important;
  }
  
  .ant-modal-body {
    padding: 16px 20px !important;
  }
  
  .ant-modal-footer {
    padding: 12px 20px !important;
  }
  
  .ant-modal-footer .ant-btn {
    height: 36px !important;
    font-size: 14px !important;
  }
  
  /* Modal form responsive */
  .ant-modal .ant-form .ant-row {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  
  .ant-modal .ant-form .ant-col {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  
  .ant-modal .ant-form .ant-form-item {
    margin-bottom: 16px !important;
  }
  
  .ant-modal .ant-form .ant-input,
  .ant-modal .ant-form .ant-input-textarea,
  .ant-modal .ant-form .ant-select {
    font-size: 16px !important;
  }
  
  .ant-modal .ant-form .ant-switch {
    min-width: 44px !important;
    height: 22px !important;
  }
  
  .ant-modal .ant-form .ant-switch-inner {
    font-size: 12px !important;
  }
}

/* Modal form focus effects - matching the main form */
.ant-modal .ant-form .ant-input:focus,
.ant-modal .ant-form .ant-input:focus-visible,
.ant-modal .ant-form .ant-input-focused {
  outline: 2px solid #40a9ff !important;
  outline-offset: 1px !important;
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
  transition: all 0.3s ease !important;
}

.ant-modal .ant-form .ant-input-textarea:focus,
.ant-modal .ant-form .ant-input-textarea:focus-visible,
.ant-modal .ant-form .ant-input-textarea-focused {
  outline: 2px solid #40a9ff !important;
  outline-offset: 1px !important;
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
  transition: all 0.3s ease !important;
}

.ant-modal .ant-form .ant-select:focus .ant-select-selector,
.ant-modal .ant-form .ant-select:focus-visible .ant-select-selector,
.ant-modal .ant-form .ant-select-focused .ant-select-selector {
  outline: 2px solid #40a9ff !important;
  outline-offset: 1px !important;
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
  transition: all 0.3s ease !important;
}

.ant-modal .ant-form .ant-switch:focus,
.ant-modal .ant-form .ant-switch:focus-visible {
  outline: 2px solid #40a9ff !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
  transition: all 0.3s ease !important;
}

/* Modal button styles - matching the main form exactly */
.ant-modal .ant-form .ant-btn {
  height: 36px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ant-modal .ant-form .ant-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  transition: all 0.3s ease;
}

.ant-modal .ant-form .ant-btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}

.ant-modal .ant-form .ant-btn:focus,
.ant-modal .ant-form .ant-btn:focus-visible {
  outline: 3px solid #40a9ff !important;
  outline-offset: 2px !important;
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3) !important;
  z-index: 1;
  position: relative;
  transition: all 0.3s ease !important;
}

.ant-modal .ant-form .ant-btn-primary:focus,
.ant-modal .ant-form .ant-btn-primary:focus-visible {
  outline: 3px solid #91d5ff !important;
  outline-offset: 2px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.4), 0 4px 12px rgba(102, 126, 234, 0.3) !important;
  z-index: 1;
  position: relative;
  transition: all 0.3s ease !important;
}

/* Modal form hover effects */
.ant-modal .ant-form .ant-input:hover {
  border-color: #667eea !important;
  transition: all 0.3s ease !important;
}

.ant-modal .ant-form .ant-input-textarea:hover {
  border-color: #667eea !important;
  transition: all 0.3s ease !important;
}

.ant-modal .ant-form .ant-select:hover .ant-select-selector {
  border-color: #667eea !important;
  transition: all 0.3s ease !important;
}

.ant-modal .ant-form .ant-btn:hover {
  border-color: #667eea !important;
  color: #667eea !important;
  transition: all 0.3s ease !important;
}

.ant-modal .ant-form .ant-btn-primary:hover {
  border-color: #667eea !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;
  transition: all 0.3s ease !important;
}

/* Modal form disabled state styling */
.ant-modal .ant-form .ant-input[disabled] {
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.25) !important;
  cursor: not-allowed !important;
}

.ant-modal .ant-form .ant-select-disabled .ant-select-selector {
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.25) !important;
  cursor: not-allowed !important;
}

@media (max-width: 768px) {
  .site-management-form-buttons {
    flex-direction: column !important;
    align-items: stretch !important;
  }
  
  .site-management-form-buttons .ant-space-item {
    width: 100% !important;
    margin-bottom: 8px !important;
  }
  
  .site-management-form-buttons .ant-space-item:last-child {
    margin-bottom: 0 !important;
  }
  
  .site-management-form-buttons .ant-btn {
    width: 100% !important;
    height: 40px !important;
    font-size: 14px !important;
  }
  
  /* Đảm bảo form layout tối ưu cho mobile */
  .site-management-form .ant-row {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  
  .site-management-form .ant-col {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  
  /* Tối ưu khoảng cách giữa các form items trên mobile */
  .site-management-form .ant-form-item {
    margin-bottom: 16px !important;
  }
  
  /* Đảm bảo input fields có kích thước phù hợp trên mobile */
  .site-management-form .ant-input,
  .site-management-form .ant-input-textarea,
  .site-management-form .ant-select {
    font-size: 16px !important; /* Tránh zoom trên iOS */
  }
  
  /* Tối ưu switch component trên mobile */
  .site-management-form .ant-switch {
    min-width: 44px !important; /* Đảm bảo dễ touch */
    height: 22px !important;
  }
  
  .site-management-form .ant-switch-inner {
    font-size: 12px !important;
  }
}

/* Modal footer button styles - matching the main form */
.ant-modal .ant-modal-footer .ant-btn {
  height: 36px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ant-modal .ant-modal-footer .ant-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  transition: all 0.3s ease;
}

.ant-modal .ant-modal-footer .ant-btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}

.ant-modal .ant-modal-footer .ant-btn:focus,
.ant-modal .ant-modal-footer .ant-btn:focus-visible {
  outline: 3px solid #40a9ff !important;
  outline-offset: 2px !important;
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3) !important;
  z-index: 1;
  position: relative;
  transition: all 0.3s ease !important;
}

.ant-modal .ant-modal-footer .ant-btn-primary:focus,
.ant-modal .ant-modal-footer .ant-btn-primary:focus-visible {
  outline: 3px solid #91d5ff !important;
  outline-offset: 2px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.4), 0 4px 12px rgba(102, 126, 234, 0.3) !important;
  z-index: 1;
  position: relative;
  transition: all 0.3s ease !important;
}

.ant-modal .ant-modal-footer .ant-btn:hover {
  border-color: #667eea !important;
  color: #667eea !important;
  transition: all 0.3s ease !important;
}

.ant-modal .ant-modal-footer .ant-btn-primary:hover {
  border-color: #667eea !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;
  transition: all 0.3s ease !important;
} 