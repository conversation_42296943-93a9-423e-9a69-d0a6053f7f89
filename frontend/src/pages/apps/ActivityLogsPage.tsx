import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Row,
  Col,
  Statistic,
  DatePicker,
  Select,
  Input,
  Button,
  Space,
  Tag,
  Modal,
  Descriptions,
  Tooltip,
  message,
  Spin,
  Empty,
  Typography
} from 'antd';
import {
  ReloadOutlined,
  SearchOutlined,
  EyeOutlined,
  DeleteOutlined,
  Bar<PERSON><PERSON>Outlined,
  UserOutlined,
  CalendarOutlined,
  FilterOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import './ActivityLogsPage.css';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;
const { Title, Text } = Typography;

interface ActivityLog {
  id: string;
  user_id?: string;
  user_full_name?: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  resource_name?: string;
  description?: string;
  old_values?: any;
  new_values?: any;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

interface ActivityStatistics {
  total_logs: number;
  action_statistics: Array<{ action: string; count: number }>;
  resource_statistics: Array<{ resource_type: string; count: number }>;
  daily_statistics: Array<{ date: string; count: number }>;
}

const ActivityLogsPage: React.FC = () => {
  const [logs, setLogs] = useState<ActivityLog[]>([]);
  const [statistics, setStatistics] = useState<ActivityStatistics | null>(null);
  const [loading, setLoading] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedLog, setSelectedLog] = useState<ActivityLog | null>(null);
  const [filters, setFilters] = useState({
    user_id: '',
    action: '',
    resource_type: '',
    start_date: '',
    end_date: '',
    search: ''
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });

  // Fetch activity logs
  const fetchLogs = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        skip: ((pagination.current - 1) * pagination.pageSize).toString(),
        limit: pagination.pageSize.toString(),
        ...filters
      });

      const response = await fetch(`/api/activity-logs/?${params}`);
      if (response.ok) {
        const data = await response.json();
        setLogs(data);
        setPagination(prev => ({ ...prev, total: data.length }));
      } else {
        message.error('Failed to fetch activity logs');
      }
    } catch (error) {
      message.error('Error fetching activity logs');
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch statistics
  const fetchStatistics = async () => {
    try {
      const response = await fetch('/api/activity-logs/statistics/summary');
      if (response.ok) {
        const data = await response.json();
        setStatistics(data);
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  useEffect(() => {
    fetchLogs();
    fetchStatistics();
  }, [pagination.current, pagination.pageSize]);

  // Handle filter changes
  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // Apply filters
  const applyFilters = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchLogs();
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      user_id: '',
      action: '',
      resource_type: '',
      start_date: '',
      end_date: '',
      search: ''
    });
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // View log details
  const viewLogDetails = (log: ActivityLog) => {
    setSelectedLog(log);
    setDetailModalVisible(true);
  };

  // Delete log
  const deleteLog = async (logId: string) => {
    Modal.confirm({
      title: 'Delete Activity Log',
      content: 'Are you sure you want to delete this activity log?',
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          const response = await fetch(`/api/activity-logs/${logId}`, {
            method: 'DELETE'
          });
          if (response.ok) {
            message.success('Activity log deleted successfully');
            fetchLogs();
          } else {
            message.error('Failed to delete activity log');
          }
        } catch (error) {
          message.error('Error deleting activity log');
        }
      }
    });
  };

  // Get action color
  const getActionColor = (action: string) => {
    switch (action.toUpperCase()) {
      case 'CREATE':
        return 'green';
      case 'UPDATE':
        return 'blue';
      case 'DELETE':
        return 'red';
      case 'VIEW':
        return 'orange';
      case 'LOGIN':
        return 'purple';
      case 'LOGOUT':
        return 'gray';
      case 'ERROR':
        return 'red';
      default:
        return 'default';
    }
  };

  // Table columns
  const columns: ColumnsType<ActivityLog> = [
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
      width: 100,
      render: (action: string) => (
        <Tag color={getActionColor(action)}>
          {action.toUpperCase()}
        </Tag>
      ),
      filters: [
        { text: 'CREATE', value: 'CREATE' },
        { text: 'UPDATE', value: 'UPDATE' },
        { text: 'DELETE', value: 'DELETE' },
        { text: 'VIEW', value: 'VIEW' },
        { text: 'LOGIN', value: 'LOGIN' },
        { text: 'LOGOUT', value: 'LOGOUT' },
        { text: 'ERROR', value: 'ERROR' }
      ],
      onFilter: (value, record) => record.action.toUpperCase() === value
    },
    {
      title: 'Resource',
      dataIndex: 'resource_type',
      key: 'resource_type',
      width: 120,
      render: (resourceType: string, record: ActivityLog) => (
        <div>
          <div>{resourceType}</div>
          {record.resource_name && (
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.resource_name}
            </Text>
          )}
        </div>
      )
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (description: string) => (
        <Tooltip title={description}>
          <span>{description}</span>
        </Tooltip>
      )
    },
    {
      title: 'User',
      dataIndex: 'user_full_name',
      key: 'user_full_name',
      width: 150,
      render: (userName: string, record: ActivityLog) => (
        <div>
          <UserOutlined style={{ marginRight: 4 }} />
          {userName || 'System'}
        </div>
      )
    },
    {
      title: 'IP Address',
      dataIndex: 'ip_address',
      key: 'ip_address',
      width: 120,
      render: (ip: string) => ip || '-'
    },
    {
      title: 'Date',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (date: string) => (
        <div>
          <CalendarOutlined style={{ marginRight: 4 }} />
          {dayjs(date).format('YYYY-MM-DD HH:mm:ss')}
        </div>
      ),
      sorter: (a, b) => dayjs(a.created_at).unix() - dayjs(b.created_at).unix()
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 100,
      render: (_, record: ActivityLog) => (
        <Space>
          <Tooltip title="View Details">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => viewLogDetails(record)}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => deleteLog(record.id)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div className="activity-logs-page">
      <Title level={2}>Activity Logs</Title>
      
      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Logs"
              value={statistics?.total_logs || 0}
              prefix={<BarChartOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Today's Logs"
              value={statistics?.daily_statistics?.find(s => s.date === dayjs().format('YYYY-MM-DD'))?.count || 0}
              prefix={<CalendarOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Active Users"
              value={statistics?.action_statistics?.find(s => s.action === 'LOGIN')?.count || 0}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Errors"
              value={statistics?.action_statistics?.find(s => s.action === 'ERROR')?.count || 0}
              prefix={<BarChartOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={4}>
            <Select
              placeholder="Action"
              allowClear
              value={filters.action}
              onChange={(value) => handleFilterChange('action', value)}
              style={{ width: '100%' }}
            >
              <Option value="CREATE">CREATE</Option>
              <Option value="UPDATE">UPDATE</Option>
              <Option value="DELETE">DELETE</Option>
              <Option value="VIEW">VIEW</Option>
              <Option value="LOGIN">LOGIN</Option>
              <Option value="LOGOUT">LOGOUT</Option>
              <Option value="ERROR">ERROR</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="Resource Type"
              allowClear
              value={filters.resource_type}
              onChange={(value) => handleFilterChange('resource_type', value)}
              style={{ width: '100%' }}
            >
              <Option value="User">User</Option>
              <Option value="Role">Role</Option>
              <Option value="Site">Site</Option>
              <Option value="ActivityLog">ActivityLog</Option>
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              placeholder={['Start Date', 'End Date']}
              onChange={(dates) => {
                if (dates) {
                  handleFilterChange('start_date', dates[0]?.toISOString());
                  handleFilterChange('end_date', dates[1]?.toISOString());
                } else {
                  handleFilterChange('start_date', '');
                  handleFilterChange('end_date', '');
                }
              }}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={6}>
            <Input
              placeholder="Search description..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              prefix={<SearchOutlined />}
            />
          </Col>
          <Col span={4}>
            <Space>
              <Button
                type="primary"
                icon={<FilterOutlined />}
                onClick={applyFilters}
              >
                Apply
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={resetFilters}
              >
                Reset
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={logs}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} items`,
            onChange: (page, pageSize) =>
              setPagination(prev => ({ ...prev, current: page, pageSize: pageSize || 20 }))
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Detail Modal */}
      <Modal
        title="Activity Log Details"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            Close
          </Button>
        ]}
        width={800}
      >
        {selectedLog && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="ID" span={2}>
              {selectedLog.id}
            </Descriptions.Item>
            <Descriptions.Item label="Action">
              <Tag color={getActionColor(selectedLog.action)}>
                {selectedLog.action.toUpperCase()}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="Resource Type">
              {selectedLog.resource_type}
            </Descriptions.Item>
            <Descriptions.Item label="Resource ID">
              {selectedLog.resource_id || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="Resource Name">
              {selectedLog.resource_name || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="User" span={2}>
              {selectedLog.user_full_name || 'System'}
            </Descriptions.Item>
            <Descriptions.Item label="IP Address">
              {selectedLog.ip_address || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="Created At">
              {dayjs(selectedLog.created_at).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
            <Descriptions.Item label="Description" span={2}>
              {selectedLog.description || '-'}
            </Descriptions.Item>
            {selectedLog.old_values && (
              <Descriptions.Item label="Old Values" span={2}>
                <TextArea
                  value={JSON.stringify(selectedLog.old_values, null, 2)}
                  rows={4}
                  readOnly
                />
              </Descriptions.Item>
            )}
            {selectedLog.new_values && (
              <Descriptions.Item label="New Values" span={2}>
                <TextArea
                  value={JSON.stringify(selectedLog.new_values, null, 2)}
                  rows={4}
                  readOnly
                />
              </Descriptions.Item>
            )}
            {selectedLog.user_agent && (
              <Descriptions.Item label="User Agent" span={2}>
                <TextArea
                  value={selectedLog.user_agent}
                  rows={2}
                  readOnly
                />
              </Descriptions.Item>
            )}
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default ActivityLogsPage; 