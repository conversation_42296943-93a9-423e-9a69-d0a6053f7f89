// Đường dẫn: frontend/src/pages/apps/AppsPage.tsx (<PERSON><PERSON><PERSON> bản cập nhật)
import React, { useEffect, useState, useCallback } from 'react';
import { 
  Row, 
  Col, 
  Card, 
  Button, 
  Spin, 
  Tag, 
  Typography, 
  Layout, 
  Space,
  Avatar,
  Badge,
  Input,
  Tooltip,
  Empty,
  Modal,
  Alert,
  Tabs,
  Checkbox,
  Descriptions,
  App
} from 'antd';
import { 
  AppstoreOutlined, 
  SearchOutlined, 
  DownloadOutlined, 
  CheckCircleOutlined,
  SettingOutlined,
  EyeOutlined,
  FilterOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  ToolOutlined,
  LogoutOutlined,
  UserOutlined
} from '@ant-design/icons';
import axios from 'axios';
import { handleBrowserExtensionError, getErrorMessage, logError } from '../../utils/errorHandler';
import { useAuth } from '../../contexts/AuthContext';
import { usePermissions } from '../../contexts/PermissionContext';
import { useNavigate } from 'react-router-dom';
import LanguagesDropdown from '../../components/LanguagesDropdown';
import { useTranslation } from 'react-i18next';

const { Header, Content } = Layout;
const { Title, Text, Paragraph } = Typography;
const { Search } = Input;

interface Module {
  name: string;
  summary: string;
  version: string;
  state: 'installed' | 'uninstalled';
  author?: string;
  depends?: string[];
  category?: string;
  description?: string;
  is_core?: boolean;
  can_uninstall?: boolean;
  is_core_module?: boolean;
  auto_install?: boolean;
  hidden?: boolean;
}

interface ModuleConfig {
  auto_install: boolean;
  hidden: boolean;
  required: boolean;
  description: string;
  category: string;
  author: string;
  version: string;
}

// Cấu hình axios interceptor
axios.interceptors.request.use(
  (config) => {
    console.log('DEBUG: Axios request:', {
      method: config.method,
      url: config.url,
      headers: config.headers
    });
    return config;
  },
  (error) => {
    console.error('DEBUG: Axios request error:', error);
    return Promise.reject(error);
  }
);

axios.interceptors.response.use(
  (response) => {
    console.log('DEBUG: Axios response:', {
      status: response.status,
      url: response.config.url,
      data: response.data
    });
    return response;
  },
  (error) => {
    console.error('DEBUG: Axios response error:', {
      message: error.message,
      code: error.code,
      status: error.response?.status,
      url: error.config?.url,
      data: error.response?.data
    });
    return Promise.reject(error);
  }
);

const AppsPage: React.FC = () => {
  const [modules, setModules] = useState<Module[]>([]);
  const [filteredModules, setFilteredModules] = useState<Module[]>([]);
  const [loading, setLoading] = useState(true);
  const [installing, setInstalling] = useState<string | null>(null);
  const [uninstalling, setUninstalling] = useState<string | null>(null);
  const [searchText, setSearchText] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [uninstallModalVisible, setUninstallModalVisible] = useState(false);
  const [moduleToUninstall, setModuleToUninstall] = useState<string | null>(null);
  const [configModalVisible, setConfigModalVisible] = useState(false);
  const [moduleConfigs, setModuleConfigs] = useState<{[key: string]: ModuleConfig}>({});
  const [configLoading, setConfigLoading] = useState(false);
  const [settingsModalVisible, setSettingsModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('system');
  const [filterModalVisible, setFilterModalVisible] = useState(false);
  const [userProfileVisible, setUserProfileVisible] = useState(false);

  const { user, logout } = useAuth();
  const { hasModulePermission, permissions, loading: permissionsLoading } = usePermissions();
  const { message } = App.useApp();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // CSS để đảm bảo Select dropdown hoạt động đúng
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .custom-select-dropdown {
        z-index: 9999 !important;
        position: absolute !important;
        background: white;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08);
        padding: 4px 0;
        max-height: 200px;
        overflow-y: auto;
      }
      .custom-select-item {
        padding: 8px 12px;
        cursor: pointer;
        transition: background-color 0.3s;
      }
      .custom-select-item:hover {
        background-color: #f5f5f5;
      }
      .custom-select-item.selected {
        background-color: #e6f7ff;
        color: #1890ff;
      }
      .ant-modal {
        z-index: 10000 !important;
      }
      .ant-modal-mask {
        z-index: 9999 !important;
      }
      .ant-modal-wrap {
        z-index: 10000 !important;
      }
    `;
    document.head.appendChild(style);
    
    // Xử lý lỗi browser extension
    const cleanup = handleBrowserExtensionError();
    
    return () => {
      document.head.removeChild(style);
      cleanup();
    };
  }, []);

  const fetchModules = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      console.log('DEBUG: Fetching modules from API...');
      const response = await axios.get('http://localhost:8000/api/internal/modules/all', {
        timeout: 10000, // 10 second timeout
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      console.log('DEBUG: API response received:', response.data);
      
      // API trả về { modules: [...], total: number }
      const modulesData = response.data.modules || response.data;
      console.log('DEBUG: Raw modules data:', modulesData);
      console.log('DEBUG: Number of modules to transform:', modulesData.length);

      // Transform data từ API để phù hợp với interface
      const transformedModules: Module[] = modulesData.map((module: any) => ({
        name: module.name,
        summary: module.summary || module.name.replace('_', ' ').toUpperCase(),
        version: module.version || '1.0.0',
        state: module.is_installed ? 'installed' : 'uninstalled',
        author: module.author || 'Metis Team',
        category: module.category || getCategoryFromName(module.name),
        description: module.description || getDescriptionFromName(module.name),
        depends: module.depends || [],
        is_core: module.is_core || false,
        can_uninstall: module.can_uninstall !== undefined ? module.can_uninstall : true,
        is_core_module: module.is_core_module || false,
        auto_install: module.auto_install || false,
        hidden: module.hidden || false
      }));

      console.log('DEBUG: Transformed modules:', transformedModules);
      console.log('DEBUG: Number of transformed modules:', transformedModules.length);
      setModules(transformedModules);
    } catch (error: any) {
      logError('fetchModules', error);
      const errorMessage = getErrorMessage(error);
      setError(errorMessage);
      message.error(t('apps.error.loadModules'));
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchModuleConfigs = useCallback(async () => {
    setConfigLoading(true);
    try {
      console.log('DEBUG: Fetching module configs...');
      const response = await axios.get('http://localhost:8000/api/internal/modules/config/all', {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      console.log('DEBUG: Module configs received:', response.data);
      setModuleConfigs(response.data);
    } catch (error: any) {
      logError('fetchModuleConfigs', error);
      message.error(t('apps.error.loadConfigs'));
    } finally {
      setConfigLoading(false);
    }
  }, []);

  const handleOpenConfig = async () => {
    await fetchModuleConfigs();
    setConfigModalVisible(true);
  };

  const handleSettingsClick = () => {
    setSettingsModalVisible(true);
  };

  // Health check function
  const checkBackendHealth = async () => {
    try {
      console.log('DEBUG: Checking backend health...');
      const response = await axios.get('http://localhost:8000/', {
        timeout: 5000,
        headers: {
          'Accept': 'application/json'
        }
      });
      console.log('DEBUG: Backend health check successful:', response.data);
      return true;
    } catch (error: any) {
      logError('checkBackendHealth', error);
      return false;
    }
  };

  // Phân loại module thành system và development
  const getSystemModules = () => {
    return modules.filter(module => {
      // Sử dụng thuộc tính is_core_module từ API
      return module.is_core_module === true;
    });
  };

  const getDevelopmentModules = () => {
    return modules.filter(module => {
      // Sử dụng thuộc tính is_core_module từ API
      return module.is_core_module === false;
    });
  };

  const getFilteredModules = () => {
    if (activeTab === 'system') {
      return getSystemModules();
    } else {
      return getDevelopmentModules();
    }
  };

  // Helper functions để tạo thông tin cho module
  const getCategoryFromName = (name: string): string => {
    const categories: { [key: string]: string } = {
      'base': 'Core',
      'user_management': 'Core',
      'site_management': 'Core',
      'multi_languages': 'Core',
      'activity_logs': 'Core',
      'inventory_management': 'Business',
      'crm_system': 'Business',
      'financial_management': 'Finance',
      'hr_management': 'HR',
      'project_management': 'Project',
      'sales_management': 'Business',
      'purchase_management': 'Business',
      'manufacturing': 'Business',
      'quality_management': 'Business',
      'maintenance': 'Business'
    };
    return categories[name] || 'Other';
  };

  const getDescriptionFromName = (name: string): string => {
    const descriptions: { [key: string]: string } = {
      'base': 'Module lõi chứa các thành phần cơ bản của hệ thống Metis Platform',
      'user_management': 'Hệ thống quản lý người dùng với phân quyền chi tiết và bảo mật cao',
      'site_management': 'Module quản lý thông tin và khai báo Site',
      'multi_languages': 'Module quản lý và cấu hình đa ngôn ngữ cho hệ thống',
      'activity_logs': 'Module quản lý toàn bộ thông tin log hệ thống',
      'inventory_management': 'Giải pháp quản lý kho hàng toàn diện với báo cáo thời gian thực',
      'crm_system': 'Quản lý quan hệ khách hàng với phân tích dữ liệu nâng cao',
      'financial_management': 'Hệ thống quản lý tài chính doanh nghiệp chuyên nghiệp',
      'hr_management': 'Giải pháp quản lý nhân sự toàn diện với tính năng tuyển dụng',
      'project_management': 'Công cụ quản lý dự án với Gantt chart và theo dõi tiến độ',
      'sales_management': 'Quản lý bán hàng và chăm sóc khách hàng hiệu quả',
      'purchase_management': 'Hệ thống quản lý mua hàng và nhà cung cấp',
      'manufacturing': 'Quản lý sản xuất và quy trình sản xuất',
      'quality_management': 'Đảm bảo chất lượng sản phẩm và quy trình',
      'maintenance': 'Quản lý bảo trì thiết bị và tài sản'
    };
    return descriptions[name] || 'Module chức năng cho hệ thống Metis Platform';
  };

  useEffect(() => {
    const initializeData = async () => {
      // Kiểm tra backend health trước
      const isBackendHealthy = await checkBackendHealth();
      if (!isBackendHealthy) {
        setError('Backend server không khả dụng. Vui lòng kiểm tra xem backend có đang chạy không.');
        setLoading(false);
        return;
      }
      
      await fetchModules();
      await fetchModuleConfigs();
    };
    initializeData();
  }, [fetchModules, fetchModuleConfigs]);

  // Lấy danh sách category thực tế từ modules
  const getCategories = (): string[] => {
    const cats = Array.from(new Set(modules.map(m => m.category).filter(Boolean))) as string[];
    return cats.sort(); // Sắp xếp theo thứ tự alphabet
  };

  // Filter và sort modules
  useEffect(() => {
    let filtered = getFilteredModules();
    console.log('DEBUG: Modules before permission filtering:', filtered.length, filtered.map(m => m.name));

    // Filter by permissions - chỉ hiển thị modules mà user có quyền xem
    if (!permissionsLoading) {
      filtered = filtered.filter(module => {
        // Kiểm tra xem user có bất kỳ permission nào liên quan đến module này không
        // Ví dụ: site_management_menu_overview, site_management_btn_refresh, etc.
        const hasAnyModulePermission = permissions.some(permission =>
          permission.code.startsWith(`${module.name}_`)
        );

        console.log(`AppsPage: Module ${module.name} - Has any permission: ${hasAnyModulePermission}`);
        console.log(`AppsPage: Module ${module.name} - User permissions:`,
          permissions.filter(p => p.code.startsWith(`${module.name}_`)).map(p => p.code)
        );

        return hasAnyModulePermission;
      });
      console.log('DEBUG: Modules after permission filtering:', filtered.length, filtered.map(m => m.name));
    }

    // Filter by search text
    if (searchText) {
      filtered = filtered.filter(module =>
        module.name.toLowerCase().includes(searchText.toLowerCase()) ||
        module.summary.toLowerCase().includes(searchText.toLowerCase()) ||
        module.description?.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    // Filter by category - hỗ trợ multiple selection
    if (selectedCategories.length > 0) {
      filtered = filtered.filter(module =>
        module.category && selectedCategories.includes(module.category)
      );
    } else if (categoryFilter !== 'all') {
      // Fallback cho logic cũ
      filtered = filtered.filter(module => module.category === categoryFilter);
    }

    setFilteredModules(filtered);
  }, [modules, moduleConfigs, activeTab, searchText, categoryFilter, selectedCategories, permissions, permissionsLoading, hasModulePermission]);

  const handleInstall = async (moduleName: string) => {
    setInstalling(moduleName);
    try {
      console.log('DEBUG: Installing module:', moduleName);
      const response = await axios.post(`http://localhost:8000/api/internal/modules/${moduleName}/install`, {}, {
        timeout: 30000, // 30 second timeout for installation
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      console.log('DEBUG: Install response:', response.data);
      
      // Refresh data from server to ensure consistency
      await fetchModules();
      
      message.success(t('apps.notification.installSuccess', { module: moduleName }));
    } catch (error: any) {
      logError('handleInstall', error);
      const errorMessage = getErrorMessage(error);
      message.error(errorMessage);
    } finally {
      setInstalling(null);
    }
  };

  const handleUninstall = async (moduleName: string) => {
    console.log('DEBUG: handleUninstall called for', moduleName);
    console.log('DEBUG: Showing confirm dialog...');
    setModuleToUninstall(moduleName);
    setUninstallModalVisible(true);
  };

  const handleConfirmUninstall = async () => {
    if (!moduleToUninstall) return;
    
    console.log('DEBUG: User confirmed uninstall for', moduleToUninstall);
    setUninstalling(moduleToUninstall);
    setUninstallModalVisible(false);
    
    try {
      console.log('DEBUG: Making API call to uninstall', moduleToUninstall);
      const response = await axios.post(`http://localhost:8000/api/internal/modules/${moduleToUninstall}/uninstall`, {}, {
        timeout: 30000, // 30 second timeout for uninstallation
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      console.log('DEBUG: Uninstall response:', response.data);
      
      // Refresh data from server to ensure consistency
      console.log('DEBUG: Refreshing modules data');
      await fetchModules();
      
      message.success(t('apps.notification.uninstallSuccess', { module: moduleToUninstall }));
    } catch (error: any) {
      logError('handleConfirmUninstall', error);
      const errorMessage = getErrorMessage(error);
      message.error(errorMessage);
    } finally {
      setUninstalling(null);
      setModuleToUninstall(null);
    }
  };

  const handleCancelUninstall = () => {
    console.log('DEBUG: User cancelled uninstall for', moduleToUninstall);
    setUninstallModalVisible(false);
    setModuleToUninstall(null);
  };

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      'Core': '#1890ff',
      'Business': '#52c41a',
      'Finance': '#faad14',
      'HR': '#f5222d',
      'Project': '#722ed1',
      'Other': '#666'
    };
    return colors[category] || '#666';
  };

  const getDependencyStatus = (module: Module) => {
    if (!module.depends || module.depends.length === 0) {
      return { status: 'success', text: t('apps.dependency.noDependencies') };
    }
    
    const installedModules = modules.filter(m => m.state === 'installed').map(m => m.name);
    const missingDeps = module.depends.filter(dep => !installedModules.includes(dep));
    
    if (missingDeps.length === 0) {
      return { status: 'success', text: t('apps.dependency.allInstalled') };
    }
    
    return {
      status: 'warning',
      text: t('apps.dependency.missingDependencies', { deps: missingDeps.join(', ') })
    };
  };

  const canUninstall = (module: Module) => {
    // Core module không thể gỡ cài đặt
    if (module.is_core) {
      return false;
    }
    
    const depStatus = getDependencyStatus(module);
    return depStatus.status === 'success';
  };

  // Component để render module cards
  const renderModuleCards = () => {
    if (loading) {
      return (
        <div style={{ textAlign: 'center', padding: '60px 20px' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>{t('apps.loading.modules')}</div>
        </div>
      );
    }

    if (filteredModules.length === 0) {
      return (
        <Card style={{ 
          textAlign: 'center', 
          padding: '60px 20px',
          borderRadius: '16px',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          border: 'none'
        }}>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              <div>
                <Title level={3} style={{ color: '#666', marginBottom: '8px' }}>
                  {activeTab === 'system' ? t('apps.empty.system') : t('apps.empty.development')}
                </Title>
                <Text type="secondary" style={{ fontSize: '14px' }}>
                  {activeTab === 'system' 
                    ? t('apps.empty.systemDesc') 
                    : t('apps.empty.developmentDesc')
                  }
                </Text>
              </div>
            }
          />
        </Card>
      );
    }

    return (
      <Row gutter={[24, 24]}>
        {filteredModules.map((module) => {
          const depStatus = getDependencyStatus(module);
          const canUninstallModule = canUninstall(module);
          
          return (
            <Col xs={24} sm={12} md={8} lg={6} key={module.name}>
              <Card
                hoverable
                style={{ 
                  height: '100%',
                  borderRadius: '16px',
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                  border: 'none',
                  overflow: 'hidden'
                }}
                styles={{ body: { padding: '20px', height: '100%', display: 'flex', flexDirection: 'column' } }}
              >
                <div style={{ flex: 1 }}>
                  <div style={{ marginBottom: '12px' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
                      <Tag 
                        color={getCategoryColor(module.category || 'Other')}
                        style={{ 
                          borderRadius: '6px',
                          fontSize: '11px',
                          fontWeight: 600,
                          padding: '2px 8px'
                        }}
                      >
                        {module.category}
                      </Tag>
                      {module.is_core && (
                        <Tag 
                          color="red"
                          style={{ 
                            borderRadius: '6px',
                            fontSize: '10px',
                            fontWeight: 600,
                            padding: '1px 6px',
                            border: 'none'
                          }}
                        >
                          CORE
                        </Tag>
                      )}
                    </div>
                    <Title level={4} style={{ 
                      margin: '8px 0', 
                      fontSize: '16px',
                      fontWeight: 600,
                      lineHeight: '1.3'
                    }}>
                      {module.summary}
                    </Title>
                    <Text type="secondary" style={{ 
                      fontSize: '12px',
                      fontFamily: 'monospace',
                      color: '#666'
                    }}>
                      {module.name}
                    </Text>
                  </div>

                  <Paragraph 
                    ellipsis={{ rows: 2 }} 
                    style={{ 
                      fontSize: '13px', 
                      color: '#666',
                      marginBottom: '16px',
                      lineHeight: '1.4'
                    }}
                  >
                    {module.description}
                  </Paragraph>

                  <div style={{ marginBottom: '16px' }}>
                    <div style={{ marginBottom: '8px' }}>
                      <Text type="secondary" style={{ 
                        fontSize: '12px',
                        fontWeight: 500
                      }}>
                        v{module.version} • {module.author}
                      </Text>
                    </div>
                    
                    {module.depends && module.depends.length > 0 && (
                      <div style={{ marginBottom: '8px' }}>
                        <Tooltip title={`Phụ thuộc: ${module.depends.join(', ')}`}>
                          <Tag 
                            color={depStatus.status === 'success' ? 'green' : 'orange'}
                            icon={<InfoCircleOutlined />}
                            style={{ 
                              fontSize: '10px',
                              borderRadius: '4px',
                              padding: '1px 6px'
                            }}
                          >
                            {depStatus.text}
                          </Tag>
                        </Tooltip>
                      </div>
                    )}
                  </div>

                  <div style={{ display: 'flex', gap: '8px' }}>
                    {module.state === 'installed' ? (
                      <>
                        {module.is_core_module ? (
                          <div style={{ 
                            flex: 1,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            padding: '8px 16px',
                            backgroundColor: '#f6ffed',
                            border: '1px solid #b7eb8f',
                            borderRadius: '8px',
                            color: '#52c41a',
                            fontWeight: 600,
                            fontSize: '14px'
                          }}>
                            <CheckCircleOutlined style={{ marginRight: '8px' }} />
                            {t('apps.installed')}
                          </div>
                        ) : (
                          <>
                            <Tooltip title={
                              !canUninstallModule
                                ? t('apps.uninstall.disabledTooltip')
                                : ''
                            }>
                              <Button 
                                danger
                                type="default" 
                                icon={<DeleteOutlined />}
                                onClick={() => handleUninstall(module.name)}
                                loading={uninstalling === module.name}
                                disabled={!canUninstallModule}
                                style={{ 
                                  flex: 1,
                                  borderRadius: '8px',
                                  height: '36px',
                                  fontWeight: 600
                                }}
                              >
                                {uninstalling === module.name ? t('apps.uninstalling') : t('apps.uninstall')}
                              </Button>
                            </Tooltip>
                            {!canUninstallModule && (
                              <Tooltip title={t('apps.uninstall.disabledTooltip')}>
                                <InfoCircleOutlined style={{ color: '#faad14', fontSize: '16px' }} />
                              </Tooltip>
                            )}
                          </>
                        )}
                      </>
                    ) : (
                      <Button 
                        type="primary" 
                        icon={<DownloadOutlined />}
                        onClick={() => handleInstall(module.name)}
                        loading={installing === module.name}
                        disabled={depStatus.status === 'warning'}
                        style={{ 
                          flex: 1,
                          borderRadius: '8px',
                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                          border: 'none',
                          height: '36px',
                          fontWeight: 600,
                          boxShadow: '0 4px 16px rgba(102, 126, 234, 0.3)'
                        }}
                      >
                        {installing === module.name ? t('apps.installing') : t('apps.install')}
                      </Button>
                    )}
                    <Tooltip title="Xem chi tiết (mở tab mới)">
                      <Button 
                        type="text" 
                        icon={<EyeOutlined />}
                        onClick={() => {
                          if (module.name === 'base') {
                            // Mở BasePage trong tab mới
                            window.open('/base', '_blank');
                          } else if (module.name === 'user_management') {
                            // Mở UserManagementPage trong tab mới
                            window.open('/user-management', '_blank');
                          } else if (module.name === 'site_management') {
                            // Mở SiteManagementPage trong tab mới
                            window.open('/site-management', '_blank');
                          } else if (module.name === 'activity_logs') {
                            // Mở ActivityLogsPage trong tab mới
                            window.open('/activity-logs', '_blank');
                          } else {
                            // TODO: Navigate to other module pages when created
                            message.info(t('apps.notification.modulePageNotCreated', { module: module.name }));
                          }
                        }}
                        style={{ 
                          borderRadius: '8px',
                          height: '36px',
                          width: '36px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      />
                    </Tooltip>
                  </div>
                </div>
              </Card>
            </Col>
          );
        })}
      </Row>
    );
  };

  const handleFilterClick = () => {
    setFilterModalVisible(true);
  };

  const handleFilterApply = () => {
    setFilterModalVisible(false);
  };

  const handleFilterReset = () => {
    setCategoryFilter('all');
    setSelectedCategories([]);
    setFilterModalVisible(false);
  };

  const handleCategoryChange = (category: string, checked: boolean) => {
    if (checked) {
      setSelectedCategories(prev => [...prev, category]);
    } else {
      setSelectedCategories(prev => prev.filter(cat => cat !== category));
    }
  };

  const handleSelectAllCategories = (checked: boolean) => {
    if (checked) {
      const allCategories = getCategories();
      setSelectedCategories(allCategories);
    } else {
      setSelectedCategories([]);
    }
  };

  const handleLogout = async () => {
    await logout();
    message.success(t('apps.notification.logoutSuccess'));
    navigate('/login');
  };

  if (loading && modules.length === 0) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <Layout style={{ minHeight: '100vh', background: '#f5f5f5' }}>
        <Header style={{ 
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          padding: '0 24px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          height: '64px'
        }}>
          <Space>
            <Avatar 
              size={40} 
              icon={<AppstoreOutlined />} 
              style={{ background: 'rgba(255,255,255,0.2)' }}
            />
            <div>
              <Title level={3} style={{ color: 'white', margin: 0, fontSize: '20px' }}>
                {t('app.title')}
              </Title>
              <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: '12px' }}>
                App Store & Module Management
              </Text>
            </div>
          </Space>
        </Header>
        
        <Content style={{ padding: '24px', textAlign: 'center' }}>
          <Card style={{ maxWidth: 500, margin: '100px auto' }}>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <div>
                  <Title level={4} style={{ color: '#666' }}>
                    Không thể kết nối đến server
                  </Title>
                  <Text type="secondary" style={{ display: 'block', marginBottom: 16 }}>
                    {error}
                  </Text>
                  <Button 
                    type="primary" 
                    icon={<ReloadOutlined />}
                    onClick={fetchModules}
                  >
                    Thử lại
                  </Button>
                </div>
              }
            />
          </Card>
        </Content>
      </Layout>
    );
  }

  return (
    <Layout style={{ minHeight: '100vh', background: '#f5f5f5' }}>
      {/* Header - Fixed layout issues */}
      <Header style={{ 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '0 24px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
        height: '72px',
        position: 'sticky',
        top: 0,
        zIndex: 999
      }}>
        <Space size="large" style={{ flexShrink: 0 }}>
          <Avatar 
            size={48} 
            icon={<AppstoreOutlined />} 
            style={{ 
              background: 'rgba(255,255,255,0.2)',
              fontSize: '24px',
              flexShrink: 0
            }}
          />
          <div style={{ minWidth: 0 }}>
            <Title level={2} style={{ 
              color: 'white', 
              margin: 0, 
              fontSize: '22px', 
              fontWeight: 600,
              lineHeight: '1.2',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }}>
              {t('app.title')}
            </Title>
            <Text style={{ 
              color: 'rgba(255,255,255,0.9)', 
              fontSize: '13px', 
              fontWeight: 400,
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: 'block'
            }}>
              {t('apps.header.subtitle')}
            </Text>
          </div>
        </Space>
        
        <Space size="middle" style={{ flexShrink: 0 }}>
          <LanguagesDropdown />
          <Badge count={modules.filter(m => m.state === 'installed').length} size="small">
            <Button 
              type="text" 
              icon={<CheckCircleOutlined />} 
              style={{ 
                color: 'white', 
                fontSize: '13px',
                fontWeight: 500,
                height: '40px',
                padding: '0 12px',
                whiteSpace: 'nowrap'
              }}
            >
              {t('apps.header.installed')}
            </Button>
          </Badge>
          <Button 
            type="text" 
            icon={<ReloadOutlined />} 
            style={{ 
              color: 'white',
              fontSize: '13px',
              fontWeight: 500,
              height: '40px',
              padding: '0 12px',
              whiteSpace: 'nowrap'
            }}
            onClick={fetchModules}
            loading={loading}
          >
            {t('apps.header.refresh')}
          </Button>
          <Button 
            type="text" 
            icon={<InfoCircleOutlined />} 
            style={{ 
              color: 'white',
              fontSize: '13px',
              fontWeight: 500,
              height: '40px',
              padding: '0 12px',
              whiteSpace: 'nowrap'
            }}
            onClick={async () => {
              const isHealthy = await checkBackendHealth();
              if (isHealthy) {
                message.success(t('apps.header.connectionSuccess'));
              } else {
                message.error(t('apps.header.connectionError'));
              }
            }}
          >
            {t('apps.header.testConnection')}
          </Button>
          <Button 
            type="text" 
            icon={<SettingOutlined />} 
            style={{ 
              color: 'white',
              fontSize: '13px',
              fontWeight: 500,
              height: '40px',
              padding: '0 12px',
              whiteSpace: 'nowrap',
              display: 'flex',
              alignItems: 'center',
              gap: '4px'
            }}
            onClick={handleSettingsClick}
          >
            {t('apps.header.settings')}
          </Button>
          <a onClick={() => setUserProfileVisible(true)}>
            <Avatar style={{ backgroundColor: '#87d068', cursor: 'pointer' }}>
              {user?.full_name ? user.full_name[0].toUpperCase() : <UserOutlined />}
            </Avatar>
          </a>
        </Space>
      </Header>

      {/* Content - Fixed responsive issues */}
      <Content style={{ 
        padding: '24px',
        maxWidth: '100%',
        margin: 0,
        background: '#f5f5f5'
      }}>
        {/* Search and Filter Section - chỉ giữ search và nút Advanced Filter */}
        <Card 
          style={{ 
            marginBottom: '24px', 
            borderRadius: '16px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            border: 'none',
            position: 'relative',
            zIndex: 100
          }}
          styles={{ body: { padding: '20px' } }}
        >
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={16} md={16} lg={16}>
              <Search
                placeholder={t('apps.search.placeholder')}
                prefix={<SearchOutlined style={{ color: '#999' }} />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ 
                  borderRadius: '8px',
                  height: '40px'
                }}
              />
            </Col>
            <Col xs={24} sm={8} md={4} lg={4}>
              <Button
                type="default"
                icon={<FilterOutlined />}
                onClick={handleFilterClick}
                style={{ 
                  width: '100%',
                  height: '40px',
                  borderRadius: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px'
                }}
              >
                {t('apps.filter.advanced')}
              </Button>
            </Col>
            <Col xs={24} sm={24} md={4} lg={4}>
              <div style={{ textAlign: 'right' }}>
                <Text type="secondary" style={{ fontSize: '13px', fontWeight: 500 }}>
                  {t('apps.showing', { count: filteredModules.length, total: modules.length })}
                </Text>
              </div>
            </Col>
          </Row>
        </Card>

        {/* Tabs Section */}
        <Card 
          style={{ 
            marginBottom: '24px', 
            borderRadius: '16px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            border: 'none',
            position: 'relative',
            zIndex: 100
          }}
          styles={{ body: { padding: '0' } }}
        >
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={[
              {
                key: 'development',
                label: (
                  <span>
                    <AppstoreOutlined style={{ marginRight: '8px' }} />
                    {t('apps.tab.development')}
                    <Badge 
                      count={getDevelopmentModules().length} 
                      size="small" 
                      style={{ marginLeft: '8px' }}
                    />
                  </span>
                ),
                children: (
                  <div style={{ padding: '24px' }}>
                    <Alert
                      message={t('apps.development.title')}
                      description={t('apps.development.desc')}
                      type="success"
                      showIcon
                      style={{ marginBottom: 24 }}
                    />
                    {renderModuleCards()}
                  </div>
                )
              },
              {
                key: 'system',
                label: (
                  <span>
                    <SettingOutlined style={{ marginRight: '8px' }} />
                    {t('apps.tab.system')}
                    <Badge 
                      count={getSystemModules().length} 
                      size="small" 
                      style={{ marginLeft: '8px' }}
                    />
                  </span>
                ),
                children: (
                  <div style={{ padding: '24px' }}>
                    <Alert
                      message={t('apps.system.title')}
                      description={t('apps.system.desc')}
                      type="info"
                      showIcon
                      style={{ marginBottom: 24 }}
                    />
                    {renderModuleCards()}
                  </div>
                )
              }
            ]}
            style={{ 
              borderRadius: '16px'
            }}
            size="large"
            type="card"
          />
        </Card>
      </Content>
      
      {/* Uninstall Confirmation Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <ExclamationCircleOutlined style={{ color: '#faad14', fontSize: '18px' }} />
            <span>{t('apps.uninstall.confirmTitle')}</span>
          </div>
        }
        open={uninstallModalVisible}
        onOk={handleConfirmUninstall}
        onCancel={handleCancelUninstall}
        okText={t('apps.uninstall.button')}
        cancelText={t('apps.uninstall.cancel')}
        okType="danger"
        confirmLoading={uninstalling === moduleToUninstall}
        width={500}
      >
        <div>
          <p>{t('apps.uninstall.warningTitle')} <strong>{moduleToUninstall}</strong>?</p>
          <Alert
            message={t('apps.uninstall.warningTitle')}
            description={t('apps.uninstall.warningDesc')}
            type="warning"
            showIcon
            style={{ marginTop: 16 }}
          />
        </div>
      </Modal>

      {/* Module Configuration Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <ToolOutlined style={{ color: '#1890ff', fontSize: '18px' }} />
            <span>{t('apps.config.title')}</span>
          </div>
        }
        open={configModalVisible}
        onCancel={() => setConfigModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setConfigModalVisible(false)}>
            {t('apps.config.close')}
          </Button>
        ]}
        width={800}
        style={{ top: 20 }}
      >
        <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
          {configLoading ? (
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <Spin size="large" />
              <div style={{ marginTop: 16 }}>{t('apps.config.loading')}</div>
            </div>
          ) : (
            <div>
              <Alert
                message={t('apps.config.infoTitle')}
                description={t('apps.config.infoDesc')}
                type="info"
                showIcon
                style={{ marginBottom: 24 }}
              />
              
              {Object.entries(moduleConfigs).map(([moduleName, config]) => (
                <Card
                  key={moduleName}
                  size="small"
                  style={{ 
                    marginBottom: 16,
                    border: moduleName === 'base' ? '2px solid #52c41a' : '1px solid #d9d9d9'
                  }}
                  title={
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <span style={{ fontWeight: 600, fontSize: '16px' }}>
                        {moduleName.replace('_', ' ').toUpperCase()}
                      </span>
                      {moduleName === 'base' && (
                        <Tag color="green" style={{ margin: 0 }}>{t('apps.config.coreModule')}</Tag>
                      )}
                    </div>
                  }
                >
                  <Row gutter={[16, 12]}>
                    <Col span={12}>
                      <div>
                        <Text strong>{t('apps.config.description')}:</Text>
                        <div style={{ marginTop: 4, color: '#666' }}>
                          {config.description}
                        </div>
                      </div>
                    </Col>
                    <Col span={12}>
                      <div>
                        <Text strong>{t('apps.config.author')}:</Text>
                        <div style={{ marginTop: 4, color: '#666' }}>
                          {config.author}
                        </div>
                      </div>
                    </Col>
                    <Col span={8}>
                      <div>
                        <Text strong>{t('apps.config.version')}:</Text>
                        <div style={{ marginTop: 4, color: '#666' }}>
                          {config.version}
                        </div>
                      </div>
                    </Col>
                    <Col span={8}>
                      <div>
                        <Text strong>{t('apps.config.category')}:</Text>
                        <div style={{ marginTop: 4, color: '#666' }}>
                          {config.category}
                        </div>
                      </div>
                    </Col>
                    <Col span={8}>
                      <div>
                        <Text strong>{t('apps.config.status')}:</Text>
                        <div style={{ marginTop: 4 }}>
                          <Space size="small">
                            <Tag color={config.auto_install ? 'green' : 'default'}>
                              {config.auto_install ? t('apps.config.autoInstall') : t('apps.config.manualInstall')}
                            </Tag>
                            <Tag color={config.hidden ? 'orange' : 'blue'}>
                              {config.hidden ? t('apps.config.hidden') : t('apps.config.visible')}
                            </Tag>
                            {config.required && (
                              <Tag color="red">{t('apps.config.required')}</Tag>
                            )}
                          </Space>
                        </div>
                      </div>
                    </Col>
                  </Row>
                  
                  {moduleName === 'base' && (
                    <Alert
                      message={t('apps.config.coreModuleTitle')}
                      description={t('apps.config.coreModuleDesc')}
                      type="success"
                      showIcon
                      style={{ marginTop: 16 }}
                    />
                  )}
                </Card>
              ))}
            </div>
          )}
        </div>
      </Modal>

      {/* Settings Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <SettingOutlined style={{ color: '#1890ff', fontSize: '18px' }} />
            <span>{t('apps.settings.title')}</span>
          </div>
        }
        open={settingsModalVisible}
        onCancel={() => setSettingsModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setSettingsModalVisible(false)}>
            {t('apps.settings.close')}
          </Button>
        ]}
        width={600}
      >
        <div>
          <Alert
            message={t('apps.settings.systemTitle')}
            description={t('apps.settings.systemDesc')}
            type="info"
            showIcon
            style={{ marginBottom: 24 }}
          />
          
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            <Card size="small" title={t('apps.settings.moduleManagement')}>
              <div>
                <Text>{t('apps.settings.moduleManagementDesc')}</Text>
                <div style={{ marginTop: 16 }}>
                  <Button 
                    type="primary" 
                    icon={<ToolOutlined />}
                    onClick={() => {
                      setSettingsModalVisible(false);
                      handleOpenConfig();
                    }}
                  >
                    {t('apps.settings.configureModule')}
                  </Button>
                </div>
              </div>
            </Card>
            
            <Card size="small" title={t('apps.settings.systemInfo')}>
              <div>
                <Text>{t('apps.settings.version')}: 1.0.0</Text>
                <br />
                <Text>{t('apps.settings.database')}: metisdb</Text>
                <br />
                <Text>{t('apps.settings.installedModules')}: {modules.filter(m => m.state === 'installed').length}</Text>
                <br />
                <Text>{t('apps.settings.systemModules')}: {getSystemModules().length}</Text>
                <br />
                <Text>{t('apps.settings.developmentModules')}: {getDevelopmentModules().length}</Text>
                <br />
                <Text>{t('apps.settings.hiddenModules')}: 2 (base, user_management)</Text>
              </div>
            </Card>
          </Space>
        </div>
      </Modal>

      {/* Advanced Filter Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <FilterOutlined style={{ color: '#1890ff', fontSize: '18px' }} />
            <span>{t('apps.filter.advanced')}</span>
          </div>
        }
        open={filterModalVisible}
        onCancel={() => setFilterModalVisible(false)}
        footer={[
          <Button key="reset" onClick={handleFilterReset}>
            {t('apps.filter.reset')}
          </Button>,
          <Button key="apply" type="primary" onClick={handleFilterApply}>
            {t('apps.filter.apply')}
          </Button>
        ]}
        width={500}
      >
        <div>
          <Alert
            message={t('apps.filter.title')}
            description={t('apps.filter.desc')}
            type="info"
            showIcon
            style={{ marginBottom: 24 }}
          />
          
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            <div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                <Text strong style={{ fontSize: '14px' }}>{t('apps.filter.category')}</Text>
                <Checkbox
                  checked={selectedCategories.length === getCategories().length && getCategories().length > 0}
                  indeterminate={selectedCategories.length > 0 && selectedCategories.length < getCategories().length}
                  onChange={(e) => handleSelectAllCategories(e.target.checked)}
                  style={{ fontSize: '13px' }}
                >
                  {t('apps.filter.selectAll')}
                </Checkbox>
              </div>
              
              <div style={{ 
                maxHeight: '250px', 
                overflowY: 'auto', 
                border: '1px solid #e8e8e8', 
                borderRadius: '8px',
                backgroundColor: '#fafafa'
              }}>
                {getCategories().length > 0 ? (
                  <div style={{ padding: '4px' }}>
                    {getCategories().map((category, index) => (
                      <div
                        key={category}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          padding: '12px 16px',
                          borderBottom: index < getCategories().length - 1 ? '1px solid #f0f0f0' : 'none',
                          backgroundColor: selectedCategories.includes(category) ? '#f6ffed' : 'transparent',
                          borderRadius: '6px',
                          margin: '2px 0',
                          transition: 'all 0.2s ease',
                          cursor: 'pointer'
                        }}
                        onClick={() => handleCategoryChange(category, !selectedCategories.includes(category))}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = selectedCategories.includes(category) ? '#f6ffed' : '#f5f5f5';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = selectedCategories.includes(category) ? '#f6ffed' : 'transparent';
                        }}
                      >
                        <Checkbox
                          checked={selectedCategories.includes(category)}
                          onChange={(e) => handleCategoryChange(category, e.target.checked)}
                          style={{ marginRight: '12px', flexShrink: 0 }}
                          onClick={(e) => e.stopPropagation()}
                        />
                        
                        <div style={{ 
                          display: 'flex', 
                          alignItems: 'center', 
                          flex: 1,
                          gap: '10px'
                        }}>
                          <div 
                            style={{ 
                              width: '10px', 
                              height: '10px', 
                              borderRadius: '50%', 
                              backgroundColor: getCategoryColor(category),
                              flexShrink: 0
                            }} 
                          />
                          <span style={{ 
                            fontSize: '14px', 
                            fontWeight: selectedCategories.includes(category) ? '500' : '400',
                            color: selectedCategories.includes(category) ? '#1890ff' : '#262626'
                          }}>
                            {category}
                          </span>
                        </div>
                        
                        <Tag 
                          color={selectedCategories.includes(category) ? 'blue' : 'default'}
                          style={{ 
                            marginLeft: 'auto',
                            fontSize: '11px',
                            padding: '2px 8px',
                            borderRadius: '10px',
                            border: 'none'
                          }}
                        >
                          {modules.filter(m => m.category === category).length}
                        </Tag>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div style={{ 
                    textAlign: 'center', 
                    padding: '40px 20px', 
                    color: '#999',
                    backgroundColor: 'white',
                    borderRadius: '6px',
                    margin: '8px'
                  }}>
                    <Text type="secondary" style={{ fontSize: '14px' }}>
                      {t('apps.filter.noCategory')}
                    </Text>
                  </div>
                )}
              </div>
              
              {selectedCategories.length > 0 && (
                <div style={{ 
                  marginTop: '12px',
                  padding: '8px 12px',
                  backgroundColor: '#f0f9ff',
                  borderRadius: '6px',
                  border: '1px solid #bae7ff'
                }}>
                  <Text type="secondary" style={{ fontSize: '12px', color: '#1890ff' }}>
                    {t('apps.filter.selected', { count: selectedCategories.length, categories: selectedCategories.join(', ') })}
                  </Text>
                </div>
              )}
            </div>
            
            <div style={{ 
              padding: '12px',
              backgroundColor: '#fafafa',
              borderRadius: '6px',
              border: '1px solid #f0f0f0'
            }}>
              <Text type="secondary" style={{ fontSize: '12px', color: '#666' }}>
                {t('apps.filter.hint')}
              </Text>
            </div>
          </Space>
        </div>
      </Modal>

      {/* User Profile Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <UserOutlined style={{ fontSize: '20px' }} />
            <span style={{ fontSize: '18px', fontWeight: 500 }}>{t('apps.profile.title')}</span>
          </div>
        }
        open={userProfileVisible}
        onCancel={() => setUserProfileVisible(false)}
        footer={null}
        width={380}
      >
        {user && (
          <>
            <div style={{ textAlign: 'center', marginBottom: 24, paddingTop: 12 }}>
              <Avatar size={80} style={{ backgroundColor: '#7265e6', fontSize: '36px', marginBottom: 12 }}>
                {user.full_name ? user.full_name[0].toUpperCase() : <UserOutlined />}
              </Avatar>
              <Title level={4} style={{ marginBottom: 4 }}>{user.full_name}</Title>
              <Text type="secondary">{user.email}</Text>
            </div>
            <Descriptions column={1} bordered size="small">
              <Descriptions.Item label={t('apps.profile.username')}>{user.username}</Descriptions.Item>
              <Descriptions.Item label={t('apps.profile.phone')}>{user.phone_number || t('apps.profile.notUpdated')}</Descriptions.Item>
              <Descriptions.Item label={t('apps.profile.status')}>
                {user.is_active ? <Tag color="success">{t('apps.profile.active')}</Tag> : <Tag color="error">{t('apps.profile.inactive')}</Tag>}
              </Descriptions.Item>
            </Descriptions>
            <Button 
              type="primary" 
              danger 
              block 
              onClick={handleLogout} 
              style={{ marginTop: 24 }}
              icon={<LogoutOutlined />}
            >
              {t('logout.button')}
            </Button>
          </>
        )}
      </Modal>
    </Layout>
  );
};

const AppsPageWrapper: React.FC = () => (
  <App>
    <AppsPage />
  </App>
);

export default AppsPageWrapper;