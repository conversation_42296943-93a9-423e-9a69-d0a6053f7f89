/* UserManagementPage.css - Responsive Design cho User Management Module */

/* Loại bỏ hiệu ứng hover của Ant Design Table */
.ant-table-wrapper {
  box-shadow: none !important;
}

.ant-table-wrapper:hover {
  box-shadow: none !important;
}

.ant-table {
  box-shadow: none !important;
}

.ant-table:hover {
  box-shadow: none !important;
}

.ant-table-tbody > tr:hover > td {
  background-color: transparent !important;
}

.ant-table-tbody > tr:hover {
  background-color: transparent !important;
}

.ant-table-tbody > tr:hover > td {
  border-color: transparent !important;
}

/* Loại bỏ hoàn toàn hiệu ứng hover của table rows */
.ant-table-tbody > tr {
  transition: none !important;
}

.ant-table-tbody > tr:hover {
  background-color: transparent !important;
  transform: none !important;
  box-shadow: none !important;
}

.ant-table-tbody > tr:hover > td {
  background-color: transparent !important;
  border-color: transparent !important;
  box-shadow: none !important;
}

/* Loại bỏ hiệu ứng hover của Card chứa table */
.ant-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
  transition: none !important;
}

.ant-card:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
  transform: none !important;
}

/* CSS cụ thể cho Card trong UserManagementPage */
.user-management-page-content .ant-card,
.user-management-page-content .ant-card:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
  transform: none !important;
  transition: none !important;
}

/* CSS cụ thể cho table trong UserManagementPage */
.user-management-page-content .ant-card .ant-table,
.user-management-page-content .ant-card .ant-table:hover {
  box-shadow: none !important;
}

.user-management-page-content .ant-card .ant-table-wrapper,
.user-management-page-content .ant-card .ant-table-wrapper:hover {
  box-shadow: none !important;
}

.user-management-page-content .ant-card .ant-table-tbody > tr:hover {
  background-color: transparent !important;
  transform: none !important;
  box-shadow: none !important;
}

.user-management-page-content .ant-card .ant-table-tbody > tr:hover > td {
  background-color: transparent !important;
  border-color: transparent !important;
  box-shadow: none !important;
}

/* Mobile First Approach */
@media (max-width: 768px) {
  .user-management-page-header {
    padding: 0 16px !important;
    height: 56px !important;
  }
  
  .user-management-page-header .ant-typography {
    font-size: 14px !important;
  }
  
  .user-management-page-header .user-management-title-responsive {
    font-size: 14px !important;
    line-height: 1.2 !important;
  }
  
  .user-management-page-header .ant-avatar {
    width: 28px !important;
    height: 28px !important;
  }
  
  .user-management-page-sider {
    position: fixed !important;
    top: 56px !important;
    left: 0;
    z-index: 999;
    height: calc(100vh - 56px) !important;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    width: 280px !important;
    background: #fff !important;
    box-shadow: 2px 0 8px rgba(0,0,0,0.15) !important;
    padding-top: 0 !important;
  }
  
  /* Khi sidebar mở (collapsed = false) */
  .user-management-page-sider:not(.collapsed) {
    transform: translateX(0);
  }
  
  /* Khi sidebar đóng (collapsed = true) */
  .user-management-page-sider.collapsed {
    transform: translateX(-100%);
  }
  
  .user-management-page-content {
    margin: 16px !important;
    padding: 16px !important;
    margin-left: 0 !important;
  }
  
  .user-management-page-main-content {
    margin: 16px !important;
    padding: 16px !important;
  }
  
  .user-management-breadcrumb {
    margin-bottom: 16px !important;
  }
  
  .user-management-overview-avatar {
    margin-bottom: 12px !important;
  }
  
  .user-management-overview-stats {
    margin-bottom: 12px !important;
  }
  
  .user-management-overview-stats .ant-space {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .user-management-overview-stats .ant-space-item {
    margin-bottom: 8px !important;
  }
  
  /* Mobile menu toggle button */
  .user-management-nav-toggle {
    height: 48px !important;
    padding: 0 12px !important;
  }
  
  .user-management-nav-toggle .ant-btn {
    width: 32px !important;
    height: 32px !important;
    font-size: 14px !important;
  }
  
  .user-management-nav-toggle.collapsed {
    justify-content: center !important;
    padding: 0 !important;
  }
  
  .user-management-nav-toggle.expanded {
    justify-content: flex-start !important;
    padding: 0 12px !important;
  }
  
  /* Mobile overlay - chỉ hiển thị khi sidebar mở */
  .user-management-page-sider::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }
  
  /* Chỉ hiển thị overlay khi sidebar mở */
  .user-management-page-sider:not(.collapsed)::before {
    opacity: 1;
    pointer-events: auto;
  }
  
  /* Mobile menu items */
  .user-management-nav-menu .ant-menu-item {
    height: 48px !important;
    line-height: 48px !important;
    margin: 4px 8px !important;
    border-radius: 8px !important;
  }
  
  .user-management-nav-menu .ant-menu-item:hover {
    background: #f0f7ff !important;
    color: #1890ff !important;
  }
  
  .user-management-nav-menu .ant-menu-item-selected {
    background: #e6f7ff !important;
    color: #1890ff !important;
    font-weight: 600 !important;
  }
}

/* Tablet */
@media (min-width: 769px) and (max-width: 1024px) {
  .user-management-page-header {
    padding: 0 20px !important;
  }
  
  .user-management-page-header .user-management-title-responsive {
    font-size: 15px !important;
    line-height: 1.3 !important;
  }
  
  .user-management-page-header .ant-avatar {
    width: 30px !important;
    height: 30px !important;
  }
  
  .user-management-page-content {
    margin: 20px !important;
    padding: 20px !important;
  }
  
  .user-management-page-sider {
    width: 240px !important;
  }
  
  .user-management-page-sider.collapsed {
    width: 80px !important;
  }
}

/* Desktop */
@media (min-width: 1025px) {
  .user-management-page-sider {
    width: 280px !important;
  }
  
  .user-management-page-sider.collapsed {
    width: 80px !important;
  }
  
  .user-management-page-header .user-management-title-responsive {
    font-size: 16px !important;
    line-height: 1.4 !important;
  }
  
  .user-management-page-header .ant-avatar {
    width: 32px !important;
    height: 32px !important;
  }
}

/* Common Styles */
.user-management-page-layout {
  min-height: 100vh;
}

.user-management-page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  height: 64px !important;
  padding: 0 24px !important;
}

.user-management-page-header .ant-typography {
  color: white !important;
  margin: 0 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.user-management-page-header .ant-avatar {
  flex-shrink: 0;
}

.user-management-page-header .ant-btn {
  color: white;
  border: none;
  background: transparent;
}

.user-management-page-header .ant-btn:hover {
  background: rgba(255,255,255,0.1);
  color: white;
}

.user-management-page-sider {
  background: #fff;
  box-shadow: 2px 0 8px rgba(0,0,0,0.1);
  position: sticky;
  top: 64px;
  height: calc(100vh - 64px);
  overflow: auto;
  padding-top: 0 !important;
}

.user-management-page-content {
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.user-management-page-main-content {
  background: #fff;
  border-radius: 8px;
  min-height: calc(100vh - 112px);
  overflow: auto;
  margin: 24px;
  padding: 24px;
}

.user-management-overview-card {
  margin-bottom: 24px;
}

.user-management-overview-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin-bottom: 16px;
}

.user-management-overview-stats {
  margin-bottom: 16px;
}

.user-management-overview-tags {
  margin-bottom: 16px;
}

.user-management-overview-description {
  font-size: 14px;
  line-height: 1.6;
}

.user-management-stats-card {
  text-align: center;
}

.user-management-stats-number {
  font-size: 24px;
  font-weight: bold;
}

.user-management-stats-label {
  font-size: 12px;
  color: #666;
}

.user-management-activity-card {
  font-size: 12px;
}

.user-management-activity-item {
  margin-bottom: 8px;
}

.user-management-activity-time {
  color: #666;
  margin-bottom: 4px;
}

.user-management-nav-menu {
  border-right: 0;
  height: calc(100% - 65px);
  overflow: auto;
}

.user-management-nav-toggle {
  display: none !important;
}

.user-management-breadcrumb {
  margin-bottom: 24px;
  line-height: 1.5;
}

.user-management-breadcrumb .ant-breadcrumb-item {
  display: flex;
  align-items: center;
}

.user-management-breadcrumb .ant-breadcrumb-link {
  display: flex;
  align-items: center;
  height: auto;
  line-height: 1.5;
}

.user-management-breadcrumb .ant-btn {
  display: inline-flex;
  align-items: center;
  height: auto;
  line-height: 1.5;
  padding: 0;
  border: none;
  background: none;
  color: #1890ff;
  text-decoration: none;
}

.user-management-breadcrumb .ant-btn:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.mobile-menu-toggle {
  display: none;
}

@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: block;
  }
  
  .desktop-menu-toggle {
    display: none;
  }
}

.user-management-content-area {
  transition: margin-left 0.3s ease;
}

@media (max-width: 768px) {
  .user-management-content-area {
    margin-left: 0 !important;
  }
  
  .user-management-content-area.sidebar-open {
    margin-left: 240px !important;
  }
}

.user-management-card-responsive {
  margin-bottom: 16px;
}

@media (max-width: 768px) {
  .user-management-card-responsive {
    margin-bottom: 12px;
  }
}

.user-management-button-responsive {
  height: 36px;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .user-management-button-responsive {
    height: 32px;
    font-size: 12px;
  }
}

.user-management-title-responsive {
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (max-width: 768px) {
  .user-management-title-responsive {
    font-size: 14px;
  }
}

.user-management-subtitle-responsive {
  font-size: 12px;
  color: rgba(255,255,255,0.9);
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (max-width: 768px) {
  .user-management-subtitle-responsive {
    font-size: 11px;
  }
}

.user-management-module-header {
  margin-bottom: 24px;
}

.user-management-module-sider {
  background: #fafafa;
}

.user-management-module-content {
  padding: 24px;
}

.user-management-module-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: none !important;
}

.user-management-module-card:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
  transform: none !important;
}

.user-management-system-config-section {
  margin-bottom: 32px;
}

.user-management-system-config-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 8px;
}

.user-management-system-config-item {
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 8px;
  border-left: 4px solid #1890ff;
}

.user-management-user-management-section {
  margin-bottom: 32px;
  padding: 24px;
  background: #f8f9fa;
  border-radius: 12px;
}

.user-management-user-management-header {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  text-align: center;
}

.user-management-data-management-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.user-management-data-management-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
}

.user-management-dev-tools-section {
  margin-bottom: 32px;
  padding: 24px;
  background: #fff3cd;
  border-radius: 12px;
  border: 1px solid #ffeaa7;
}

.user-management-dev-tools-header {
  font-size: 18px;
  font-weight: 600;
  color: #856404;
  margin-bottom: 16px;
  text-align: center;
}

.user-management-documentation-section {
  margin-bottom: 32px;
  padding: 24px;
  background: #d1ecf1;
  border-radius: 12px;
  border: 1px solid #bee5eb;
}

.user-management-documentation-title {
  font-size: 18px;
  font-weight: 600;
  color: #0c5460;
  margin-bottom: 16px;
  text-align: center;
} 