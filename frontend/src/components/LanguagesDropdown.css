.languages-dropdown {
  position: relative;
  display: inline-block;
  background: none !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.languages-dropdown-code {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  font-weight: 700;
  color: #fff;
  letter-spacing: 1px;
  text-transform: uppercase;
  border: none;
  background: none;
  outline: none;
  padding: 0 4px;
  margin: 0;
  transition: color 0.2s;
}
.languages-dropdown-code:focus,
.languages-dropdown-code:hover {
  color: #1677ff;
}

.languages-dropdown-menu {
  position: absolute;
  top: 110%;
  left: 0;
  min-width: 140px;
  background: #fff;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.08);
  z-index: 1000;
  padding: 4px 0;
  margin-top: 4px;
}

.languages-dropdown-item {
  padding: 10px 18px;
  font-size: 15px;
  color: #262626;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}

.languages-dropdown-item:hover {
  background: #f0f5ff;
  color: #1677ff;
}

.languages-dropdown-item.selected {
  background: #e6f7ff;
  color: #1677ff;
  font-weight: 600;
} 