import React, { useState, useRef, useEffect } from 'react';
import { Tooltip } from 'antd';
import { useLanguage } from '../contexts/LanguageContext';
import './LanguagesDropdown.css';

const LANGUAGES = [
  { code: 'vi', label: 'Tiếng Việt' },
  { code: 'en', label: 'English' }
];

const LanguagesDropdown: React.FC<{ style?: React.CSSProperties }> = ({ style }) => {
  const { language, setLanguage } = useLanguage();
  const [open, setOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        setOpen(false);
      }
    };
    if (open) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [open]);

  const handleSelect = (code: string) => {
    setLanguage(code);
    setOpen(false);
  };

  const current = LANGUAGES.find(l => l.code === language);
  const codeDisplay = (current?.code || 'vi').toUpperCase();

  return (
    <div className="languages-dropdown" ref={ref} style={style}>
      <Tooltip title="Chọn ngôn ngữ">
        <span
          className="languages-dropdown-code"
          onClick={() => setOpen(o => !o)}
          tabIndex={0}
          role="button"
        >
          {codeDisplay}
        </span>
      </Tooltip>
      {open && (
        <div className="languages-dropdown-menu">
          {LANGUAGES.map(lang => (
            <div
              key={lang.code}
              className={`languages-dropdown-item${language === lang.code ? ' selected' : ''}`}
              onClick={() => handleSelect(lang.code)}
            >
              {lang.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default LanguagesDropdown; 