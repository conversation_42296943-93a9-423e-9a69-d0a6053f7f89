.site-parent-dropdown {
  position: relative;
  width: 100%;
}

.site-parent-dropdown.disabled {
  pointer-events: none;
  opacity: 0.6;
}

.dropdown-input {
  position: relative;
  cursor: pointer;
}

.dropdown-input.open .ant-input {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.dropdown-input .ant-input {
  cursor: pointer;
  padding-right: 60px;
}

.dropdown-input .ant-input:focus {
  cursor: text;
}

.dropdown-suffix {
  display: flex;
  align-items: center;
  gap: 4px;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

.clear-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #d9d9d9;
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
}

.clear-icon:hover {
  background-color: #bfbfbf;
}

.arrow-icon {
  color: #bfbfbf;
  font-size: 12px;
  transition: transform 0.2s, color 0.2s;
}

.arrow-icon.open {
  transform: rotate(180deg);
  color: #1890ff;
}

.dropdown-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1050;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  max-height: 256px;
  overflow-y: auto;
  margin-top: 4px;
}

.dropdown-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  color: #8c8c8c;
  font-size: 14px;
}

.dropdown-hint {
  padding: 8px 12px;
  background-color: #f0f8ff;
  border-bottom: 1px solid #e6f7ff;
  font-size: 12px;
  color: #1890ff;
  border-left: 3px solid #1890ff;
}

.dropdown-hint strong {
  font-weight: 600;
}

.dropdown-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f0f0f0;
}

.dropdown-option:last-child {
  border-bottom: none;
}

.dropdown-option:hover {
  background-color: #f5f5f5;
}

.dropdown-option.selected {
  background-color: #e6f7ff;
  border-left: 3px solid #1890ff;
}

.dropdown-option.active {
  background-color: #e6f7ff;
  color: #1890ff;
  border-left: 3px solid #1890ff;
}

.dropdown-option.active .option-code {
  color: #1890ff;
  font-weight: 500;
}

.dropdown-option.selected .option-code {
  color: #1890ff;
  font-weight: 500;
}

.option-code {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: #595959;
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  min-width: 80px;
  text-align: center;
  font-weight: 500;
}

.option-name {
  flex: 1;
  font-size: 14px;
  color: #262626;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-empty {
  padding: 16px;
  text-align: center;
}

.dropdown-empty .ant-empty {
  margin: 0;
}

.dropdown-empty .ant-empty-description {
  color: #8c8c8c;
  font-size: 14px;
}

/* Scrollbar styling */
.dropdown-options::-webkit-scrollbar {
  width: 6px;
}

.dropdown-options::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.dropdown-options::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.dropdown-options::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive design */
@media (max-width: 768px) {
  .dropdown-options {
    max-height: 200px;
  }
  
  .dropdown-option {
    padding: 10px 12px;
  }
  
  .option-code {
    min-width: 70px;
    font-size: 12px;
  }
  
  .option-name {
    font-size: 13px;
  }
}

/* Enhanced focus styles for accessibility */
.dropdown-input .ant-input:focus,
.dropdown-input .ant-input:focus-visible {
  outline: 2px solid #40a9ff !important;
  outline-offset: 1px !important;
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.dropdown-input.open .ant-input:focus,
.dropdown-input.open .ant-input:focus-visible {
  outline: 2px solid #40a9ff !important;
  outline-offset: 1px !important;
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3) !important;
}

.dropdown-option:focus,
.dropdown-option:focus-visible {
  outline: 2px solid #1890ff !important;
  outline-offset: -2px !important;
  background-color: #e6f7ff !important;
}

/* Ensure dropdown container can receive focus */
.site-parent-dropdown {
  position: relative;
}

.site-parent-dropdown:focus-within .dropdown-input .ant-input {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Animation for dropdown open/close */
.dropdown-options {
  animation: slideDown 0.2s ease-out;
  transform-origin: top;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: scaleY(0.8) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scaleY(1) translateY(0);
  }
}
