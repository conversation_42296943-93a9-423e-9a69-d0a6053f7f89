import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Input, Empty, Spin } from 'antd';
import type { InputRef } from 'antd';
import { SearchOutlined, DownOutlined } from '@ant-design/icons';
import './SearchDropdown.css';

interface User {
  id: string;
  username: string;
  email: string;
  full_name: string;
  is_active: boolean;
}

interface UserOption {
  id: string;
  username: string;
  email: string;
  full_name: string;
  label: string;
}

interface SearchUserDropdownProps {
  value?: string;
  onChange?: (value: string | undefined) => void;
  placeholder?: string;
  allowClear?: boolean;
  disabled?: boolean;
  users: User[];
  loading?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

const SearchUserDropdown: React.FC<SearchUserDropdownProps> = ({
  value,
  onChange,
  placeholder = "Tìm kiếm và chọn người dùng",
  allowClear = true,
  disabled = false,
  users = [],
  loading = false,
  className = '',
  style = {}
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<InputRef>(null);
  const optionsRef = useRef<HTMLDivElement>(null);

  // Convert users to options
  const userOptions = useMemo(() => {
    return users.map(user => ({
      id: user.id,
      username: user.username,
      email: user.email,
      full_name: user.full_name,
      label: `${user.username} - ${user.full_name} (${user.email})`
    }));
  }, [users]);

  // Filter options based on search text
  const filteredOptions = useMemo(() => {
    if (!searchText.trim()) {
      return userOptions;
    }
    
    const searchLower = searchText.toLowerCase();
    return userOptions.filter(option => 
      option.username.toLowerCase().includes(searchLower) ||
      option.full_name.toLowerCase().includes(searchLower) ||
      option.email.toLowerCase().includes(searchLower)
    );
  }, [userOptions, searchText]);

  // Get selected option display text
  const selectedOption = userOptions.find(option => option.id === value);
  const displayText = selectedOption ? selectedOption.label : '';

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchText('');
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) {
      if (e.key === 'Enter' || e.key === 'ArrowDown' || e.key === ' ') {
        e.preventDefault();
        setIsOpen(true);
        setSelectedIndex(filteredOptions.length > 0 ? 0 : -1);
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev =>
          prev < filteredOptions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev =>
          prev > 0 ? prev - 1 : filteredOptions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < filteredOptions.length) {
          handleOptionSelect(filteredOptions[selectedIndex]);
        }
        break;
      case 'Tab':
        if (e.shiftKey) {
          setIsOpen(false);
          setSearchText('');
          setSelectedIndex(-1);
          return;
        }

        if (searchText.trim() && filteredOptions.length > 0) {
          e.preventDefault();
          handleOptionSelect(filteredOptions[0]);
        } else if (selectedIndex >= 0 && selectedIndex < filteredOptions.length) {
          e.preventDefault();
          handleOptionSelect(filteredOptions[selectedIndex]);
        } else {
          setIsOpen(false);
          setSearchText('');
          setSelectedIndex(-1);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setIsOpen(false);
        setSearchText('');
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Auto select first option when filtered options change
  useEffect(() => {
    if (isOpen && searchText.trim() && filteredOptions.length > 0) {
      setSelectedIndex(0);
    } else if (isOpen && !searchText.trim() && filteredOptions.length > 0) {
      setSelectedIndex(0);
    } else if (filteredOptions.length === 0) {
      setSelectedIndex(-1);
    }
  }, [filteredOptions, isOpen, searchText]);

  // Scroll selected option into view
  useEffect(() => {
    if (isOpen && selectedIndex >= 0 && optionsRef.current) {
      const selectedElement = optionsRef.current.children[selectedIndex] as HTMLElement;
      if (selectedElement) {
        selectedElement.scrollIntoView({
          block: 'nearest',
          behavior: 'smooth'
        });
      }
    }
  }, [selectedIndex, isOpen]);

  const handleInputClick = () => {
    if (disabled) return;
    setIsOpen(!isOpen);
    if (!isOpen) {
      setSelectedIndex(filteredOptions.length > 0 ? 0 : -1);
    }
  };

  const handleInputFocus = () => {
    if (disabled) return;
    if (!isOpen) {
      setIsOpen(true);
      setSelectedIndex(filteredOptions.length > 0 ? 0 : -1);
    }
  };

  const handleInputBlur = (e: React.FocusEvent) => {
    const relatedTarget = e.relatedTarget as HTMLElement;
    if (dropdownRef.current && relatedTarget && dropdownRef.current.contains(relatedTarget)) {
      return;
    }

    setTimeout(() => {
      setIsOpen(false);
      setSearchText('');
      setSelectedIndex(-1);
    }, 150);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSearchText = e.target.value;
    setSearchText(newSearchText);

    if (newSearchText.trim()) {
      setSelectedIndex(0);
    } else {
      setSelectedIndex(filteredOptions.length > 0 ? 0 : -1);
    }
  };

  const handleOptionSelect = (option: UserOption) => {
    onChange?.(option.id);
    setIsOpen(false);
    setSearchText('');
    setSelectedIndex(-1);
    inputRef.current?.blur();
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange?.(undefined);
    setSearchText('');
    setSelectedIndex(-1);
  };

  return (
    <div
      ref={dropdownRef}
      className={`search-user-dropdown ${className} ${disabled ? 'disabled' : ''}`}
      style={style}
      tabIndex={disabled ? -1 : 0}
      onFocus={(e) => {
        if (e.target === e.currentTarget && inputRef.current) {
          inputRef.current.focus();
        }
      }}
    >
      <div 
        className={`dropdown-input ${isOpen ? 'open' : ''}`}
        onClick={handleInputClick}
      >
        <Input
          ref={inputRef}
          value={isOpen ? searchText : displayText}
          onChange={handleSearchChange}
          onKeyDown={handleKeyDown}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          placeholder={placeholder}
          disabled={disabled}
          prefix={<SearchOutlined />}
          suffix={
            <div className="dropdown-suffix">
              {loading && <Spin size="small" />}
              {allowClear && value && !loading && (
                <span
                  className="clear-icon"
                  onClick={handleClear}
                >
                  ×
                </span>
              )}
              <DownOutlined className={`arrow-icon ${isOpen ? 'open' : ''}`} />
            </div>
          }
          readOnly={!isOpen}
        />
      </div>

      {isOpen && (
        <div className="dropdown-options" ref={optionsRef}>
          {loading ? (
            <div className="dropdown-loading">
              <Spin size="small" />
              <span>Đang tải...</span>
            </div>
          ) : filteredOptions.length > 0 ? (
            <>
              {searchText.trim() && selectedIndex >= 0 && (
                <div className="dropdown-hint">
                  <span>Nhấn Tab hoặc Enter để chọn: </span>
                  <strong>{filteredOptions[selectedIndex].label}</strong>
                </div>
              )}
              {filteredOptions.map((option, index) => (
                <div
                  key={option.id}
                  className={`dropdown-option ${index === selectedIndex ? 'selected' : ''} ${value === option.id ? 'active' : ''}`}
                  onClick={() => handleOptionSelect(option)}
                  onMouseEnter={() => setSelectedIndex(index)}
                >
                  <span className="option-username">{option.username}</span>
                  <span className="option-fullname">{option.full_name}</span>
                  <span className="option-email">{option.email}</span>
                </div>
              ))}
            </>
          ) : (
            <div className="dropdown-empty">
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={searchText ? 'Không tìm thấy người dùng phù hợp' : 'Không có dữ liệu'}
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SearchUserDropdown; 