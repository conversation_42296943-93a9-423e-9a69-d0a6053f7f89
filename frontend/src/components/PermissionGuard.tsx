import React from 'react';
import { usePermissions } from '../contexts/PermissionContext';

interface PermissionGuardProps {
  children: React.ReactNode;
  permission?: string;
  module?: string;
  menu?: string;
  button?: string;
  fallback?: React.ReactNode;
  requireAll?: boolean; // Nếu true, cần tất cả permissions. Nếu false, chỉ cần 1 permission
}

const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permission,
  module,
  menu,
  button,
  fallback = null,
  requireAll = false
}) => {
  const { hasPermission, hasModulePermission, hasMenuPermission, hasButtonPermission, loading } = usePermissions();

  // Nếu đang loading permissions, hiển thị children (để tránh flicker)
  if (loading) {
    return <>{children}</>;
  }

  const checks: boolean[] = [];

  // Kiểm tra permission code trực tiếp
  if (permission) {
    checks.push(hasPermission(permission));
  }

  // Kiểm tra module permission
  if (module) {
    checks.push(hasModulePermission(module));
  }

  // Kiểm tra menu permission
  if (menu) {
    checks.push(hasMenuPermission(menu));
  }

  // Kiểm tra button permission
  if (button) {
    checks.push(hasButtonPermission(button));
  }

  // Nếu không có permission nào được chỉ định, hiển thị children
  if (checks.length === 0) {
    return <>{children}</>;
  }

  // Kiểm tra logic
  const hasAccess = requireAll 
    ? checks.every(check => check) // Tất cả permissions phải có
    : checks.some(check => check);  // Chỉ cần 1 permission

  return hasAccess ? <>{children}</> : <>{fallback}</>;
};

export default PermissionGuard;
