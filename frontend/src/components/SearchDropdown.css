/* Common styles for search dropdowns */
.search-user-dropdown,
.search-role-dropdown {
  position: relative;
  width: 100%;
}

.search-user-dropdown.disabled,
.search-role-dropdown.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.dropdown-input {
  position: relative;
  cursor: pointer;
}

.dropdown-input.open {
  z-index: 1001;
}

.dropdown-input .ant-input {
  cursor: pointer;
  background-color: #fff;
}

.dropdown-input.open .ant-input {
  cursor: text;
}

.dropdown-suffix {
  display: flex;
  align-items: center;
  gap: 4px;
}

.clear-icon {
  cursor: pointer;
  color: #999;
  font-size: 12px;
  padding: 2px;
  border-radius: 50%;
  transition: all 0.2s;
}

.clear-icon:hover {
  background-color: #f0f0f0;
  color: #666;
}

.arrow-icon {
  color: #999;
  transition: transform 0.2s;
}

.arrow-icon.open {
  transform: rotate(180deg);
}

.dropdown-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-top: none;
  border-radius: 0 0 6px 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  max-height: 250px;
  overflow-y: auto;
  z-index: 1000;
}

.dropdown-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  gap: 8px;
  color: #666;
}

.dropdown-hint {
  padding: 8px 12px;
  background-color: #f6f8fa;
  border-bottom: 1px solid #e1e4e8;
  font-size: 12px;
  color: #666;
}

.dropdown-option {
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  gap: 2px;
  transition: background-color 0.2s;
}

.dropdown-option:hover {
  background-color: #f5f5f5;
}

.dropdown-option.selected {
  background-color: #e6f7ff;
}

.dropdown-option.active {
  background-color: #f0f9ff;
  border-left: 3px solid #1890ff;
}

.dropdown-option .option-username,
.dropdown-option .option-name {
  font-weight: 500;
  color: #333;
}

.dropdown-option .option-fullname,
.dropdown-option .option-email,
.dropdown-option .option-description {
  font-size: 12px;
  color: #666;
}

.dropdown-empty {
  padding: 16px;
  text-align: center;
}

/* Specific styles for user dropdown */
.search-user-dropdown .dropdown-option {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
}

.search-user-dropdown .option-username {
  grid-column: 1;
  font-weight: 500;
}

.search-user-dropdown .option-fullname {
  grid-column: 1;
  font-size: 12px;
  color: #666;
}

.search-user-dropdown .option-email {
  grid-column: 2;
  font-size: 12px;
  color: #999;
  text-align: right;
}

/* Specific styles for role dropdown */
.search-role-dropdown .dropdown-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.search-role-dropdown .option-name {
  font-weight: 500;
  color: #333;
}

.search-role-dropdown .option-description {
  font-size: 12px;
  color: #666;
  font-style: italic;
} 