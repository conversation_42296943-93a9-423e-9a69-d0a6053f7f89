import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Input, Empty, Spin } from 'antd';
import type { InputRef } from 'antd';
import { SearchOutlined, DownOutlined } from '@ant-design/icons';
import './SiteParentDropdown.css';

interface Site {
  id: number;
  code: string;
  name: string;
  descriptions?: string;
  is_activated: boolean;
  parent_id?: number;
  children?: Site[];
}

interface SiteOption {
  id: number;
  code: string;
  name: string;
  label: string;
}

interface SiteParentDropdownProps {
  value?: number;
  onChange?: (value: number | undefined) => void;
  placeholder?: string;
  allowClear?: boolean;
  disabled?: boolean;
  sites: Site[];
  loading?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

const SiteParentDropdown: React.FC<SiteParentDropdownProps> = ({
  value,
  onChange,
  placeholder = "Chọn Site cha (không bắt buộc)",
  allowClear = true,
  disabled = false,
  sites = [],
  loading = false,
  className = '',
  style = {}
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<InputRef>(null);
  const optionsRef = useRef<HTMLDivElement>(null);

  // Flatten sites data và cache
  const siteOptions = useMemo(() => {
    const flattenSites = (siteList: Site[]): SiteOption[] => {
      const result: SiteOption[] = [];
      
      const addSite = (site: Site) => {
        result.push({
          id: site.id,
          code: site.code,
          name: site.name,
          label: `${site.code} - ${site.name}`
        });
        
        if (site.children && site.children.length > 0) {
          site.children.forEach(addSite);
        }
      };
      
      siteList.forEach(addSite);
      return result;
    };
    
    return flattenSites(sites);
  }, [sites]);

  // Filter options based on search text
  const filteredOptions = useMemo(() => {
    if (!searchText.trim()) {
      return siteOptions;
    }
    
    const searchLower = searchText.toLowerCase();
    return siteOptions.filter(option => 
      option.code.toLowerCase().includes(searchLower) ||
      option.name.toLowerCase().includes(searchLower)
    );
  }, [siteOptions, searchText]);

  // Get selected option display text
  const selectedOption = siteOptions.find(option => option.id === value);
  const displayText = selectedOption ? selectedOption.label : '';

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchText('');
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) {
      if (e.key === 'Enter' || e.key === 'ArrowDown' || e.key === ' ') {
        e.preventDefault();
        setIsOpen(true);
        setSelectedIndex(filteredOptions.length > 0 ? 0 : -1);
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev =>
          prev < filteredOptions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev =>
          prev > 0 ? prev - 1 : filteredOptions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < filteredOptions.length) {
          handleOptionSelect(filteredOptions[selectedIndex]);
        }
        break;
      case 'Tab':
        // Xử lý Shift+Tab (tab ngược)
        if (e.shiftKey) {
          // Đóng dropdown và cho phép tab ngược bình thường
          setIsOpen(false);
          setSearchText('');
          setSelectedIndex(-1);
          // Không preventDefault để cho phép tab ngược tự nhiên
          return;
        }

        // Auto complete với option đầu tiên khi có search text
        if (searchText.trim() && filteredOptions.length > 0) {
          e.preventDefault();
          handleOptionSelect(filteredOptions[0]);
        } else if (selectedIndex >= 0 && selectedIndex < filteredOptions.length) {
          e.preventDefault();
          handleOptionSelect(filteredOptions[selectedIndex]);
        } else {
          // Cho phép tab ra khỏi component
          setIsOpen(false);
          setSearchText('');
          setSelectedIndex(-1);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setIsOpen(false);
        setSearchText('');
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Auto select first option when filtered options change
  useEffect(() => {
    if (isOpen && searchText.trim() && filteredOptions.length > 0) {
      setSelectedIndex(0);
    } else if (isOpen && !searchText.trim() && filteredOptions.length > 0) {
      setSelectedIndex(0);
    } else if (filteredOptions.length === 0) {
      setSelectedIndex(-1);
    }
  }, [filteredOptions, isOpen, searchText]);

  // Scroll selected option into view
  useEffect(() => {
    if (isOpen && selectedIndex >= 0 && optionsRef.current) {
      const selectedElement = optionsRef.current.children[selectedIndex] as HTMLElement;
      if (selectedElement) {
        selectedElement.scrollIntoView({
          block: 'nearest',
          behavior: 'smooth'
        });
      }
    }
  }, [selectedIndex, isOpen]);

  const handleInputClick = () => {
    if (disabled) return;
    setIsOpen(!isOpen);
    if (!isOpen) {
      setSelectedIndex(filteredOptions.length > 0 ? 0 : -1);
    }
  };

  const handleInputFocus = () => {
    if (disabled) return;
    if (!isOpen) {
      setIsOpen(true);
      setSelectedIndex(filteredOptions.length > 0 ? 0 : -1);
    }
  };

  const handleInputBlur = (e: React.FocusEvent) => {
    // Kiểm tra xem focus có chuyển đến element khác trong dropdown không
    const relatedTarget = e.relatedTarget as HTMLElement;
    if (dropdownRef.current && relatedTarget && dropdownRef.current.contains(relatedTarget)) {
      return; // Không đóng dropdown nếu focus vẫn trong component
    }

    // Delay để cho phép click event được xử lý trước
    setTimeout(() => {
      setIsOpen(false);
      setSearchText('');
      setSelectedIndex(-1);
    }, 150);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSearchText = e.target.value;
    setSearchText(newSearchText);

    // Auto select first option when searching
    if (newSearchText.trim()) {
      setSelectedIndex(0);
    } else {
      setSelectedIndex(filteredOptions.length > 0 ? 0 : -1);
    }
  };

  const handleOptionSelect = (option: SiteOption) => {
    onChange?.(option.id);
    setIsOpen(false);
    setSearchText('');
    setSelectedIndex(-1);
    inputRef.current?.blur();
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange?.(undefined);
    setSearchText('');
    setSelectedIndex(-1);
  };

  return (
    <div
      ref={dropdownRef}
      className={`site-parent-dropdown ${className} ${disabled ? 'disabled' : ''}`}
      style={style}
      tabIndex={disabled ? -1 : 0}
      onFocus={(e) => {
        // Chỉ focus input nếu focus đến container chính
        if (e.target === e.currentTarget && inputRef.current) {
          inputRef.current.focus();
        }
      }}
    >
      <div 
        className={`dropdown-input ${isOpen ? 'open' : ''}`}
        onClick={handleInputClick}
      >
        <Input
          ref={inputRef}
          value={isOpen ? searchText : displayText}
          onChange={handleSearchChange}
          onKeyDown={handleKeyDown}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          placeholder={placeholder}
          disabled={disabled}
          prefix={<SearchOutlined />}
          suffix={
            <div className="dropdown-suffix">
              {loading && <Spin size="small" />}
              {allowClear && value && !loading && (
                <span
                  className="clear-icon"
                  onClick={handleClear}
                >
                  ×
                </span>
              )}
              <DownOutlined className={`arrow-icon ${isOpen ? 'open' : ''}`} />
            </div>
          }
          readOnly={!isOpen}
        />
      </div>

      {isOpen && (
        <div className="dropdown-options" ref={optionsRef}>
          {loading ? (
            <div className="dropdown-loading">
              <Spin size="small" />
              <span>Đang tải...</span>
            </div>
          ) : filteredOptions.length > 0 ? (
            <>
              {searchText.trim() && selectedIndex >= 0 && (
                <div className="dropdown-hint">
                  <span>Nhấn Tab hoặc Enter để chọn: </span>
                  <strong>{filteredOptions[selectedIndex].label}</strong>
                </div>
              )}
              {filteredOptions.map((option, index) => (
                <div
                  key={option.id}
                  className={`dropdown-option ${index === selectedIndex ? 'selected' : ''} ${value === option.id ? 'active' : ''}`}
                  onClick={() => handleOptionSelect(option)}
                  onMouseEnter={() => setSelectedIndex(index)}
                >
                  <span className="option-code">{option.code}</span>
                  <span className="option-name">{option.name}</span>
                </div>
              ))}
            </>
          ) : (
            <div className="dropdown-empty">
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={searchText ? 'Không tìm thấy Site phù hợp' : 'Không có dữ liệu'}
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SiteParentDropdown;
