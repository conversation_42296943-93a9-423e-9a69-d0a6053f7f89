#!/usr/bin/env python3
"""
Script để gán role admin cho user admin
"""
import requests
import json

# API URLs
BASE_URL = "http://localhost:8000/api/user-management"
USER_ID = "d3f3565c-08fe-4f04-b61a-cedb421fc628"  # admin user ID
ADMIN_ROLE_ID = 1  # Giả sử role admin có ID = 1

def assign_role_to_user():
    """Gán role cho user"""
    print(f"🔄 Gán role {ADMIN_ROLE_ID} cho user {USER_ID}...")
    
    payload = {
        "user_id": USER_ID,
        "role_id": ADMIN_ROLE_ID
    }
    
    try:
        response = requests.post(f"{BASE_URL}/user-roles", json=payload)
        print(f"📊 Response status: {response.status_code}")
        print(f"📊 Response body: {response.text}")
        
        if response.status_code == 200:
            print("✅ Gán role thành công!")
        else:
            print("❌ Gán role thất bại!")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")

def check_user_permissions():
    """Kiểm tra permissions của user sau khi gán role"""
    print(f"🔍 Kiểm tra permissions của user {USER_ID}...")
    
    try:
        response = requests.get(f"{BASE_URL}/users/{USER_ID}/permissions")
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            permissions = response.json()
            print(f"✅ User có {len(permissions)} permissions:")
            for perm in permissions:
                print(f"  - {perm['code']} ({perm['type']}): {perm['name']}")
        else:
            print("❌ Không thể lấy permissions!")
            print(f"📊 Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")

def check_roles():
    """Kiểm tra danh sách roles có sẵn"""
    print("🔍 Kiểm tra danh sách roles...")
    
    try:
        response = requests.get(f"{BASE_URL}/roles")
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            roles = response.json()
            print(f"✅ Có {len(roles)} roles:")
            for role in roles:
                print(f"  - ID {role['id']}: {role['name']} - {role['description']}")
        else:
            print("❌ Không thể lấy roles!")
            print(f"📊 Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")

def check_role_permissions():
    """Kiểm tra permissions của role admin"""
    print(f"🔍 Kiểm tra permissions của role {ADMIN_ROLE_ID}...")
    
    try:
        response = requests.get(f"{BASE_URL}/roles/{ADMIN_ROLE_ID}/permissions")
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            permission_ids = response.json()
            print(f"✅ Role có {len(permission_ids)} permissions: {permission_ids}")
        else:
            print("❌ Không thể lấy role permissions!")
            print(f"📊 Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    print("🚀 Bắt đầu gán role admin...")
    
    # 1. Kiểm tra roles có sẵn
    check_roles()
    print()
    
    # 2. Kiểm tra permissions của role admin
    check_role_permissions()
    print()
    
    # 3. Gán role cho user
    assign_role_to_user()
    print()
    
    # 4. Kiểm tra permissions của user sau khi gán
    check_user_permissions()
    
    print("🏁 Hoàn thành!")
