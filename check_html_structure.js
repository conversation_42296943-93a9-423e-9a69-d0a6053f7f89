// Script để kiểm tra cấu trúc HTML và tìm Role permissions
const puppeteer = require('puppeteer');

async function checkHTMLStructure() {
  console.log('🚀 Kiểm tra cấu trúc HTML...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  try {
    // Đăng nhập
    console.log('🔐 Đăng nhập...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    await page.type('input[placeholder="Tên đăng nhập"]', 'admin');
    await page.type('input[placeholder="Mật khẩu"]', 'admin123');
    await page.click('button[type="submit"]');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Navigate đến user-management
    console.log('🔄 Navigate đến user-management...');
    await page.goto('http://localhost:5173/user-management', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Lấy toàn bộ HTML
    console.log('📋 Lấy cấu trúc HTML...');
    const htmlStructure = await page.evaluate(() => {
      // Tìm tất cả text có chứa "Role permissions"
      const allElements = Array.from(document.querySelectorAll('*'));
      const rolePermElements = allElements.filter(el => 
        el.textContent && el.textContent.includes('Role permissions')
      );
      
      // Tìm tất cả các element có thể click được
      const clickableElements = allElements.filter(el => {
        const style = window.getComputedStyle(el);
        return (
          el.tagName === 'BUTTON' ||
          el.tagName === 'A' ||
          style.cursor === 'pointer' ||
          el.onclick !== null ||
          el.getAttribute('role') === 'tab' ||
          el.className.includes('tab')
        );
      }).slice(0, 20);
      
      // Tìm tất cả text chứa "Lưu"
      const saveElements = allElements.filter(el => 
        el.textContent && el.textContent.trim() === 'Lưu'
      );
      
      return {
        rolePermElements: rolePermElements.map(el => ({
          tag: el.tagName,
          text: el.textContent.trim(),
          className: el.className,
          id: el.id,
          parentTag: el.parentElement?.tagName,
          parentClass: el.parentElement?.className
        })),
        clickableElements: clickableElements.map(el => ({
          tag: el.tagName,
          text: el.textContent?.trim().substring(0, 50),
          className: el.className,
          id: el.id
        })),
        saveElements: saveElements.map(el => ({
          tag: el.tagName,
          text: el.textContent.trim(),
          className: el.className,
          id: el.id,
          disabled: el.disabled,
          parentTag: el.parentElement?.tagName,
          parentClass: el.parentElement?.className
        }))
      };
    });
    
    console.log('🎯 Elements chứa "Role permissions":', JSON.stringify(htmlStructure.rolePermElements, null, 2));
    console.log('🖱️ Clickable elements:', JSON.stringify(htmlStructure.clickableElements, null, 2));
    console.log('💾 Save elements:', JSON.stringify(htmlStructure.saveElements, null, 2));
    
    // Thử click vào element chứa "Role permissions"
    if (htmlStructure.rolePermElements.length > 0) {
      console.log('✅ Tìm thấy Role permissions, thử click...');
      
      await page.evaluate(() => {
        const allElements = Array.from(document.querySelectorAll('*'));
        const rolePermElement = allElements.find(el => 
          el.textContent && el.textContent.includes('Role permissions')
        );
        
        if (rolePermElement) {
          console.log('RolePermissionsPage: Clicking on Role permissions element');
          rolePermElement.click();
        }
      });
      
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Kiểm tra lại sau khi click
      console.log('🔍 Kiểm tra lại sau khi click...');
      const afterClickStructure = await page.evaluate(() => {
        const allElements = Array.from(document.querySelectorAll('*'));
        const saveElements = allElements.filter(el => 
          el.textContent && el.textContent.trim() === 'Lưu'
        );
        
        return {
          saveElements: saveElements.map(el => ({
            tag: el.tagName,
            text: el.textContent.trim(),
            className: el.className,
            id: el.id,
            disabled: el.disabled,
            visible: el.offsetParent !== null
          }))
        };
      });
      
      console.log('💾 Save elements sau khi click:', JSON.stringify(afterClickStructure.saveElements, null, 2));
      
      // Thử click button Lưu nếu có
      if (afterClickStructure.saveElements.length > 0) {
        console.log('🖱️ Thử click button Lưu...');
        
        await page.evaluate(() => {
          const allElements = Array.from(document.querySelectorAll('*'));
          const saveButton = allElements.find(el => 
            el.textContent && el.textContent.trim() === 'Lưu' && !el.disabled
          );
          
          if (saveButton) {
            console.log('RolePermissionsPage: Clicking Save button');
            saveButton.click();
          }
        });
        
        await new Promise(resolve => setTimeout(resolve, 3000));
        console.log('✅ Đã click button Lưu');
      }
    }
    
    console.log('⏳ Giữ browser mở 20 giây để quan sát...');
    await new Promise(resolve => setTimeout(resolve, 20000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành');
  }
}

checkHTMLStructure().catch(console.error);
