// Script đơn giản để mở browser cho user test manual
const puppeteer = require('puppeteer');

async function openBrowserForManualTest() {
  console.log('🚀 Mở browser để test manual auto-check permissions...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs từ RolePermissionsPage
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('RolePermissionsPage')) {
      console.log(`🔍 Frontend: ${text}`);
    }
  });
  
  try {
    // Đăng nhập
    console.log('🔐 Đăng nhập...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    await page.type('input[placeholder="Tên đăng nhập"]', 'admin');
    await page.type('input[placeholder="Mật khẩu"]', 'admin123');
    await page.click('button[type="submit"]');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Navigate đến user-management
    console.log('🔄 Navigate đến user-management...');
    await page.goto('http://localhost:5173/user-management', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('✅ Browser đã sẵn sàng!');
    console.log('');
    console.log('📝 HƯỚNG DẪN TEST AUTO-CHECK PERMISSIONS:');
    console.log('');
    console.log('   🎯 BƯỚC 1: Click vào menu "Role permissions" ở sidebar bên trái');
    console.log('');
    console.log('   🎯 BƯỚC 2: Test auto-check với các role sau:');
    console.log('      - Chọn role "SuperAdmin" → Sẽ auto-check permissions [1, 2]');
    console.log('      - Chọn role "admin" → Sẽ auto-check permissions [1, 2, 3]');
    console.log('      - Chọn role khác → Sẽ clear tất cả checkboxes');
    console.log('');
    console.log('   ✅ KẾT QUẢ MONG ĐỢI:');
    console.log('      - Khi chọn role, permissions tương ứng sẽ được tự động check');
    console.log('      - Console sẽ hiển thị logs debug về quá trình load permissions');
    console.log('');
    console.log('   🔍 THEO DÕI CONSOLE LOGS:');
    console.log('      - Mở Developer Tools (F12) → Console tab');
    console.log('      - Quan sát logs bắt đầu với "RolePermissionsPage:"');
    console.log('');
    console.log('⏳ Browser sẽ mở trong 10 phút để bạn test...');
    console.log('');
    
    // Giữ browser mở 10 phút
    await new Promise(resolve => setTimeout(resolve, 600000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành test manual');
  }
}

openBrowserForManualTest().catch(console.error);
