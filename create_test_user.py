#!/usr/bin/env python3
"""
Script để tạo user test không có quyền
"""
import requests
import json

# API URLs
BASE_URL = "http://localhost:8000/api/user-management"

def create_test_user():
    """Tạo user test"""
    print("🔄 Tạo user test...")
    
    payload = {
        "username": "testuser",
        "password": "test123",
        "full_name": "Test User",
        "email": "<EMAIL>",
        "phone_number": "0987654321",
        "is_active": True
    }
    
    try:
        response = requests.post(f"{BASE_URL}/users", json=payload)
        print(f"📊 Response status: {response.status_code}")
        print(f"📊 Response body: {response.text}")
        
        if response.status_code == 200:
            user_data = response.json()
            print("✅ Tạo user thành công!")
            print(f"📋 User ID: {user_data['id']}")
            return user_data['id']
        else:
            print("❌ Tạo user thất bại!")
            return None
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return None

def create_limited_role():
    """Tạo role với quyền hạn chế"""
    print("🔄 Tạo role Limited User...")
    
    payload = {
        "name": "Limited User",
        "description": "User với quyền hạn chế - chỉ xem overview",
        "is_activated": True
    }
    
    try:
        response = requests.post(f"{BASE_URL}/roles", json=payload)
        print(f"📊 Response status: {response.status_code}")
        print(f"📊 Response body: {response.text}")
        
        if response.status_code == 200:
            role_data = response.json()
            print("✅ Tạo role thành công!")
            print(f"📋 Role ID: {role_data['id']}")
            return role_data['id']
        else:
            print("❌ Tạo role thất bại!")
            return None
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return None

def assign_limited_permissions(role_id):
    """Gán quyền hạn chế cho role (chỉ overview)"""
    print(f"🔄 Gán quyền hạn chế cho role {role_id}...")
    
    # Chỉ gán permission xem module và menu overview
    permission_ids = [6, 18]  # user_management_view, user_management_menu_overview
    
    payload = {
        "role_id": role_id,
        "permission_ids": permission_ids
    }
    
    try:
        response = requests.post(f"{BASE_URL}/role-permissions", json=payload)
        print(f"📊 Response status: {response.status_code}")
        print(f"📊 Response body: {response.text}")
        
        if response.status_code == 200:
            print("✅ Gán quyền thành công!")
        else:
            print("❌ Gán quyền thất bại!")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")

def assign_role_to_user(user_id, role_id):
    """Gán role cho user"""
    print(f"🔄 Gán role {role_id} cho user {user_id}...")
    
    payload = {
        "user_id": user_id,
        "role_id": role_id
    }
    
    try:
        response = requests.post(f"{BASE_URL}/user-roles", json=payload)
        print(f"📊 Response status: {response.status_code}")
        print(f"📊 Response body: {response.text}")
        
        if response.status_code == 200:
            print("✅ Gán role thành công!")
        else:
            print("❌ Gán role thất bại!")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")

def check_user_permissions(user_id):
    """Kiểm tra permissions của user"""
    print(f"🔍 Kiểm tra permissions của user {user_id}...")
    
    try:
        response = requests.get(f"{BASE_URL}/users/{user_id}/permissions")
        if response.status_code == 200:
            permissions = response.json()
            print(f"✅ User có {len(permissions)} permissions:")
            for perm in permissions:
                print(f"  - {perm['code']} ({perm['type']}): {perm['name']}")
        else:
            print("❌ Không thể lấy permissions!")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    print("🚀 Tạo user test với quyền hạn chế...")
    
    # 1. Tạo user test
    user_id = create_test_user()
    print()
    
    if user_id:
        # 2. Tạo role hạn chế
        role_id = create_limited_role()
        print()
        
        if role_id:
            # 3. Gán quyền hạn chế cho role
            assign_limited_permissions(role_id)
            print()
            
            # 4. Gán role cho user
            assign_role_to_user(user_id, role_id)
            print()
            
            # 5. Kiểm tra kết quả
            check_user_permissions(user_id)
            
            print()
            print("📝 THÔNG TIN ĐĂNG NHẬP:")
            print("   Username: testuser")
            print("   Password: test123")
            print("   Expected: Chỉ thấy menu Overview, không thấy Users/Roles/etc")
    
    print("🏁 Hoàn thành!")
