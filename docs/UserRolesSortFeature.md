# User Roles Sort Feature

## Tổng quan
Tính năng sort cho bảng User Roles cho phép người dùng sắp xếp dữ liệu theo các cột khác nhau để dễ dàng tìm kiếm và quản lý thông tin.

## Tính năng

### 1. Sort theo cột
- **User ID**: Sort theo ID của user (UUID string)
- **Username**: Sort theo tên đăng nhập (alphabetical)
- **Họ và tên**: Sort theo tên đầy đủ (alphabetical)
- **Role ID**: Sort theo ID của role (numeric)
- **Tên Role**: Sort theo tên role (alphabetical)

### 2. Hướng sort
- **Ascend**: Sắp xếp tăng dần (A-Z, 0-9)
- **Descend**: Sắp xếp giảm dần (Z-A, 9-0)

### 3. Trạng thái sort
- Hiển thị icon mũi tên để chỉ ra hướng sort hiện tại
- Mặc định sort theo Username tăng dần

## C<PERSON>ch sử dụng

1. **Click vào header cột** để sort theo cột đó
2. **Click lần nữa** để đổi hướng sort (ascend ↔ descend)
3. **Click lần thứ ba** để bỏ sort

## Implementation

### Frontend Changes

#### State Management
```typescript
// Sort user roles state
const [userRoleSortedInfo, setUserRoleSortedInfo] = useState<any>({
  columnKey: 'username',
  order: 'ascend'
});
```

#### Sort Handler
```typescript
// Handle user role table sort
const handleUserRoleTableChange = (_pagination: any, _filters: any, sorter: any) => {
  setUserRoleSortedInfo(sorter);
};
```

#### Sort Logic
```typescript
// Get sorted user roles
const getSortedUserRoles = () => {
  if (!userRoleSortedInfo.columnKey || !userRoleSortedInfo.order) {
    return filteredUserRoles;
  }

  return [...filteredUserRoles].sort((a, b) => {
    let aValue: any;
    let bValue: any;

    switch (userRoleSortedInfo.columnKey) {
      case 'user_id':
        aValue = a.user_id;
        bValue = b.user_id;
        break;
      case 'username':
        aValue = a.username.toLowerCase();
        bValue = b.username.toLowerCase();
        break;
      case 'full_name':
        aValue = a.full_name.toLowerCase();
        bValue = b.full_name.toLowerCase();
        break;
      case 'role_id':
        aValue = a.role_id;
        bValue = b.role_id;
        break;
      case 'role_name':
        aValue = a.role_name.toLowerCase();
        bValue = b.role_name.toLowerCase();
        break;
      default:
        return 0;
    }

    if (userRoleSortedInfo.order === 'ascend') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });
};
```

#### Table Configuration
```typescript
<Table
  dataSource={getSortedUserRoles()}
  onChange={handleUserRoleTableChange}
  sortDirections={['ascend', 'descend']}
>
  <Table.Column 
    title="User ID" 
    dataIndex="user_id" 
    key="user_id" 
    sorter={true}
    sortDirections={['ascend', 'descend']}
  />
  // ... other columns
</Table>
```

## Lợi ích

1. **Dễ dàng tìm kiếm**: Người dùng có thể sort theo username hoặc tên để tìm user cụ thể
2. **Quản lý hiệu quả**: Sort theo role để xem user nào có role nào
3. **UX tốt hơn**: Giao diện trực quan với icon sort
4. **Hiệu suất**: Sort được thực hiện ở client-side, không cần gọi API

## Tương thích

- Tương thích với tính năng search hiện có
- Tương thích với tính năng refresh
- Tương thích với tính năng add/delete user role

## Testing

### Test Cases
1. **Sort Username**: Click header Username → verify sort A-Z
2. **Sort Full Name**: Click header Họ và tên → verify sort A-Z
3. **Sort Role ID**: Click header Role ID → verify sort numeric
4. **Sort Role Name**: Click header Tên Role → verify sort A-Z
5. **Change Direction**: Click header lần nữa → verify sort Z-A
6. **Clear Sort**: Click header lần thứ ba → verify no sort
7. **Search + Sort**: Kết hợp search và sort → verify hoạt động đúng

### Expected Behavior
- Sort hoạt động với dữ liệu thực từ API
- Sort hoạt động với dữ liệu mock khi API fail
- Icon sort hiển thị đúng hướng
- Performance không bị ảnh hưởng với dataset lớn 