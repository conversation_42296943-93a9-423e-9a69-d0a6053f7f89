# User Roles Feature - User Management Module

## Tổng quan

Tính năng User Roles cho phép quản lý mối quan hệ giữa Users và Roles trong hệ thống. Người dùng có thể xem danh sách các user roles, tìm kiếm và xóa user roles.

## Tính năng

### 1. Hiển thị danh sách User Roles
- Bảng hiển thị tất cả user roles từ database
- Thông tin hiển thị:
  - User ID (UUID)
  - Username
  - Họ và tên
  - Role ID
  - Tên Role

### 2. Tìm kiếm User Roles
- Tìm kiếm theo username, họ tên hoặc tên role
- Tìm kiếm real-time khi nhập text
- C<PERSON> thể xóa text tìm kiếm để hiển thị lại tất cả

### 3. Xóa User Role
- Icon delete ở cột cuối cùng
- Modal xác nhận trước khi xóa
- <PERSON><PERSON><PERSON> thị thông tin chi tiết user role sẽ bị xóa
- <PERSON><PERSON><PERSON> bá<PERSON> về hậu quả của việc xóa

### 4. Refresh dữ liệu
- Nút refresh để tải lại dữ liệu từ server
- Loading state khi đang tải dữ liệu

## Cấu trúc Database

### Bảng user_roles
```sql
CREATE TABLE user_roles (
    user_id UUID REFERENCES users(id),
    role_id INTEGER REFERENCES roles(id),
    PRIMARY KEY (user_id, role_id)
);
```

## API Endpoints

### GET /api/user-management/user-roles
Lấy danh sách tất cả user roles với thông tin user và role

**Response:**
```json
[
  {
    "user_id": "6e5d5fb4-98d0-41f9-9b53-69d2a44ca54d",
    "role_id": 1,
    "username": "admin",
    "full_name": "Administrator",
    "role_name": "SuperAdmin"
  }
]
```

### DELETE /api/user-management/user-roles/{user_id}/{role_id}
Xóa user role theo user_id và role_id

**Response:**
```json
{
  "message": "UserRole đã được xóa thành công"
}
```

## Cách sử dụng

1. Mở User Management Module
2. Click vào menu "User Roles" trong sidebar
3. Xem danh sách user roles trong bảng
4. Sử dụng ô tìm kiếm để lọc dữ liệu
5. Click icon delete để xóa user role (có xác nhận)

## Lưu ý

- Dữ liệu user roles chỉ hiển thị, không cho phép chỉnh sửa
- Chỉ có thể xóa user role, không thể thêm mới từ giao diện này
- Việc xóa user role sẽ làm user mất quyền hạn của role đó
- Hành động xóa không thể hoàn tác

## Technical Details

### Frontend Components
- `UserManagementPage.tsx`: Component chính
- Interface `UserRole`: Định nghĩa kiểu dữ liệu
- `renderUserRolesContent()`: Render bảng user roles
- `fetchUserRoles()`: API call để lấy dữ liệu
- `handleDeleteUserRoleClick()`: Xử lý xóa user role

### Backend Components
- `router.py`: API endpoints
- `models.py`: Database models
- `UserRoleResponse`: Pydantic model cho response

### State Management
- `userRoles`: Danh sách user roles
- `userRolesLoading`: Loading state
- `filteredUserRoles`: Dữ liệu đã lọc
- `deleteUserRoleModalVisible`: Hiển thị modal xóa
- `userRoleToDelete`: User role sẽ bị xóa 