# Metis Platform - Tài liệu kỹ thuật

## Tổng quan

Metis Platform là một hệ thống báo cáo và phân tích dữ liệu thông minh được xây dựng theo kiến trúc module hóa, l<PERSON>y cảm hứng từ Odoo. Hệ thống cho phép người dùng cài đặt và quản lý các ứng dụng (module) một cách linh hoạt thông qua giao diện AppsPage.

## Kiến trúc tổng thể

### Stack công nghệ
- **Frontend**: React + TypeScript + Ant Design
- **Backend**: Python (FastAPI)
- **Database**: PostgreSQL
- **Kiến trúc**: Module-based architecture (tương tự Odoo)

### Cấu trúc dự án
```
metis-ai/
├── backend/                 # Backend FastAPI
│   ├── src/
│   │   ├── core/           # Core components
│   │   ├── modules/        # Các module ứng dụng
│   │   └── main.py         # Entry point
│   └── update_schema.py    # Script cập nhật database
├── frontend/               # Frontend React
│   └── src/
│       └── pages/apps/     # AppsPage component
└── docs/                   # Tài liệu kỹ thuật
```

## Backend Architecture

### 1. Core Components

#### Database Configuration (`core/database.py`)
- Sử dụng SQLAlchemy ORM
- Kết nối PostgreSQL với connection string được cấu hình
- Tạo Base class cho tất cả models

#### Module Configuration (`core/module_config.py`)
- Quản lý cấu hình module (auto-install, hidden, required)
- Định nghĩa module Core và module Development
- Cung cấp các helper functions để kiểm tra trạng thái module

#### Module Router (`core/module_router.py`)
- API endpoints để quản lý module
- Chức năng cài đặt/gỡ cài đặt module
- Quản lý dependencies giữa các module

### 2. Module System

#### Module Structure
Mỗi module có cấu trúc:
```
module_name/
├── __init__.py
├── __manifest__.py    # Metadata của module
└── models.py          # Database models
```

#### Base Module
- Module lõi chứa model `IrModule` để quản lý thông tin module
- Tự động cài đặt khi khởi động server
- Không hiển thị trên AppsPage

#### User Management Module
- Module quản lý người dùng với các models: User, Role, UserRole
- Phụ thuộc vào Base module
- Tự động cài đặt khi khởi động server

### 3. Module Lifecycle

#### Khởi động Server
1. Quét tất cả module trong thư mục `modules/`
2. Đọc `__manifest__.py` và đồng bộ thông tin vào database
3. Tự động cài đặt các module được cấu hình `auto_install: true`

#### Cài đặt Module
1. Tải động module models
2. Tạo tất cả bảng database được định nghĩa trong module
3. Cập nhật trạng thái `installed` trong `ir_module`

#### Gỡ cài đặt Module
1. Kiểm tra dependencies (không cho phép gỡ nếu có module khác phụ thuộc)
2. Xóa tất cả bảng database của module
3. Cập nhật trạng thái `uninstalled` trong `ir_module`

## Frontend Architecture

### 1. AppsPage Component

#### Chức năng chính
- Hiển thị danh sách module có thể cài đặt
- Phân loại module thành System và Development
- Tìm kiếm, lọc và sắp xếp module
- Cài đặt/gỡ cài đặt module
- Hiển thị thông tin dependencies

#### UI/UX Features
- Giao diện hiện đại với Ant Design
- Responsive design cho mobile và desktop
- Loading states và error handling
- Confirmation dialogs cho các thao tác quan trọng
- Progress indicators cho quá trình cài đặt

### 2. Module Management

#### Module Categories
- **Core Modules**: Module hệ thống (base, user_management)
- **Development Modules**: Module phát triển (có thể cài đặt/gỡ cài đặt)

#### Module States
- `installed`: Module đã được cài đặt
- `uninstalled`: Module chưa được cài đặt

#### Dependencies Management
- Hiển thị danh sách module phụ thuộc
- Kiểm tra trạng thái cài đặt của dependencies
- Ngăn chặn gỡ cài đặt module có dependencies

## Database Schema

### IrModule Table
```sql
CREATE TABLE ir_module (
    name VARCHAR(128) PRIMARY KEY,
    state VARCHAR(16) DEFAULT 'uninstalled',
    version VARCHAR(32),
    summary VARCHAR(255),
    author VARCHAR(128),
    depends VARCHAR(128)[] DEFAULT '{}',
    description TEXT,
    category VARCHAR(64)
);
```

### User Management Tables
- `roles`: Quản lý vai trò
- `users`: Quản lý người dùng
- `user_roles`: Quan hệ nhiều-nhiều giữa user và role

## API Endpoints

### Module Management
- `GET /api/internal/modules/` - Lấy danh sách module (ẩn module hidden)
- `GET /api/internal/modules/all` - Lấy tất cả module
- `POST /api/internal/modules/{module_name}/install` - Cài đặt module
- `POST /api/internal/modules/{module_name}/uninstall` - Gỡ cài đặt module
- `GET /api/internal/modules/{module_name}/dependencies` - Lấy thông tin dependencies

### Configuration
- `GET /api/internal/modules/config/all` - Lấy cấu hình tất cả module
- `GET /api/internal/modules/config/{module_name}` - Lấy cấu hình module cụ thể
- `GET /api/internal/modules/hidden` - Lấy danh sách module ẩn
- `GET /api/internal/modules/auto-install` - Lấy danh sách module auto-install

## Tính năng nổi bật

### 1. Module Independence
- Mỗi module hoàn toàn độc lập
- Không có code chồng chéo giữa các module
- Module có thể gọi lẫn nhau thông qua dependencies

### 2. Dynamic Module Loading
- AppsPage load module động từ backend
- Không có module mẫu demo
- Tất cả module đều được quản lý thống nhất

### 3. Complete Installation/Uninstallation
- Cài đặt: Tạo tất cả bảng database
- Gỡ cài đặt: Xóa tất cả bảng và dữ liệu
- Quản lý dependencies tự động

### 4. Modern UI/UX
- Giao diện full màn hình hiện đại
- Responsive design
- Loading states và error handling
- Intuitive user experience

## Cấu hình và Deployment

### Database Configuration
- Database: `metisdb`
- Host: `localhost`
- Port: `5432`
- Connection string được cấu hình thông qua environment variables

### Environment Variables
- `DATABASE_URL`: Connection string cho database (optional)

### Development Setup
1. Backend: `cd backend && python -m uvicorn src.main:app --reload`
2. Frontend: `cd frontend && npm run dev`

## Roadmap và Phát triển

### Tính năng đang phát triển
- Thêm các module business (CRM, Inventory, Finance, etc.)
- Hệ thống phân quyền chi tiết
- API documentation với Swagger
- Unit tests và integration tests

### Cải tiến kỹ thuật
- Caching layer cho performance
- Logging system
- Monitoring và alerting
- Docker containerization
- CI/CD pipeline

## Kết luận

Metis Platform là một giải pháp module-based architecture mạnh mẽ, cho phép phát triển và triển khai ứng dụng một cách linh hoạt. Hệ thống được thiết kế để dễ dàng mở rộng và bảo trì, với giao diện người dùng hiện đại và trải nghiệm tốt. 