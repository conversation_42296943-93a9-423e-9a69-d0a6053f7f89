# User Roles Add Feature - User Management Module

## Tổng quan

Tính năng thêm mới User Role cho phép người dùng tạo mối quan hệ giữa Users và Roles trong hệ thống thông qua giao diện thân thiện với các component dropdown tìm kiếm.

## Tính năng mới

### 1. <PERSON><PERSON><PERSON> "Thêm mới"
- Icon dấu cộng (+) trong menu User Roles
- Vị trí: Bên cạnh nút Refresh trong header của bảng
- Khi click sẽ mở popup form thêm mới

### 2. Popup Form Thêm mới
- **User Name**: Component SearchUserDropdown
  - <PERSON>ad danh sách Users từ database
  - Cho phép gõ để tìm kiếm theo username, họ tên, email
  - Hiển thị thông tin user: username, họ tên, email, trạng thái
  - Icon user với màu xanh (active) hoặc xám (inactive)

- **Role**: Component SearchRoleDropdown
  - <PERSON><PERSON> <PERSON><PERSON> sách Roles từ database
  - Cho phép gõ để tìm kiếm theo tên role, mô tả
  - Hiển thị thông tin role: tên role, mô tả, trạng thái kích hoạt
  - Icon shield với màu xanh (activated) hoặc xám (not activated)
  - Tag hiển thị trạng thái "Kích hoạt" hoặc "Không kích hoạt"

### 3. Các nút trong Form
- **Hủy bỏ**: Hủy thông tin đã nhập và đóng form
- **Lưu & Thêm mới**: Lưu thông tin xuống database và reset lại form
- **Lưu & Đóng**: Lưu thông tin xuống database và đóng form

## API Endpoints

### POST /api/user-management/user-roles
Tạo user role mới với validation đầy đủ.

## Components

### SearchUserDropdown
- Tìm kiếm real-time theo username, họ tên, email
- Hiển thị loading state
- Icon user với màu theo trạng thái

### SearchRoleDropdown
- Tìm kiếm real-time theo tên role, mô tả
- Hiển thị loading state
- Icon shield với màu theo trạng thái
- Tag hiển thị trạng thái kích hoạt

## Cách sử dụng

1. Mở User Management Module
2. Click vào menu "User Roles"
3. Click nút "Thêm mới" (icon dấu cộng)
4. Chọn User và Role từ dropdown
5. Click nút phù hợp để lưu hoặc hủy 