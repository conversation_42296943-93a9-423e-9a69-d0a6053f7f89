# User Roles Validation Feature

## Tổng quan
Tính năng cảnh báo và validation cho User Roles trong UserManagementPage, giúp người dùng tránh tạo duplicate user roles.

## Các tính năng đã thêm

### 1. Kiểm tra User Role đã tồn tại
- **Hàm `checkUserRoleExists`**: Kiểm tra xem user role đã tồn tại trong database chưa
- **Hàm `getUserRoleInfo`**: Lấy thông tin user và role để hiển thị trong cảnh báo

### 2. <PERSON><PERSON><PERSON> báo khi nhấn "Lưu & Đóng" hoặc "Lưu & Thêm mới"
- Kiểm tra trước khi gửi API request
- Hiển thị message warning với thông tin chi tiết:
  ```
  User "username - full_name" đã có vai trò "role_name". <PERSON>ui lòng chọn User hoặc Role khác.
  ```

### 3. <PERSON><PERSON><PERSON> báo real-time
- **useEffect**: <PERSON> dõi thay đổi của `selectedUserId` và `selectedRoleId`
- Hi<PERSON><PERSON> thị cảnh báo ngay khi user chọn User và Role đã tồn tại
- Message hiển thị trong 5 giây với icon ⚠️

### 4. Hiển thị thông tin User Roles hiện tại
- Component hiển thị trong modal "Thêm User Role mới"
- Hiển thị tất cả vai trò hiện tại của user được chọn
- Sử dụng Tag components để hiển thị tên vai trò
- Thông báo nếu user chưa có vai trò nào

## Cấu trúc code

### Hàm kiểm tra
```typescript
const checkUserRoleExists = (userId: string, roleId: number): boolean => {
  return userRoles.some(userRole => 
    userRole.user_id === userId && userRole.role_id === roleId
  );
};

const getUserRoleInfo = (userId: string, roleId: number) => {
  const user = users.find(u => u.id === userId);
  const role = roles.find(r => r.id === roleId);
  return {
    username: user?.username || 'Unknown',
    fullName: user?.full_name || 'Unknown',
    roleName: role?.name || 'Unknown'
  };
};
```

### Validation trong handleAddUserRole
```typescript
// Kiểm tra user role đã tồn tại
if (checkUserRoleExists(selectedUserId, selectedRoleId)) {
  const { username, fullName, roleName } = getUserRoleInfo(selectedUserId, selectedRoleId);
  message.warning(
    `User "${username} - ${fullName}" đã có vai trò "${roleName}". Vui lòng chọn User hoặc Role khác.`
  );
  return;
}
```

### useEffect cho real-time warning
```typescript
useEffect(() => {
  if (selectedUserId && selectedRoleId) {
    if (checkUserRoleExists(selectedUserId, selectedRoleId)) {
      const { username, fullName, roleName } = getUserRoleInfo(selectedUserId, selectedRoleId);
      message.warning(
        `⚠️ User "${username} - ${fullName}" đã có vai trò "${roleName}". Vui lòng chọn User hoặc Role khác.`,
        5
      );
    }
  }
}, [selectedUserId, selectedRoleId, userRoles, users, roles]);
```

## UI Components

### Modal Add UserRole với thông tin hiện tại
- Hiển thị section "📋 Vai trò hiện tại của User:"
- Background màu xám nhạt (#f6f8fa)
- Border và padding phù hợp
- Hiển thị danh sách vai trò bằng Tag components
- Thông báo nếu user chưa có vai trò

## Lợi ích
1. **Tránh duplicate data**: Ngăn chặn tạo user role trùng lặp
2. **UX tốt hơn**: Cảnh báo rõ ràng và thông tin chi tiết
3. **Real-time feedback**: Người dùng biết ngay khi chọn sai
4. **Thông tin đầy đủ**: Hiển thị vai trò hiện tại của user
5. **Giảm lỗi API**: Kiểm tra trước khi gửi request

## Cách sử dụng
1. Mở UserManagementPage
2. Chọn menu "User Roles"
3. Nhấn "Thêm mới"
4. Chọn User và Role
5. Hệ thống sẽ hiển thị cảnh báo nếu đã tồn tại
6. Xem thông tin vai trò hiện tại của user được chọn 