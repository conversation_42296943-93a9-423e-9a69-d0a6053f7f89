# Site Management Module - T<PERSON><PERSON> liệu kỹ thuật và Hướng dẫn sử dụng

## Tổng quan

Site Management Module là một Core Module của Metis Platform, cung cấp các tính năng quản lý thông tin và khai báo Site. Site là định danh của một tư cách pháp nhân của tổ chức, hỗ trợ cấu trúc phân cấp cha-con.

## Thông tin Module

### Cấu hình cơ bản
- **Tên module**: `site_management`
- **Loại module**: Core Module (hiển thị trong tab Module hệ thống)
- **Tự động cài đặt**: <PERSON><PERSON> (`auto_install: true`)
- **Ẩn khỏi AppsPage**: Không (`hidden: false`)
- **Bắt buộc**: C<PERSON> (`required: true`)
- **Phiên bản**: 1.0
- **T<PERSON><PERSON> gi<PERSON>**: Metis AI
- **<PERSON><PERSON> mục**: Core

### Manifest
```python
{
    'name': 'site_management',
    'summary': 'Site Management Module',
    'version': '1.0',
    'author': 'Metis AI',
    'category': 'Core',
    'description': 'Module quản lý thông tin và khai báo Site.',
    'is_core_module': True,
    'auto_install': True,
    'hidden': False,
    'required': True
}
```

## Kiến trúc kỹ thuật

### Backend Architecture

#### 1. Database Models (`models.py`)
```python
class Site(Base):
    __tablename__ = 'sites'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(String(10), unique=True, nullable=False, index=True)
    name = Column(String(250), nullable=False)
    descriptions = Column(String(1000), nullable=True)
    is_activated = Column(Boolean, default=True)
    parent_id = Column(Integer, ForeignKey('sites.id'), nullable=True)
    
    # Relationships
    children = relationship('Site', backref=backref('parent', remote_side=[id]))
```

#### 2. API Endpoints (`router.py`)
- `GET /api/site-management/sites` - Lấy danh sách tất cả Sites
- `GET /api/site-management/sites/{site_id}` - Lấy thông tin Site theo ID
- `POST /api/site-management/sites` - Tạo Site mới
- `PUT /api/site-management/sites/{site_id}` - Cập nhật thông tin Site
- `DELETE /api/site-management/sites/{site_id}` - Xóa Site
- `GET /api/site-management/sites/check-code/{code}` - Kiểm tra mã Site trùng lặp

#### 3. Business Logic
- **Validation**: Kiểm tra mã Site duy nhất, format hợp lệ
- **Hierarchy**: Quản lý quan hệ cha-con, ngăn chặn xóa Site có con
- **State Management**: Quản lý trạng thái kích hoạt/tạm dừng

### Frontend Architecture

#### 1. Component Structure
```
SiteManagementPage/
├── SiteManagementPage.tsx      # Component chính
├── SiteManagementPage.css      # Styles responsive
└── SiteParentDropdown.tsx      # Component dropdown Site cha
```

#### 2. State Management
- **Module Detail**: Thông tin module từ API
- **Sites Data**: Danh sách sites với cấu trúc cây
- **Form States**: Quản lý form thêm mới/chỉnh sửa
- **UI States**: Loading, error, modal visibility

#### 3. Responsive Design
- **Desktop**: Layout đầy đủ với sidebar và content
- **Mobile**: Layout tối ưu với collapsible sidebar
- **Tablet**: Layout trung gian với responsive grid

## Cấu trúc Database

### Bảng Sites
| Trường | Kiểu dữ liệu | Mô tả | Ràng buộc |
|--------|-------------|-------|-----------|
| id | Integer | Mã số tăng tự động, khóa chính | Primary Key, Auto Increment |
| code | String(10) | Mã viết tắt của Site | Unique, Not Null, Index |
| name | String(250) | Tên của Site | Not Null |
| descriptions | String(1000) | Mô tả site | Nullable |
| is_activated | Boolean | Trạng thái kích hoạt | Default: True |
| parent_id | Integer | Mã ID Site cha | Foreign Key (sites.id), Nullable |

### Quan hệ và Constraints
- **Self-referencing**: Sites có thể có quan hệ cha-con với chính nó
- **Parent-Child**: Một Site có thể có nhiều Site con
- **Cascade Protection**: Không thể xóa Site có con
- **Unique Code**: Mã Site phải duy nhất trong toàn bộ hệ thống

## Tính năng chính

### 1. Tổng quan Module (Overview)
- **Module Information**: Hiển thị thông tin chi tiết module
- **System Statistics**: Thống kê bảng dữ liệu, API endpoints, users, permissions
- **Recent Activities**: Lịch sử hoạt động gần đây
- **Module Tags**: Core Module, Tự động cài đặt, etc.

### 2. Danh sách Sites (List)
- **Tree Structure**: Hiển thị sites theo cấu trúc cây cha-con
- **Search & Filter**: Tìm kiếm theo mã, tên, mô tả
- **Sorting**: Sắp xếp theo mã, tên
- **Bulk Actions**: Chỉnh sửa, xóa, toggle trạng thái
- **Responsive Table**: Tối ưu cho mobile và desktop

### 3. Thêm mới Site (Add)
- **Form Validation**: Real-time validation cho tất cả fields
- **Code Check**: Kiểm tra trùng lặp mã Site
- **Parent Selection**: Dropdown chọn Site cha với search
- **Mobile Optimized**: Layout responsive cho mobile
- **Save Options**: Lưu & Đóng, Lưu & Tạo mới

### 4. Chỉnh sửa Site (Edit)
- **Modal Form**: Chỉnh sửa trong modal overlay
- **Field Restrictions**: Mã Site không thể chỉnh sửa
- **Parent Lock**: Site cha không thể thay đổi
- **Validation**: Kiểm tra dữ liệu trước khi lưu

## Hướng dẫn sử dụng

### 1. Truy cập Module
```
URL: http://localhost:5173/site-management
Navigation: AppsPage → Module hệ thống → Site Management → View
```

### 2. Xem tổng quan
1. Mở SiteManagementPage
2. Tab "Tổng quan" hiển thị mặc định
3. Xem thông tin module, thống kê hệ thống, hoạt động gần đây

### 3. Quản lý danh sách Sites
1. Chuyển sang tab "Danh sách"
2. Sử dụng thanh tìm kiếm để lọc sites
3. Click vào nút expand (▶) để xem sites con
4. Sử dụng các nút thao tác:
   - **Chỉnh sửa** (✏️): Mở modal chỉnh sửa
   - **Xóa** (🗑️): Xóa site (nếu không có con)
   - **Toggle** (⚡): Bật/tắt trạng thái

### 4. Thêm Site mới
1. Chuyển sang tab "Thêm mới"
2. Điền thông tin:
   - **Mã Site**: Bắt buộc, tối đa 10 ký tự, chỉ chữ hoa + số + _
   - **Tên Site**: Bắt buộc, tối đa 250 ký tự
   - **Mô tả**: Không bắt buộc, tối đa 1000 ký tự
   - **Site cha**: Chọn từ dropdown (không bắt buộc)
   - **Trạng thái**: Mặc định kích hoạt
3. Chọn hành động:
   - **Lưu & Đóng**: Lưu và quay về danh sách
   - **Lưu & Tạo mới**: Lưu và reset form

### 5. Chỉnh sửa Site
1. Từ danh sách, click nút "Chỉnh sửa"
2. Modal hiển thị với thông tin hiện tại
3. Chỉnh sửa các field được phép:
   - **Tên Site**: Có thể thay đổi
   - **Mô tả**: Có thể thay đổi
   - **Trạng thái**: Có thể toggle
   - **Mã Site**: Không thể thay đổi
   - **Site cha**: Không thể thay đổi
4. Click "Lưu" để cập nhật

### 6. Xóa Site
1. Từ danh sách, click nút "Xóa"
2. Hệ thống kiểm tra:
   - **Có con**: Hiển thị cảnh báo, không cho phép xóa
   - **Không có con**: Hiển thị xác nhận xóa
3. Xác nhận để xóa vĩnh viễn

## Validation Rules

### Mã Site (Code)
- **Bắt buộc**: Phải nhập
- **Format**: Chỉ chữ hoa, số, dấu gạch dưới
- **Độ dài**: Tối đa 10 ký tự
- **Unique**: Không được trùng với site khác
- **Real-time**: Kiểm tra ngay khi nhập

### Tên Site (Name)
- **Bắt buộc**: Phải nhập
- **Độ dài**: Tối đa 250 ký tự
- **Format**: Cho phép ký tự Unicode

### Mô tả (Descriptions)
- **Không bắt buộc**: Có thể để trống
- **Độ dài**: Tối đa 1000 ký tự
- **Format**: Cho phép xuống dòng

### Site cha (Parent)
- **Không bắt buộc**: Có thể để trống
- **Validation**: Không được chọn chính nó
- **Hierarchy**: Hỗ trợ nhiều cấp

## API Documentation

### Endpoints

#### GET /api/site-management/sites
Lấy danh sách tất cả sites với cấu trúc cây.

**Response:**
```json
[
  {
    "id": 1,
    "code": "HQ",
    "name": "Trụ sở chính",
    "descriptions": "Trụ sở chính của công ty",
    "is_activated": true,
    "parent_id": null,
    "children": [
      {
        "id": 2,
        "code": "BR1",
        "name": "Chi nhánh 1",
        "descriptions": "Chi nhánh tại Hà Nội",
        "is_activated": true,
        "parent_id": 1,
        "children": []
      }
    ]
  }
]
```

#### POST /api/site-management/sites
Tạo site mới.

**Request:**
```json
{
  "code": "BR2",
  "name": "Chi nhánh 2",
  "descriptions": "Chi nhánh tại TP.HCM",
  "is_activated": true,
  "parent_id": 1
}
```

#### PUT /api/site-management/sites/{id}
Cập nhật site.

**Request:**
```json
{
  "name": "Chi nhánh 2 - TP.HCM",
  "descriptions": "Chi nhánh chính tại TP.HCM",
  "is_activated": true
}
```

#### DELETE /api/site-management/sites/{id}
Xóa site (chỉ khi không có con).

#### GET /api/site-management/sites/check-code/{code}
Kiểm tra mã site có sẵn không.

**Response:**
```json
{
  "available": true
}
```

### Error Handling
- **400 Bad Request**: Dữ liệu không hợp lệ
- **404 Not Found**: Site không tồn tại
- **409 Conflict**: Mã site trùng lặp
- **422 Unprocessable Entity**: Validation errors

## Responsive Design

### Desktop (>= 1200px)
- **Layout**: Full sidebar + content area
- **Table**: Hiển thị đầy đủ columns
- **Form**: 2-column layout
- **Buttons**: Horizontal arrangement

### Tablet (768px - 1199px)
- **Layout**: Collapsible sidebar
- **Table**: Responsive columns
- **Form**: 1-column layout
- **Buttons**: Responsive arrangement

### Mobile (< 768px)
- **Layout**: Hidden sidebar, hamburger menu
- **Table**: Scrollable, compact columns
- **Form**: Full-width inputs
- **Buttons**: Vertical stack, full-width

## Performance Optimizations

### Backend
- **Database Indexing**: Index trên code và parent_id
- **Query Optimization**: Efficient tree queries
- **Caching**: Module information caching
- **Validation**: Server-side validation

### Frontend
- **Lazy Loading**: Components load on demand
- **State Management**: Efficient re-renders
- **API Caching**: Cache module details
- **Debounced Search**: Optimized search input

## Security Considerations

### Data Validation
- **Input Sanitization**: Clean user inputs
- **SQL Injection**: Parameterized queries
- **XSS Prevention**: Output encoding
- **CSRF Protection**: Token-based requests

### Access Control
- **Authentication**: User session validation
- **Authorization**: Role-based access
- **Audit Trail**: Log all changes
- **Data Integrity**: Constraint enforcement

## Troubleshooting

### Common Issues

#### 1. Không thể xóa Site
**Nguyên nhân**: Site có con
**Giải pháp**: Xóa tất cả sites con trước

#### 2. Mã Site bị trùng
**Nguyên nhân**: Mã đã tồn tại
**Giải pháp**: Chọn mã khác

#### 3. Form không lưu được
**Nguyên nhân**: Validation errors
**Giải pháp**: Kiểm tra và sửa lỗi validation

#### 4. Không hiển thị sites con
**Nguyên nhân**: Chưa expand node
**Giải pháp**: Click vào nút expand (▶)

### Debug Information
- **Browser Console**: Check for JavaScript errors
- **Network Tab**: Monitor API requests
- **Backend Logs**: Check server errors
- **Database**: Verify data integrity

## Migration và Deployment

### Database Migration
```bash
# Tạo bảng
cd backend
python migrate_site_management.py create

# Xóa bảng
python migrate_site_management.py drop
```

### Environment Setup
```bash
# Backend
cd backend
pip install -r requirements.txt
python -m uvicorn src.main:app --reload

# Frontend
cd frontend
npm install
npm run dev
```

### Production Deployment
- **Database**: PostgreSQL với connection pooling
- **Backend**: Gunicorn + FastAPI
- **Frontend**: Nginx static serving
- **SSL**: HTTPS configuration
- **Monitoring**: Health checks và logging

## Roadmap

### Tính năng sắp tới
- **Bulk Operations**: Import/export sites
- **Advanced Search**: Full-text search
- **Audit Trail**: Change history
- **API Rate Limiting**: Request throttling
- **Caching Layer**: Redis integration

### Performance Improvements
- **Pagination**: Large dataset handling
- **Virtual Scrolling**: Smooth scrolling
- **Optimistic Updates**: UI responsiveness
- **Background Sync**: Offline support

---

*Tài liệu này được cập nhật lần cuối: [Ngày hiện tại]*
*Phiên bản: 1.0*
*Tác giả: Metis AI Team* 