// Test permission filtering on AppsPage
const puppeteer = require('puppeteer');

async function testPermissionFiltering() {
  console.log('🚀 Test permission filtering on AppsPage...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('AppsPage') || text.includes('Permission') || text.includes('DEBUG')) {
      console.log(`🔍 Frontend: ${text}`);
    }
  });
  
  try {
    // Đăng nhập với phucnguyen
    console.log('🔐 Đăng nhập với phucnguyen...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    
    // Clear any existing data
    await page.evaluate(() => {
      localStorage.clear();
    });
    
    await page.type('input[placeholder="Tên đăng nhập"]', 'phucnguyen');
    await page.type('input[placeholder="Mật khẩu"]', 'phuc123');
    await page.click('button[type="submit"]');
    
    // Đợi một chút để login process hoàn thành
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Navigate đến apps page
    console.log('🔄 Navigate đến apps page...');
    await page.goto('http://localhost:5173/apps', { waitUntil: 'networkidle2' });
    
    // Đợi lâu hơn để permissions và modules load
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    // Kiểm tra user info và permissions
    const userInfo = await page.evaluate(() => {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      return {
        userId: user.id,
        username: user.username,
        hasUser: !!user.id
      };
    });
    
    console.log('📊 User info:', userInfo);
    
    if (userInfo.hasUser) {
      // Test permissions API
      const permissionsResponse = await page.evaluate(async (userId) => {
        try {
          const token = localStorage.getItem('token');
          const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          };
          
          if (token) {
            headers['Authorization'] = `Bearer ${token}`;
          }
          
          const response = await fetch(`http://localhost:8000/api/user-management/users/${userId}/permissions`, {
            headers
          });
          
          if (response.ok) {
            return await response.json();
          } else {
            return { error: `HTTP ${response.status}: ${response.statusText}` };
          }
        } catch (error) {
          return { error: error.message };
        }
      }, userInfo.userId);
      
      console.log('🔍 Permissions API response:', permissionsResponse);
      
      // Đếm module cards visible
      const moduleInfo = await page.evaluate(() => {
        const moduleCards = document.querySelectorAll('.ant-card');
        const moduleTexts = Array.from(moduleCards).map(card => {
          const title = card.querySelector('.ant-typography');
          return title ? title.textContent.trim() : 'Unknown';
        });
        return {
          count: moduleCards.length,
          modules: moduleTexts
        };
      });
      
      console.log('📊 Module cards visible to phucnguyen:');
      console.log(`   Count: ${moduleInfo.count}`);
      moduleInfo.modules.forEach((module, index) => {
        console.log(`   ${index + 1}. ${module}`);
      });
      
      console.log('');
      console.log('📝 EXPECTED RESULT:');
      console.log('   - Phucnguyen has permissions: site_management_menu_overview, site_management_btn_refresh, site_management_btn_open_apps_page');
      console.log('   - Phucnguyen should see ONLY Site Management module');
      console.log('   - Phucnguyen should NOT see: User Management, Activity Logs, Base, Sample Development modules');
      console.log('');
      
      if (moduleInfo.count === 1 && moduleInfo.modules.some(m => m.toLowerCase().includes('site'))) {
        console.log('✅ PERMISSION SYSTEM WORKING CORRECTLY!');
      } else if (moduleInfo.count === 0) {
        console.log('⚠️  No modules visible - permissions might still be loading or all modules filtered out');
      } else {
        console.log('❌ PERMISSION SYSTEM NOT WORKING - unexpected modules visible');
        console.log('   Expected: Only Site Management');
        console.log('   Actual:', moduleInfo.modules);
      }
    } else {
      console.log('❌ User not logged in properly');
    }
    
    console.log('⏳ Browser sẽ mở trong 60 giây để kiểm tra...');
    await new Promise(resolve => setTimeout(resolve, 60000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành test');
  }
}

testPermissionFiltering().catch(console.error);
