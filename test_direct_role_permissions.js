// Test trực tiếp Role permissions page
const puppeteer = require('puppeteer');

async function testDirectRolePermissions() {
  console.log('🚀 Test trực tiếp Role permissions page...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('RolePermissionsPage') || text.includes('Role selected') || text.includes('checked keys') || text.includes('permissions loaded')) {
      console.log(`🔍 Frontend: ${text}`);
    }
  });
  
  try {
    // Đăng nhập
    console.log('🔐 Đăng nhập...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    await page.type('input[placeholder="Tên đăng nhập"]', 'admin');
    await page.type('input[placeholder="Mật khẩu"]', 'admin123');
    await page.click('button[type="submit"]');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Navigate trực tiếp đến Role permissions page
    console.log('🔄 Navigate trực tiếp đến Role permissions...');
    await page.goto('http://localhost:5173/user-management?tab=role-permissions', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('✅ Role permissions page loaded');
    
    // Kiểm tra tree structure
    const treeInfo = await page.evaluate(() => {
      const treeNodes = document.querySelectorAll('.ant-tree-treenode');
      const nodeInfo = Array.from(treeNodes).slice(0, 5).map(node => ({
        key: node.getAttribute('data-key'),
        title: node.querySelector('.ant-tree-title')?.textContent?.trim(),
        hasCheckbox: !!node.querySelector('.ant-tree-checkbox'),
        isChecked: node.querySelector('.ant-tree-checkbox')?.classList.contains('ant-tree-checkbox-checked')
      }));
      
      return {
        totalNodes: treeNodes.length,
        sampleNodes: nodeInfo
      };
    });
    
    console.log('📊 Tree structure:', JSON.stringify(treeInfo, null, 2));
    
    console.log('⏳ Giữ browser mở 2 phút để test manual...');
    console.log('📝 Hãy chọn role "admin" và quan sát auto-check...');
    
    await new Promise(resolve => setTimeout(resolve, 120000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành');
  }
}

testDirectRolePermissions().catch(console.error);
