// Script để kiểm tra cấu trúc trang
const puppeteer = require('puppeteer');

async function checkPageStructure() {
  console.log('🚀 Kiểm tra cấu trúc trang...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs từ browser
  page.on('console', msg => {
    const type = msg.type();
    const text = msg.text();
    if (text.includes('RolePermissionsPage') || text.includes('Error') || text.includes('DEBUG')) {
      console.log(`🔍 [BROWSER ${type.toUpperCase()}] ${text}`);
    }
  });
  
  try {
    console.log('📱 Mở trang user-management...');
    await page.goto('http://localhost:5173/user-management', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Kiểm tra toàn bộ cấu trúc DOM
    console.log('🔍 Kiểm tra cấu trúc DOM...');
    const pageStructure = await page.evaluate(() => {
      const body = document.body;
      
      // Tìm tất cả các element có text liên quan đến role, permission, tab
      const allElements = Array.from(document.querySelectorAll('*'));
      const relevantElements = allElements
        .filter(el => {
          const text = el.textContent?.toLowerCase() || '';
          return text.includes('role') || text.includes('permission') || text.includes('tab') || 
                 text.includes('user') || text.includes('management') || text.includes('lưu');
        })
        .slice(0, 20) // Chỉ lấy 20 phần tử đầu
        .map(el => ({
          tag: el.tagName,
          text: el.textContent?.trim().substring(0, 100),
          className: el.className,
          id: el.id,
          role: el.getAttribute('role')
        }));
      
      // Tìm tất cả các button
      const buttons = Array.from(document.querySelectorAll('button')).map(btn => ({
        text: btn.textContent?.trim(),
        className: btn.className,
        disabled: btn.disabled,
        type: btn.type
      }));
      
      // Tìm tất cả các tab-like elements
      const tabs = Array.from(document.querySelectorAll('[role="tab"], .ant-tabs-tab, .tab, [class*="tab"]')).map(tab => ({
        text: tab.textContent?.trim(),
        className: tab.className,
        role: tab.getAttribute('role')
      }));
      
      return {
        title: document.title,
        url: window.location.href,
        relevantElements,
        buttons,
        tabs,
        hasReactRoot: !!document.getElementById('root'),
        bodyClasses: document.body.className
      };
    });
    
    console.log('📋 Cấu trúc trang:', JSON.stringify(pageStructure, null, 2));
    
    // Chụp screenshot
    await page.screenshot({ path: 'page_structure.png', fullPage: true });
    console.log('📸 Đã chụp screenshot: page_structure.png');
    
    // Thử tìm và click vào các element có text "Role permissions"
    console.log('🔍 Tìm kiếm text "Role permissions"...');
    const rolePermElements = await page.$$eval('*', elements => {
      return elements
        .filter(el => el.textContent && el.textContent.includes('Role permissions'))
        .map(el => ({
          tag: el.tagName,
          text: el.textContent.trim(),
          className: el.className,
          clickable: el.tagName === 'BUTTON' || el.onclick !== null || el.style.cursor === 'pointer'
        }));
    });
    
    console.log('🎯 Elements chứa "Role permissions":', rolePermElements);
    
    // Thử tìm button "Lưu"
    console.log('🔍 Tìm kiếm button "Lưu"...');
    const saveButtons = await page.$$eval('button', buttons => {
      return buttons
        .filter(btn => btn.textContent && btn.textContent.includes('Lưu'))
        .map(btn => ({
          text: btn.textContent.trim(),
          className: btn.className,
          disabled: btn.disabled,
          visible: btn.offsetParent !== null
        }));
    });
    
    console.log('💾 Buttons "Lưu":', saveButtons);
    
    console.log('⏳ Giữ browser mở 20 giây để quan sát...');
    await new Promise(resolve => setTimeout(resolve, 20000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
    await page.screenshot({ path: 'structure_error.png', fullPage: true });
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành');
  }
}

checkPageStructure().catch(console.error);
