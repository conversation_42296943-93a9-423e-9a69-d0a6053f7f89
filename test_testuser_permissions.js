// Test testuser permissions specifically
const puppeteer = require('puppeteer');

async function testTestUserPermissions() {
  console.log('🚀 Test testuser permissions...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('PermissionContext') || text.includes('UserManagementPage') || text.includes('permissions')) {
      console.log(`🔍 Frontend: ${text}`);
    }
  });
  
  try {
    // Đăng nhập với testuser
    console.log('🔐 Đăng nhập với testuser...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    await page.type('input[placeholder="Tên đăng nhập"]', 'testuser');
    await page.type('input[placeholder="Mật khẩu"]', 'test123');
    await page.click('button[type="submit"]');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Navigate đến user-management
    console.log('🔄 Navigate đến user-management...');
    await page.goto('http://localhost:5173/user-management', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 8000)); // Đợi lâu hơn để permissions load
    
    // Kiểm tra permissions được load
    const permissionsInfo = await page.evaluate(() => {
      // Kiểm tra localStorage
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      
      return {
        userId: user.id,
        username: user.username,
        localStorageUser: user
      };
    });
    
    console.log('📊 User info:', permissionsInfo);
    
    // Test API call để lấy permissions
    console.log('🔍 Testing permissions API...');
    const response = await page.evaluate(async (userId) => {
      try {
        const response = await fetch(`http://localhost:8000/api/user-management/users/${userId}/permissions`);
        const permissions = await response.json();
        console.log('TestUser permissions from API:', permissions);
        return permissions;
      } catch (error) {
        console.error('Error fetching permissions:', error);
        return null;
      }
    }, permissionsInfo.userId);
    
    console.log('✅ TestUser permissions:', response);
    
    if (response && response.length > 0) {
      console.log('📋 Permission codes:');
      response.forEach(perm => {
        console.log(`  - ${perm.code} (${perm.type}): ${perm.name}`);
      });
    } else {
      console.log('❌ No permissions found for testuser');
    }
    
    // Đếm menu items
    const menuInfo = await page.evaluate(() => {
      const menuItems = document.querySelectorAll('.ant-menu-item');
      const menuTexts = Array.from(menuItems).map(item => item.textContent.trim());
      return {
        count: menuItems.length,
        items: menuTexts
      };
    });
    
    console.log('📊 Menu items visible to testuser:');
    console.log(`   Count: ${menuInfo.count}`);
    menuInfo.items.forEach((item, index) => {
      console.log(`   ${index + 1}. ${item}`);
    });
    
    console.log('');
    console.log('📝 EXPECTED RESULT:');
    console.log('   - TestUser should have 2 permissions: user_management_view + user_management_menu_overview');
    console.log('   - TestUser should see 1 menu item: "Tổng quan" (Overview)');
    console.log('   - TestUser should NOT see: Users, Roles, User-Roles, Role permissions');
    console.log('');
    
    if (menuInfo.count === 1 && menuInfo.items.includes('Tổng quan')) {
      console.log('✅ PERMISSION SYSTEM WORKING CORRECTLY!');
    } else if (menuInfo.count === 0) {
      console.log('⚠️  No menu items visible - permissions might still be loading');
    } else {
      console.log('❌ PERMISSION SYSTEM NOT WORKING - unexpected menu items');
    }
    
    console.log('⏳ Browser sẽ mở trong 2 phút để kiểm tra...');
    
    await new Promise(resolve => setTimeout(resolve, 120000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành test');
  }
}

testTestUserPermissions().catch(console.error);
