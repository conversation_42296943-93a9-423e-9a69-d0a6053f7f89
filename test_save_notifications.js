// Test thông b<PERSON><PERSON> khi lưu role permissions
const puppeteer = require('puppeteer');

async function testSaveNotifications() {
  console.log('🚀 Test thông báo khi lưu role permissions...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('RolePermissionsPage') || text.includes('Save')) {
      console.log(`🔍 Frontend: ${text}`);
    }
  });
  
  try {
    // Đăng nhập
    console.log('🔐 Đăng nhập...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    await page.type('input[placeholder="Tên đăng nhập"]', 'admin');
    await page.type('input[placeholder="Mật khẩu"]', 'admin123');
    await page.click('button[type="submit"]');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Navigate đến user-management
    console.log('🔄 Navigate đến user-management...');
    await page.goto('http://localhost:5173/user-management', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('✅ Browser đã sẵn sàng!');
    console.log('');
    console.log('📝 HƯỚNG DẪN TEST THÔNG BÁO:');
    console.log('');
    console.log('   🎯 BƯỚC 1: Click vào menu "Role permissions" ở sidebar bên trái');
    console.log('');
    console.log('   🎯 BƯỚC 2: Test thông báo lưu:');
    console.log('      1. Chọn role "admin"');
    console.log('      2. Check/uncheck một vài permissions');
    console.log('      3. Click nút "Lưu"');
    console.log('      4. Quan sát thông báo success màu xanh');
    console.log('');
    console.log('   ✅ KẾT QUẢ MONG ĐỢI:');
    console.log('      - Thông báo success: "Lưu phân quyền thành công!"');
    console.log('      - Thông báo hiển thị ở góc trên bên phải');
    console.log('      - Nút "Test API" đã bị loại bỏ');
    console.log('');
    console.log('   🔍 THEO DÕI CONSOLE LOGS:');
    console.log('      - Mở Developer Tools (F12) → Console tab');
    console.log('      - Quan sát logs "RolePermissionsPage: Save successful"');
    console.log('');
    console.log('⏳ Browser sẽ mở trong 10 phút để bạn test...');
    console.log('');
    
    // Giữ browser mở 10 phút
    await new Promise(resolve => setTimeout(resolve, 600000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành test thông báo');
  }
}

testSaveNotifications().catch(console.error);
