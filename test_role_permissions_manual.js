// Test script để mở browser và để user test manual
const puppeteer = require('puppeteer');

async function openRolePermissionsForManualTest() {
  console.log('🚀 Mở Role permissions để test manual...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('RolePermissionsPage') || text.includes('Error') || text.includes('Save')) {
      console.log(`🔍 Frontend: ${text}`);
    }
  });
  
  try {
    // Đăng nhập
    console.log('🔐 Đăng nhập...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    await page.type('input[placeholder="Tên đăng nhập"]', 'admin');
    await page.type('input[placeholder="Mật khẩu"]', 'admin123');
    await page.click('button[type="submit"]');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Navigate đến user-management
    console.log('🔄 Navigate đến user-management...');
    await page.goto('http://localhost:5173/user-management', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Click vào menu Role permissions
    console.log('📋 Click vào menu Role permissions...');
    await page.evaluate(() => {
      const menuItems = Array.from(document.querySelectorAll('.ant-menu-item'));
      const rolePermissionsItem = menuItems.find(item => 
        item.textContent && item.textContent.includes('Role permissions')
      );
      
      if (rolePermissionsItem) {
        console.log('Found Role permissions menu item, clicking...');
        rolePermissionsItem.click();
      }
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('✅ Role permissions page đã mở!');
    console.log('📝 Hướng dẫn test manual:');
    console.log('   1. Chọn một role từ dropdown');
    console.log('   2. Chọn/bỏ chọn một số permissions');
    console.log('   3. Click nút "Lưu"');
    console.log('   4. Kiểm tra thông báo thành công/thất bại');
    console.log('');
    console.log('🔍 Theo dõi console logs để xem debug info...');
    console.log('⏳ Browser sẽ mở trong 5 phút để bạn test...');
    
    // Giữ browser mở 5 phút
    await new Promise(resolve => setTimeout(resolve, 300000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành test manual');
  }
}

openRolePermissionsForManualTest().catch(console.error);
