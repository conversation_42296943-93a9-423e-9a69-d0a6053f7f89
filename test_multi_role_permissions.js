// Test user với multiple roles c<PERSON> đủ permissions từ tất cả roles
const puppeteer = require('puppeteer');

async function testMultiRolePermissions() {
  console.log('🚀 Test multi-role permissions...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('Permission') || text.includes('DEBUG') || text.includes('AppsPage')) {
      console.log(`🔍 Frontend: ${text}`);
    }
  });
  
  try {
    // Test với user admin có 2 roles: Admin + SuperAdmin
    console.log('🔐 Đăng nhập với admin (có 2 roles: Admin + SuperAdmin)...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    
    // Clear any existing data
    await page.evaluate(() => {
      localStorage.clear();
    });
    
    await page.type('input[placeholder="Tên đăng nhập"]', 'admin');
    await page.type('input[placeholder="Mật khẩu"]', 'admin123');
    await page.click('button[type="submit"]');
    
    // Đợi login process hoàn thành
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Navigate đến apps page
    console.log('🔄 Navigate đến apps page...');
    await page.goto('http://localhost:5173/apps', { waitUntil: 'networkidle2' });
    
    // Đợi permissions và modules load
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    // Kiểm tra user info và permissions
    const userInfo = await page.evaluate(() => {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      return {
        userId: user.id,
        username: user.username,
        hasUser: !!user.id
      };
    });
    
    console.log('📊 User info:', userInfo);
    
    if (userInfo.hasUser) {
      // Test permissions API
      const permissionsResponse = await page.evaluate(async (userId) => {
        try {
          const token = localStorage.getItem('token');
          const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          };
          
          if (token) {
            headers['Authorization'] = `Bearer ${token}`;
          }
          
          const response = await fetch(`http://localhost:8000/api/user-management/users/${userId}/permissions`, {
            headers
          });
          
          if (response.ok) {
            return await response.json();
          } else {
            return { error: `HTTP ${response.status}: ${response.statusText}` };
          }
        } catch (error) {
          return { error: error.message };
        }
      }, userInfo.userId);
      
      console.log('🔍 Admin permissions count:', permissionsResponse.length);
      console.log('🔍 Admin permissions by module:');
      
      if (Array.isArray(permissionsResponse)) {
        const moduleGroups = {};
        permissionsResponse.forEach(perm => {
          const module = perm.module_name || 'unknown';
          if (!moduleGroups[module]) {
            moduleGroups[module] = [];
          }
          moduleGroups[module].push(perm.code);
        });
        
        Object.keys(moduleGroups).forEach(module => {
          console.log(`   ${module}: ${moduleGroups[module].length} permissions`);
          moduleGroups[module].forEach(code => {
            console.log(`     - ${code}`);
          });
        });
      }
      
      // Test user roles API
      const userRolesResponse = await page.evaluate(async () => {
        try {
          const response = await fetch(`http://localhost:8000/api/user-management/user-roles`);
          if (response.ok) {
            const allUserRoles = await response.json();
            return allUserRoles.filter(ur => ur.username === 'admin');
          } else {
            return { error: `HTTP ${response.status}: ${response.statusText}` };
          }
        } catch (error) {
          return { error: error.message };
        }
      });
      
      console.log('🔍 Admin roles:', userRolesResponse);
      
      // Đếm module cards visible
      const moduleInfo = await page.evaluate(() => {
        const moduleCards = document.querySelectorAll('.ant-card');
        const moduleTexts = Array.from(moduleCards).map(card => {
          const title = card.querySelector('.ant-typography');
          return title ? title.textContent.trim() : 'Unknown';
        });
        return {
          count: moduleCards.length,
          modules: moduleTexts
        };
      });
      
      console.log('📊 Module cards visible to admin:');
      console.log(`   Count: ${moduleInfo.count}`);
      moduleInfo.modules.forEach((module, index) => {
        console.log(`   ${index + 1}. ${module}`);
      });
      
      console.log('');
      console.log('📝 EXPECTED RESULT:');
      console.log('   - Admin có 2 roles: Admin + SuperAdmin');
      console.log('   - Admin should see ALL modules (vì SuperAdmin có full permissions)');
      console.log('   - Nếu chỉ thấy theo 1 role thì có vấn đề với permission aggregation');
      console.log('');
      
      if (moduleInfo.count >= 5) {
        console.log('✅ MULTI-ROLE PERMISSION SYSTEM WORKING - admin sees multiple modules');
      } else {
        console.log('❌ POTENTIAL ISSUE - admin should see more modules with SuperAdmin role');
      }
    } else {
      console.log('❌ User not logged in properly');
    }
    
    console.log('⏳ Browser sẽ mở trong 60 giây để kiểm tra...');
    await new Promise(resolve => setTimeout(resolve, 60000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành test');
  }
}

testMultiRolePermissions().catch(console.error);
