// Test manual để kiểm tra auto-check permissions
const puppeteer = require('puppeteer');

async function testManualAutoCheck() {
  console.log('🚀 Mở browser để test manual auto-check permissions...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('RolePermissionsPage') || text.includes('checked keys') || text.includes('permissions loaded')) {
      console.log(`🔍 Frontend: ${text}`);
    }
  });
  
  try {
    // Đăng nhập
    console.log('🔐 Đăng nhập...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    await page.type('input[placeholder="Tên đăng nhập"]', 'admin');
    await page.type('input[placeholder="Mật khẩu"]', 'admin123');
    await page.click('button[type="submit"]');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Navigate đến user-management
    console.log('🔄 Navigate đến user-management...');
    await page.goto('http://localhost:5173/user-management', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Click vào menu Role permissions
    console.log('📋 Click vào menu Role permissions...');
    await page.evaluate(() => {
      const menuItems = Array.from(document.querySelectorAll('.ant-menu-item'));
      const rolePermissionsItem = menuItems.find(item => 
        item.textContent && item.textContent.includes('Role permissions')
      );
      
      if (rolePermissionsItem) {
        rolePermissionsItem.click();
      }
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('✅ Role permissions page đã mở!');
    console.log('');
    console.log('📝 HƯỚNG DẪN TEST AUTO-CHECK PERMISSIONS:');
    console.log('');
    console.log('   🎯 BƯỚC 1: Chọn role "admin" (role ID 6)');
    console.log('      - Click vào dropdown role');
    console.log('      - Chọn role "admin"');
    console.log('      - Quan sát xem có permissions nào được tự động check không');
    console.log('');
    console.log('   🎯 BƯỚC 2: Chọn role "SuperAdmin" (role ID 1)');
    console.log('      - Click vào dropdown role');
    console.log('      - Chọn role "SuperAdmin"');
    console.log('      - Quan sát xem có permissions nào được tự động check không');
    console.log('');
    console.log('   📊 THÔNG TIN PERMISSIONS ĐÃ GÁN:');
    console.log('      - Role "admin" (ID 6): có permissions [1, 2, 3]');
    console.log('      - Role "SuperAdmin" (ID 1): có permissions [1, 2]');
    console.log('');
    console.log('   ✅ KẾT QUẢ MONG ĐỢI:');
    console.log('      - Khi chọn role "admin": permissions 1, 2, 3 sẽ được tự động check');
    console.log('      - Khi chọn role "SuperAdmin": permissions 1, 2 sẽ được tự động check');
    console.log('');
    console.log('🔍 Theo dõi console logs để xem debug info...');
    console.log('⏳ Browser sẽ mở trong 5 phút để bạn test...');
    console.log('');
    
    // Giữ browser mở 5 phút
    await new Promise(resolve => setTimeout(resolve, 300000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành test manual');
  }
}

testManualAutoCheck().catch(console.error);
