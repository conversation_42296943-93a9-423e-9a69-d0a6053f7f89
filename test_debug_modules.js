// Test debug modules loading và filtering
const puppeteer = require('puppeteer');

async function testDebugModules() {
  console.log('🚀 Test debug modules loading...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('DEBUG') || text.includes('AppsPage') || text.includes('Permission')) {
      console.log(`🔍 Frontend: ${text}`);
    }
  });
  
  try {
    // Đăng nhập với admin
    console.log('🔐 Đăng nhập với admin...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    
    // Clear any existing data
    await page.evaluate(() => {
      localStorage.clear();
    });
    
    await page.type('input[placeholder="Tên đăng nhập"]', 'admin');
    await page.type('input[placeholder="Mật khẩu"]', 'admin123');
    await page.click('button[type="submit"]');
    
    // Đợi login process hoàn thành
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Navigate đến apps page
    console.log('🔄 Navigate đến apps page...');
    await page.goto('http://localhost:5173/apps', { waitUntil: 'networkidle2' });
    
    // Đợi modules load
    await new Promise(resolve => setTimeout(resolve, 15000));
    
    console.log('⏳ Browser sẽ mở trong 30 giây để kiểm tra debug logs...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành test');
  }
}

testDebugModules().catch(console.error);
