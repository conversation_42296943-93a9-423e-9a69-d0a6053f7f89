// Final test để kiểm tra auto-check permissions
const puppeteer = require('puppeteer');

async function testFinalAutoCheck() {
  console.log('🚀 Final test auto-check permissions...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('RolePermissionsPage') || text.includes('Role selected') || text.includes('checked keys') || text.includes('permissions loaded') || text.includes('Tree onCheck')) {
      console.log(`🔍 Frontend: ${text}`);
    }
  });
  
  try {
    // Đăng nhập
    console.log('🔐 Đăng nhập...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    await page.type('input[placeholder="Tên đăng nhập"]', 'admin');
    await page.type('input[placeholder="Mật khẩu"]', 'admin123');
    await page.click('button[type="submit"]');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Navigate đến user-management
    console.log('🔄 Navigate đến user-management...');
    await page.goto('http://localhost:5173/user-management', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Click vào menu Role permissions bằng cách tìm menu item
    console.log('📋 Click vào menu Role permissions...');
    const menuClicked = await page.evaluate(() => {
      // Tìm menu item có text "Role permissions"
      const menuItems = Array.from(document.querySelectorAll('.ant-menu-item'));
      console.log('Found menu items:', menuItems.length);
      
      for (let item of menuItems) {
        const text = item.textContent || '';
        console.log('Menu item text:', text);
        if (text.includes('Role permissions')) {
          console.log('Found Role permissions menu item, clicking...');
          item.click();
          return true;
        }
      }
      
      // Nếu không tìm thấy, thử tìm bằng data-menu-id
      const rolePermissionsItem = document.querySelector('[data-menu-id="role-permissions"]');
      if (rolePermissionsItem) {
        console.log('Found Role permissions by data-menu-id, clicking...');
        rolePermissionsItem.click();
        return true;
      }
      
      return false;
    });
    
    if (!menuClicked) {
      console.log('❌ Không tìm thấy menu Role permissions, thử cách khác...');
      
      // Thử click vào menu item thứ 5 (index 4)
      await page.evaluate(() => {
        const menuItems = document.querySelectorAll('.ant-menu-item');
        if (menuItems.length >= 5) {
          console.log('Clicking 5th menu item...');
          menuItems[4].click();
        }
      });
    }
    
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('✅ Đã click menu, kiểm tra xem RolePermissionsPage có load không...');
    
    // Kiểm tra xem RolePermissionsPage có được render không
    const hasRolePermissionsPage = await page.evaluate(() => {
      // Tìm SearchRoleDropdown component
      const dropdown = document.querySelector('.search-role-dropdown');
      const tree = document.querySelector('.ant-tree');
      
      return {
        hasDropdown: !!dropdown,
        hasTree: !!tree,
        dropdownVisible: dropdown ? getComputedStyle(dropdown).display !== 'none' : false,
        treeVisible: tree ? getComputedStyle(tree).display !== 'none' : false
      };
    });
    
    console.log('📊 RolePermissionsPage status:', hasRolePermissionsPage);
    
    if (hasRolePermissionsPage.hasDropdown && hasRolePermissionsPage.hasTree) {
      console.log('✅ RolePermissionsPage đã load thành công!');
      
      // Kiểm tra tree structure
      const treeInfo = await page.evaluate(() => {
        const treeNodes = document.querySelectorAll('.ant-tree-treenode');
        const nodeInfo = Array.from(treeNodes).slice(0, 3).map(node => ({
          key: node.getAttribute('data-key'),
          title: node.querySelector('.ant-tree-title')?.textContent?.trim(),
          hasCheckbox: !!node.querySelector('.ant-tree-checkbox'),
          isChecked: node.querySelector('.ant-tree-checkbox')?.classList.contains('ant-tree-checkbox-checked')
        }));
        
        return {
          totalNodes: treeNodes.length,
          sampleNodes: nodeInfo
        };
      });
      
      console.log('📊 Tree structure:', JSON.stringify(treeInfo, null, 2));
      
      console.log('⏳ Giữ browser mở 3 phút để test manual...');
      console.log('📝 HƯỚNG DẪN:');
      console.log('   1. Click vào dropdown role');
      console.log('   2. Chọn role "admin" (có permissions [1,2,3])');
      console.log('   3. Quan sát xem có permissions nào được auto-check không');
      console.log('   4. Thử chọn role khác để so sánh');
      
      await new Promise(resolve => setTimeout(resolve, 180000));
      
    } else {
      console.log('❌ RolePermissionsPage chưa load được');
    }
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành test');
  }
}

testFinalAutoCheck().catch(console.error);
