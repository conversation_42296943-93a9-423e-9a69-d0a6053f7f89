// Debug script để kiểm tra frontend
const puppeteer = require('puppeteer');

async function debugFrontend() {
  console.log('🚀 Bắt đầu debug frontend...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs từ browser
  page.on('console', msg => {
    const type = msg.type();
    const text = msg.text();
    console.log(`🔍 [BROWSER ${type.toUpperCase()}] ${text}`);
  });
  
  // Lắng nghe network requests
  page.on('response', response => {
    const url = response.url();
    const status = response.status();
    if (url.includes('/api/') || status >= 400) {
      console.log(`🌐 [${status}] ${url}`);
    }
  });
  
  // Lắng nghe errors
  page.on('pageerror', error => {
    console.error('❌ [PAGE ERROR]', error.message);
  });
  
  try {
    console.log('📱 Mở trang frontend...');
    await page.goto('http://localhost:5173', { waitUntil: 'networkidle2' });
    
    console.log('⏳ Chờ trang load...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Kiểm tra xem có React app không
    const appElement = await page.$('#root');
    if (!appElement) {
      throw new Error('Không tìm thấy React app element');
    }
    
    console.log('✅ React app đã load thành công');
    
    // Chụp screenshot trang chủ
    await page.screenshot({ path: 'debug_homepage.png', fullPage: true });
    console.log('📸 Đã chụp screenshot: debug_homepage.png');
    
    // Thử navigate trực tiếp đến user-management
    console.log('🔄 Navigate đến /user-management...');
    await page.goto('http://localhost:5173/user-management', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Chụp screenshot user management page
    await page.screenshot({ path: 'debug_user_management.png', fullPage: true });
    console.log('📸 Đã chụp screenshot: debug_user_management.png');
    
    // Tìm tất cả các tab
    console.log('🔍 Tìm kiếm các tabs...');
    const tabs = await page.$$eval('[role="tab"], .ant-tabs-tab', tabs => 
      tabs.map(tab => ({
        text: tab.textContent?.trim(),
        className: tab.className,
        visible: tab.offsetParent !== null
      }))
    );
    console.log('📋 Các tabs tìm thấy:', tabs);
    
    // Tìm tab Role permissions
    const rolePermTab = await page.$('[role="tab"]:has-text("Role permissions"), .ant-tabs-tab:has-text("Role permissions")');
    if (rolePermTab) {
      console.log('✅ Tìm thấy tab Role permissions, click vào...');
      await rolePermTab.click();
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Chụp screenshot sau khi click tab
      await page.screenshot({ path: 'debug_role_permissions.png', fullPage: true });
      console.log('📸 Đã chụp screenshot: debug_role_permissions.png');
      
      // Tìm button Lưu
      const saveButton = await page.$('button:has-text("Lưu")');
      if (saveButton) {
        console.log('✅ Tìm thấy button Lưu');
        
        // Kiểm tra xem button có disabled không
        const isDisabled = await saveButton.evaluate(btn => btn.disabled);
        console.log(`🔘 Button Lưu disabled: ${isDisabled}`);
        
        if (!isDisabled) {
          console.log('🖱️ Click button Lưu...');
          await saveButton.click();
          await new Promise(resolve => setTimeout(resolve, 2000));
          
          // Chụp screenshot sau khi click
          await page.screenshot({ path: 'debug_after_save_click.png', fullPage: true });
          console.log('📸 Đã chụp screenshot: debug_after_save_click.png');
        }
      } else {
        console.log('❌ Không tìm thấy button Lưu');
      }
    } else {
      console.log('❌ Không tìm thấy tab Role permissions');
    }
    
    console.log('⏳ Giữ browser mở 30 giây để quan sát...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
  } catch (error) {
    console.error('❌ Lỗi trong quá trình debug:', error);
    await page.screenshot({ path: 'debug_error.png', fullPage: true });
    console.log('📸 Đã chụp screenshot lỗi: debug_error.png');
  } finally {
    await browser.close();
    console.log('🏁 Debug hoàn thành');
  }
}

// Chạy debug
debugFrontend().catch(console.error);
