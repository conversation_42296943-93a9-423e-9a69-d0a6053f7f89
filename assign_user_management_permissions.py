#!/usr/bin/env python3
"""
Script để gán tất cả permissions user_management cho role SuperAdmin
"""
import requests
import json

# API URLs
BASE_URL = "http://localhost:8000/api/user-management"
SUPER_ADMIN_ROLE_ID = 1  # SuperAdmin role

def get_all_permissions():
    """Lấy tất cả permissions"""
    print("🔍 Lấy tất cả permissions...")
    
    try:
        response = requests.get(f"{BASE_URL}/permissions")
        if response.status_code == 200:
            permissions = response.json()
            print(f"✅ Có {len(permissions)} permissions:")
            
            user_mgmt_perms = []
            for perm in permissions:
                if perm['module_name'] == 'user_management':
                    user_mgmt_perms.append(perm)
                    print(f"  - ID {perm['id']}: {perm['code']} ({perm['type']}) - {perm['name']}")
            
            return user_mgmt_perms
        else:
            print("❌ Không thể lấy permissions!")
            return []
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return []

def assign_permissions_to_role(permission_ids):
    """<PERSON><PERSON> permissions cho role"""
    print(f"🔄 Gán {len(permission_ids)} permissions cho role {SUPER_ADMIN_ROLE_ID}...")
    
    payload = {
        "role_id": SUPER_ADMIN_ROLE_ID,
        "permission_ids": permission_ids
    }
    
    try:
        response = requests.post(f"{BASE_URL}/role-permissions", json=payload)
        print(f"📊 Response status: {response.status_code}")
        print(f"📊 Response body: {response.text}")
        
        if response.status_code == 200:
            print("✅ Gán permissions thành công!")
        else:
            print("❌ Gán permissions thất bại!")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")

def check_user_permissions():
    """Kiểm tra permissions của user admin"""
    USER_ID = "d3f3565c-08fe-4f04-b61a-cedb421fc628"
    print(f"🔍 Kiểm tra permissions của user {USER_ID}...")
    
    try:
        response = requests.get(f"{BASE_URL}/users/{USER_ID}/permissions")
        if response.status_code == 200:
            permissions = response.json()
            print(f"✅ User có {len(permissions)} permissions:")
            
            user_mgmt_count = 0
            for perm in permissions:
                if perm['module_name'] == 'user_management':
                    user_mgmt_count += 1
                    print(f"  - {perm['code']} ({perm['type']}): {perm['name']}")
            
            print(f"📊 Trong đó có {user_mgmt_count} permissions cho user_management")
        else:
            print("❌ Không thể lấy user permissions!")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    print("🚀 Bắt đầu gán user_management permissions...")
    
    # 1. Lấy tất cả user_management permissions
    user_mgmt_perms = get_all_permissions()
    
    if user_mgmt_perms:
        # 2. Lấy danh sách permission IDs
        permission_ids = [perm['id'] for perm in user_mgmt_perms]
        print(f"📋 Permission IDs to assign: {permission_ids}")
        print()
        
        # 3. Gán permissions cho role
        assign_permissions_to_role(permission_ids)
        print()
        
        # 4. Kiểm tra kết quả
        check_user_permissions()
    else:
        print("❌ Không tìm thấy user_management permissions!")
    
    print("🏁 Hoàn thành!")
