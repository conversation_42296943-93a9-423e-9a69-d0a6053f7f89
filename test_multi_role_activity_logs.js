// Test multi-role permission aggregation với activity_logs
const puppeteer = require('puppeteer');

async function testMultiRoleActivityLogs() {
  console.log('🧪 TEST: Multi-role permission aggregation với activity_logs...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  try {
    // Đăng nhập với admin
    console.log('🔐 Đăng nhập với admin...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    
    await page.evaluate(() => localStorage.clear());
    
    await page.type('input[placeholder="Tên đăng nhập"]', 'admin');
    await page.type('input[placeholder="Mật khẩu"]', 'admin123');
    await page.click('button[type="submit"]');
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Navigate đến apps page
    console.log('🔄 Navigate đến apps page...');
    await page.goto('http://localhost:5173/apps', { waitUntil: 'networkidle2' });
    
    await new Promise(resolve => setTimeout(resolve, 8000));
    
    // Kiểm tra permissions
    const permissionsInfo = await page.evaluate(async () => {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (!user.id) return { error: 'No user found' };
      
      try {
        const token = localStorage.getItem('token');
        const headers = {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        };
        
        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
        }
        
        const response = await fetch(`http://localhost:8000/api/user-management/users/${user.id}/permissions`, {
          headers
        });
        
        if (response.ok) {
          const permissions = await response.json();
          const activityLogsPerms = permissions.filter(p => p.module_name === 'activity_logs');
          
          return {
            userId: user.id,
            username: user.username,
            totalPermissions: permissions.length,
            activityLogsPermissions: activityLogsPerms,
            hasActivityLogsPermission: activityLogsPerms.length > 0
          };
        } else {
          return { error: `HTTP ${response.status}: ${response.statusText}` };
        }
      } catch (error) {
        return { error: error.message };
      }
    });
    
    // Đếm module cards visible trên System tab
    const systemModuleInfo = await page.evaluate(() => {
      const moduleCards = document.querySelectorAll('.ant-card');
      const moduleTexts = Array.from(moduleCards).map(card => {
        const title = card.querySelector('.ant-typography');
        return title ? title.textContent.trim() : 'Unknown';
      });
      
      const hasActivityLogs = moduleTexts.some(text => 
        text.toLowerCase().includes('activity') || 
        text.toLowerCase().includes('log') ||
        text.includes('Activity Logs')
      );
      
      return {
        count: moduleCards.length,
        modules: moduleTexts,
        hasActivityLogsModule: hasActivityLogs
      };
    });
    
    console.log('');
    console.log('🎯 TEST RESULTS:');
    console.log('================');
    console.log(`👤 User: ${permissionsInfo.username} (${permissionsInfo.userId})`);
    console.log(`🔑 Total permissions: ${permissionsInfo.totalPermissions}`);
    console.log(`📊 Activity logs permissions: ${permissionsInfo.activityLogsPermissions?.length || 0}`);
    
    if (permissionsInfo.activityLogsPermissions?.length > 0) {
      permissionsInfo.activityLogsPermissions.forEach(perm => {
        console.log(`   - ${perm.code}: ${perm.name}`);
      });
    }
    
    console.log('');
    console.log('📱 Frontend module visibility:');
    console.log(`   System tab modules: ${systemModuleInfo.count}`);
    systemModuleInfo.modules.forEach((module, index) => {
      console.log(`     ${index + 1}. ${module}`);
    });
    
    console.log('');
    console.log('✅ VERIFICATION:');
    
    if (permissionsInfo.hasActivityLogsPermission) {
      console.log('   ✅ Backend: User có permission activity_logs từ role Admin');
    } else {
      console.log('   ❌ Backend: User KHÔNG có permission activity_logs');
    }
    
    if (systemModuleInfo.hasActivityLogsModule) {
      console.log('   ✅ Frontend: Activity logs module hiển thị trên giao diện');
    } else {
      console.log('   ❌ Frontend: Activity logs module KHÔNG hiển thị trên giao diện');
    }
    
    console.log('');
    if (permissionsInfo.hasActivityLogsPermission && systemModuleInfo.hasActivityLogsModule) {
      console.log('🎉 SUCCESS: Multi-role permission aggregation hoạt động HOÀN HẢO!');
      console.log('   - User admin có permission từ role Admin');
      console.log('   - Frontend hiển thị module dựa trên aggregated permissions');
      console.log('   - Hệ thống ưu tiên permissions từ TẤT CẢ roles, không chỉ SuperAdmin');
    } else if (permissionsInfo.hasActivityLogsPermission && !systemModuleInfo.hasActivityLogsModule) {
      console.log('⚠️  PARTIAL: Backend có permission nhưng frontend không hiển thị');
      console.log('   - Có thể do cache hoặc permission filtering logic');
    } else {
      console.log('❌ ISSUE: Vẫn còn vấn đề với multi-role permission aggregation');
    }
    
    console.log('⏳ Browser sẽ mở trong 20 giây để review...');
    await new Promise(resolve => setTimeout(resolve, 20000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành test');
  }
}

testMultiRoleActivityLogs().catch(console.error);
