// Test debug tất cả modules và permissions
const puppeteer = require('puppeteer');

async function testDebugAllModules() {
  console.log('🧪 TEST: Debug tất cả modules và permissions...');

  const browser = await puppeteer.launch({
    headless: false,
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  const page = await browser.newPage();

  try {
    // Đăng nhập với admin
    console.log('🔐 Đăng nhập với admin...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });

    await page.evaluate(() => localStorage.clear());

    await page.type('input[placeholder="Tên đăng nhập"]', 'admin');
    await page.type('input[placeholder="Mật khẩu"]', 'admin123');
    await page.click('button[type="submit"]');

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Navigate đến apps page
    console.log('🔄 Navigate đến apps page...');
    await page.goto('http://localhost:5173/apps', { waitUntil: 'networkidle2' });

    await new Promise(resolve => setTimeout(resolve, 8000));

    // Kiểm tra permissions và modules
    const debugInfo = await page.evaluate(async () => {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (!user.id) return { error: 'No user found' };

      try {
        const token = localStorage.getItem('token');
        const headers = {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        };

        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
        }

        // Lấy user permissions
        const permResponse = await fetch(`http://localhost:8000/api/user-management/users/${user.id}/permissions`, {
          headers
        });

        // Lấy tất cả modules
        const moduleResponse = await fetch(`http://localhost:8000/api/internal/modules/all`, {
          headers
        });

        if (permResponse.ok && moduleResponse.ok) {
          const permissions = await permResponse.json();
          const moduleData = await moduleResponse.json();

          // Group permissions by module
          const permissionsByModule = {};
          permissions.forEach(perm => {
            if (!permissionsByModule[perm.module_name]) {
              permissionsByModule[perm.module_name] = [];
            }
            permissionsByModule[perm.module_name].push(perm);
          });

          // Check each module permission
          const modulePermissionCheck = moduleData.modules.map(module => {
            const modulePerms = permissionsByModule[module.name] || [];
            const hasPermission = modulePerms.some(perm => perm.code.startsWith(`${module.name}_`));

            return {
              name: module.name,
              is_core_module: module.is_core_module,
              permissionCount: modulePerms.length,
              permissions: modulePerms.map(p => p.code),
              hasPermission: hasPermission
            };
          });

          return {
            userId: user.id,
            username: user.username,
            totalPermissions: permissions.length,
            totalModules: moduleData.modules.length,
            permissionsByModule: permissionsByModule,
            modulePermissionCheck: modulePermissionCheck
          };
        } else {
          return { error: `HTTP ${permResponse.status}/${moduleResponse.status}` };
        }
      } catch (error) {
        return { error: error.message };
      }
    });

    // Đếm module cards visible trên System tab
    const frontendModuleInfo = await page.evaluate(() => {
      const moduleCards = document.querySelectorAll('.ant-card');
      const moduleTexts = Array.from(moduleCards).map(card => {
        const title = card.querySelector('.ant-typography');
        return title ? title.textContent.trim() : 'Unknown';
      });

      return {
        count: moduleCards.length,
        modules: moduleTexts
      };
    });

    console.log('');
    console.log('🎯 DETAILED DEBUG RESULTS:');
    console.log('===========================');
    console.log(`👤 User: ${debugInfo.username} (${debugInfo.userId})`);
    console.log(`🔑 Total permissions: ${debugInfo.totalPermissions}`);
    console.log(`📦 Total modules: ${debugInfo.totalModules}`);
    console.log('');

    console.log('📊 PERMISSION CHECK BY MODULE:');
    console.log('===============================');
    debugInfo.modulePermissionCheck.forEach(module => {
      const status = module.hasPermission ? '✅' : '❌';
      const type = module.is_core_module ? 'CORE' : 'DEV';
      console.log(`${status} ${module.name} (${type}) - ${module.permissionCount} permissions`);
      if (module.permissions.length > 0) {
        module.permissions.forEach(perm => {
          console.log(`     - ${perm}`);
        });
      }
    });

    console.log('');
    console.log('📱 FRONTEND MODULE VISIBILITY:');
    console.log('===============================');
    console.log(`Frontend hiển thị: ${frontendModuleInfo.count} modules`);
    frontendModuleInfo.modules.forEach((module, index) => {
      console.log(`     ${index + 1}. ${module}`);
    });

    console.log('');
    console.log('🔍 ANALYSIS:');
    console.log('=============');

    const expectedModules = debugInfo.modulePermissionCheck.filter(m => m.hasPermission);
    const coreModulesWithPermission = expectedModules.filter(m => m.is_core_module);
    const devModulesWithPermission = expectedModules.filter(m => !m.is_core_module);

    console.log(`Expected modules with permissions: ${expectedModules.length}`);
    console.log(`  - Core modules: ${coreModulesWithPermission.length}`);
    console.log(`  - Dev modules: ${devModulesWithPermission.length}`);
    console.log(`Frontend showing: ${frontendModuleInfo.count} modules`);

    if (expectedModules.length === frontendModuleInfo.count) {
      console.log('✅ PERFECT: Frontend hiển thị đúng số lượng modules!');
    } else {
      console.log('❌ MISMATCH: Frontend không hiển thị đúng số lượng modules!');
      console.log('');
      console.log('🔍 MISSING MODULES:');
      expectedModules.forEach(expected => {
        const found = frontendModuleInfo.modules.some(frontend =>
          frontend.toLowerCase().includes(expected.name.toLowerCase()) ||
          expected.name.toLowerCase().includes(frontend.toLowerCase())
        );
        if (!found) {
          console.log(`   ❌ ${expected.name} - Có permission nhưng không hiển thị`);
        }
      });
    }

    console.log('⏳ Browser sẽ mở trong 30 giây để review...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành test');
  }
}

testDebugAllModules().catch(console.error);
