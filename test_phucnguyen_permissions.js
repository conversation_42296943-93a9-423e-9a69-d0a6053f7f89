// Test phucnguyen permissions on AppsPage
const puppeteer = require('puppeteer');

async function testPhucnguyenPermissions() {
  console.log('🚀 Test phucnguyen permissions on AppsPage...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('AppsPage') || text.includes('PermissionContext') || text.includes('permissions')) {
      console.log(`🔍 Frontend: ${text}`);
    }
  });
  
  try {
    // Đăng nhập với phucnguyen
    console.log('🔐 Đăng nhập với phucnguyen...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    await page.type('input[placeholder="Tên đăng nhập"]', 'phucnguyen');
    await page.type('input[placeholder="Mật khẩu"]', 'phuc123');
    await page.click('button[type="submit"]');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Navigate đến apps page
    console.log('🔄 Navigate đến apps page...');
    await page.goto('http://localhost:5173/apps', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 8000)); // Đợi lâu hơn để permissions load
    
    // Kiểm tra permissions được load
    const permissionsInfo = await page.evaluate(() => {
      // Kiểm tra localStorage
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      
      return {
        userId: user.id,
        username: user.username,
        localStorageUser: user
      };
    });
    
    console.log('📊 User info:', permissionsInfo);
    
    // Test API call để lấy permissions
    console.log('🔍 Testing permissions API...');
    const response = await page.evaluate(async (userId) => {
      try {
        const response = await fetch(`http://localhost:8000/api/user-management/users/${userId}/permissions`);
        const permissions = await response.json();
        console.log('Phucnguyen permissions from API:', permissions);
        return permissions;
      } catch (error) {
        console.error('Error fetching permissions:', error);
        return null;
      }
    }, permissionsInfo.userId);
    
    console.log('✅ Phucnguyen permissions:', response);
    
    if (response && response.length > 0) {
      console.log('📋 Permission codes:');
      response.forEach(perm => {
        console.log(`  - ${perm.code} (${perm.type}): ${perm.name}`);
      });
    } else {
      console.log('❌ No permissions found for phucnguyen');
    }
    
    // Đếm module cards visible
    const moduleInfo = await page.evaluate(() => {
      const moduleCards = document.querySelectorAll('.ant-card');
      const moduleTexts = Array.from(moduleCards).map(card => {
        const title = card.querySelector('.ant-typography');
        return title ? title.textContent.trim() : 'Unknown';
      });
      return {
        count: moduleCards.length,
        modules: moduleTexts
      };
    });
    
    console.log('📊 Module cards visible to phucnguyen:');
    console.log(`   Count: ${moduleInfo.count}`);
    moduleInfo.modules.forEach((module, index) => {
      console.log(`   ${index + 1}. ${module}`);
    });
    
    console.log('');
    console.log('📝 EXPECTED RESULT:');
    console.log('   - Phucnguyen should have 3 permissions: all for site_management');
    console.log('   - Phucnguyen should see ONLY site_management module');
    console.log('   - Phucnguyen should NOT see: user_management module');
    console.log('');
    
    if (moduleInfo.count === 1 && moduleInfo.modules.some(m => m.includes('Site Management'))) {
      console.log('✅ PERMISSION SYSTEM WORKING CORRECTLY!');
    } else if (moduleInfo.count === 0) {
      console.log('⚠️  No modules visible - permissions might still be loading');
    } else {
      console.log('❌ PERMISSION SYSTEM NOT WORKING - unexpected modules visible');
      console.log('   Expected: Only Site Management');
      console.log('   Actual:', moduleInfo.modules);
    }
    
    console.log('⏳ Browser sẽ mở trong 2 phút để kiểm tra...');
    
    await new Promise(resolve => setTimeout(resolve, 120000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành test');
  }
}

testPhucnguyenPermissions().catch(console.error);
