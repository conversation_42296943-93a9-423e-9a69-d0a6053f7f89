// Demo permission system - so s<PERSON>h admin vs testuser
const puppeteer = require('puppeteer');

async function demoPermissionSystem() {
  console.log('🚀 Demo Permission System...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('UserManagementPage: Menu') || text.includes('Filtered menu items')) {
      console.log(`🔍 Frontend: ${text}`);
    }
  });
  
  try {
    console.log('');
    console.log('='.repeat(60));
    console.log('🔐 DEMO 1: ADMIN USER (FULL PERMISSIONS)');
    console.log('='.repeat(60));
    
    // Test với admin user
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    await page.type('input[placeholder="Tên đăng nhập"]', 'admin');
    await page.type('input[placeholder="Mật khẩu"]', 'admin123');
    await page.click('button[type="submit"]');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    await page.goto('http://localhost:5173/user-management', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Đếm số menu items
    const adminMenuCount = await page.evaluate(() => {
      const menuItems = document.querySelectorAll('.ant-menu-item');
      console.log('Admin menu items count:', menuItems.length);
      const menuTexts = Array.from(menuItems).map(item => item.textContent.trim());
      console.log('Admin menu items:', menuTexts);
      return {
        count: menuItems.length,
        items: menuTexts
      };
    });
    
    console.log(`✅ Admin có ${adminMenuCount.count} menu items:`);
    adminMenuCount.items.forEach((item, index) => {
      console.log(`   ${index + 1}. ${item}`);
    });
    
    // Logout
    await page.evaluate(() => {
      localStorage.clear();
    });
    
    console.log('');
    console.log('='.repeat(60));
    console.log('🔐 DEMO 2: TEST USER (LIMITED PERMISSIONS)');
    console.log('='.repeat(60));
    
    // Test với testuser
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    await page.type('input[placeholder="Tên đăng nhập"]', 'testuser');
    await page.type('input[placeholder="Mật khẩu"]', 'test123');
    await page.click('button[type="submit"]');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    await page.goto('http://localhost:5173/user-management', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Đếm số menu items
    const testUserMenuCount = await page.evaluate(() => {
      const menuItems = document.querySelectorAll('.ant-menu-item');
      console.log('TestUser menu items count:', menuItems.length);
      const menuTexts = Array.from(menuItems).map(item => item.textContent.trim());
      console.log('TestUser menu items:', menuTexts);
      return {
        count: menuItems.length,
        items: menuTexts
      };
    });
    
    console.log(`✅ TestUser có ${testUserMenuCount.count} menu items:`);
    testUserMenuCount.items.forEach((item, index) => {
      console.log(`   ${index + 1}. ${item}`);
    });
    
    console.log('');
    console.log('='.repeat(60));
    console.log('📊 KẾT QUẢ SO SÁNH');
    console.log('='.repeat(60));
    console.log(`🔹 Admin: ${adminMenuCount.count} menu items`);
    console.log(`🔹 TestUser: ${testUserMenuCount.count} menu items`);
    console.log(`🔹 Chênh lệch: ${adminMenuCount.count - testUserMenuCount.count} menu items`);
    
    if (testUserMenuCount.count < adminMenuCount.count) {
      console.log('✅ PERMISSION SYSTEM HOẠT ĐỘNG ĐÚNG!');
      console.log('   - User với quyền hạn chế chỉ thấy ít menu hơn');
      console.log('   - Hệ thống đã ẩn các menu không có quyền truy cập');
    } else {
      console.log('❌ PERMISSION SYSTEM CHƯA HOẠT ĐỘNG!');
      console.log('   - Cả hai user đều thấy cùng số lượng menu');
    }
    
    console.log('');
    console.log('📝 HƯỚNG DẪN TIẾP THEO:');
    console.log('   1. Có thể test thêm với các user khác');
    console.log('   2. Áp dụng PermissionGuard cho buttons trong từng trang');
    console.log('   3. Áp dụng cho các modules khác (site_management, etc)');
    console.log('');
    console.log('⏳ Browser sẽ mở trong 2 phút để kiểm tra thêm...');
    
    await new Promise(resolve => setTimeout(resolve, 120000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành demo');
  }
}

demoPermissionSystem().catch(console.error);
