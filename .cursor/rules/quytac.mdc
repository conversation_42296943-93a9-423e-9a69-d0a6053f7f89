---
description: 
globs: 
alwaysApply: true
---
<PERSON><PERSON><PERSON> quy tắc bên dưới sẽ được review khi tạo module mới.
--<Quy tắc chung>-------------------------
X<PERSON>y dựng giải pháp theo kiến trúc của <PERSON>, cho phép người dùng có thể Cài đặt các ứng dụng từ AppsPage và cấu hình database tương ứng trong quá trình cài đặt.

Tích hợp với i18n để hiển thị đa ngôn ngữ.

Một module mới được xây dựng hoàn toàn độc lập với các module khác, không bị chồng chéo code với các module đã có sẵn. Các module có thể gọi lẫn nhau để thực hiện các tác vụ.

AppsPage load động module từ backend lên, không tạo module mẫu để demo. 

Trong AppsPage, nhấn nút Cài đặt để cài module. Sau khi cài xong thì hiển thị nút Gỡ cài đặt, nếu nhấn vào thì gỡ toàn bộ phần cài đặt, bao gồm cả việc xóa các dữ liệu, bảng… đã tạo từ phần Cài Đặt trong Database.

Frontend: Viết giao diện bằng React và TypeScript, sử dụng thư viện Ant Design. 
Backend: Python (FastAPI & Django)
Database: PostgresSQL
--</Quy tắc chung>-------------------------

--<Cấu hình Database>-------------------------
Database name: metisdb
Username: postgres
Password: postgres
--</Cấu hình Database>-------------------------

--<Module Management>-------------------------
Đảm bảo trong cấu hình của Module có thuộc tính IsCoreModule.
	- Nếu IsCoreModule = True thì nó là loại Core Module, hiển thị trong tab Module hệ thống. Mặc định cấu hình auto_install = True, hidden = False, required = True. Khi Module đã được cài đặt thì hiển thị thông tin là "Đã cài đặt".
	- Nếu IsCoreModule = False thì nó là loại Module phát triển, hiển thị trong tab Module phát triển. hi Module đã được cài đặt thì hiển thị nút "Gỡ cài đặt" cho phép người dùng nhấn vào để gỡ cài đặt module.
auto_install = True: khi khởi động server, kiểm tra module này có được cài đặt chưa, nếu chưa thì tự động cài đặt, nếu cài rồi thì không làm gì.
auto_install = False: khi khởi động server không làm gì.
hidden = True: không hiển thị lên bất kỳ tab nào.
hidden = False: hiển thị lên tab tương ứng theo cấu hình IsCoreModule.
--</Module Management>-------------------------