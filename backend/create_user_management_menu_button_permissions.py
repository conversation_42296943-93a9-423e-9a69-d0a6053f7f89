# Script tự động sinh permission cho menu và button trong module user_management
import requests
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from src.modules.user_management.models import Permission

API_URL = os.getenv("PERMISSION_API_URL", "http://localhost:8000/api/user-management/permissions")
MODULE_NAME = "user_management"

MENUS = [
    {"key": "overview", "label": "Tổng quan"},
    {"key": "users", "label": "Người dùng"},
    {"key": "roles", "label": "Vai trò"},
    {"key": "user_roles", "label": "Gán vai trò"}
]

BUTTONS = [
    {"key": "edit_user", "label": "Sửa user", "parent_code": "user_management_menu_users"},
    {"key": "delete_user", "label": "Xóa user", "parent_code": "user_management_menu_users"},
    {"key": "refresh_user", "label": "Làm mới user", "parent_code": "user_management_menu_users"},
    {"key": "add_user", "label": "Thêm user", "parent_code": "user_management_menu_users"},
    {"key": "save_and_new_user", "label": "Lưu & Thêm mới user", "parent_code": "user_management_menu_users"},
    {"key": "save_and_close_user", "label": "Lưu & Đ<PERSON>g user", "parent_code": "user_management_menu_users"},
    {"key": "edit_role", "label": "Sửa role", "parent_code": "user_management_menu_roles"},
    {"key": "delete_role", "label": "Xóa role", "parent_code": "user_management_menu_roles"},
    {"key": "cancel_role", "label": "Hủy thêm role", "parent_code": "user_management_menu_roles"},
    {"key": "add_role", "label": "Thêm role", "parent_code": "user_management_menu_roles"},
    {"key": "save_and_close_role", "label": "Lưu & Đóng role", "parent_code": "user_management_menu_roles"}
]

# Lấy id của permission module user_management_view
engine = create_engine(os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/metisdb"))
Session = sessionmaker(bind=engine)
session = Session()
module_perm = session.query(Permission).filter_by(code=f"{MODULE_NAME}_view").first()
parent_module_id = module_perm.id if module_perm else None

# Tạo permission cho các menu
menu_ids = {}
for menu in MENUS:
    payload = {
        "code": f"{MODULE_NAME}_menu_{menu['key']}",
        "name": f"Xem menu {menu['label']}",
        "type": "menu",
        "module_name": MODULE_NAME,
        "parent_id": parent_module_id
    }
    resp = requests.post(API_URL, json=payload)
    print(f"Tạo permission cho menu {menu['key']}: {resp.status_code} - {resp.text}")
    if resp.status_code == 200:
        menu_ids[menu['key']] = resp.json().get('id')
    else:
        # Nếu đã tồn tại, lấy lại id từ DB
        perm = session.query(Permission).filter_by(code=payload['code']).first()
        if perm:
            menu_ids[menu['key']] = perm.id

# Tạo permission cho các button
for btn in BUTTONS:
    # Lấy parent_id là id của menu tương ứng
    parent_id = menu_ids.get(btn['parent_code'].replace('user_management_menu_', ''))
    if not parent_id:
        # fallback: lấy từ DB
        perm = session.query(Permission).filter_by(code=btn['parent_code']).first()
        parent_id = perm.id if perm else None
    payload = {
        "code": f"{MODULE_NAME}_btn_{btn['key']}",
        "name": f"Button {btn['label']}",
        "type": "button",
        "module_name": MODULE_NAME,
        "parent_id": parent_id
    }
    resp = requests.post(API_URL, json=payload)
    print(f"Tạo permission cho button {btn['key']}: {resp.status_code} - {resp.text}")
session.close()
