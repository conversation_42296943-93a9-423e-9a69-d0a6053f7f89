#!/usr/bin/env python3
"""
Script tạo user mẫu để test đăng nhập
"""
import sys
import os
import hashlib
import secrets
import uuid

# Thêm đường dẫn backend vào sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from sqlalchemy.orm import Session
from src.core.database import engine, Base
from src.modules.user_management.models import User, Role, UserRole

def create_sample_user():
    """Tạo user mẫu trong database"""
    
    # Tạo bảng nếu chưa có
    Base.metadata.create_all(bind=engine)
    
    # Tạo session
    db = Session(engine)
    
    try:
        # Xóa user cũ nếu có
        existing_user = db.query(User).filter(User.username == 'admin').first()
        if existing_user:
            # Xóa user roles trước
            db.query(UserRole).filter(UserRole.user_id == existing_user.id).delete()
            db.delete(existing_user)
            db.commit()
            print("Đã xóa user 'admin' cũ")
        
        # Tạo role admin nếu chưa có
        admin_role = db.query(Role).filter(Role.name == 'Admin').first()
        if not admin_role:
            admin_role = Role(
                name='Admin',
                description='Quản trị viên hệ thống',
                is_activated=True
            )
            db.add(admin_role)
            db.commit()
            db.refresh(admin_role)
            print("Đã tạo role 'Admin'")
        
        # Hash password
        password = 'admin123'
        salt = secrets.token_hex(16)
        password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
        
        # Tạo user admin
        admin_user = User(
            username='admin',
            password_hash=f"{salt}:{password_hash}",
            full_name='Administrator',
            email='<EMAIL>',
            phone_number='0123456789',
            is_active=True
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        # Gán role cho user
        user_role = UserRole(
            user_id=admin_user.id,
            role_id=admin_role.id
        )
        db.add(user_role)
        db.commit()
        
        print("Đã tạo user mẫu thành công!")
        print(f"Username: admin")
        print(f"Password: {password}")
        print(f"Full Name: {admin_user.full_name}")
        print(f"Email: {admin_user.email}")
        print(f"Role: {admin_role.name}")
        
    except Exception as e:
        print(f"Lỗi khi tạo user: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_sample_user() 