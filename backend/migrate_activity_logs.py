#!/usr/bin/env python3
# Đường dẫn: backend/migrate_activity_logs.py
"""
Migration script để tạo bảng activity_logs trong database metisdb_logs
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.database_logs import logs_engine, LogsBase
from src.modules.activity_logs.models import ActivityLog

def migrate_activity_logs():
    """Tạo bảng activity_logs trong database logs"""
    print("Bắt đầu migration cho Activity Logs module trong database metisdb_logs...")
    
    try:
        # Tạo bảng activity_logs trong database logs
        ActivityLog.__table__.create(logs_engine, checkfirst=True)
        print("✓ Đã tạo bảng activity_logs trong database metisdb_logs thành công")
        
        # Kiểm tra xem bảng đã tồn tại chưa
        from sqlalchemy import inspect
        inspector = inspect(logs_engine)
        
        if 'activity_logs' in inspector.get_table_names():
            print("✓ Bảng activity_logs đã được tạo và sẵn sàng sử dụng")
            
            # Hiển thị thông tin cột
            columns = inspector.get_columns('activity_logs')
            print(f"✓ Bảng có {len(columns)} cột:")
            for column in columns:
                print(f"  - {column['name']}: {column['type']}")
        else:
            print("✗ Không thể tạo bảng activity_logs")
            return False
            
        print("✓ Migration Activity Logs module hoàn thành thành công!")
        print("✓ Database: metisdb_logs")
        print("✓ Bảng: activity_logs")
        return True
        
    except Exception as e:
        print(f"✗ Lỗi trong quá trình migration: {e}")
        return False

if __name__ == "__main__":
    migrate_activity_logs() 