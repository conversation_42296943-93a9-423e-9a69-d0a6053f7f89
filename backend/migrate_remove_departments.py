#!/usr/bin/env python3
"""
Script để xóa bảng departments và cột department_id khỏi bảng users
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.database import SessionLocal, engine
from sqlalchemy import text

def remove_departments():
    """Xóa bảng departments và cột department_id khỏi bảng users"""
    
    print("🔄 Bắt đầu xóa Departments khỏi User Management module...")
    
    db = SessionLocal()
    try:
        # Kiểm tra bảng departments có tồn tại không
        inspector = engine.dialect.inspector(engine)
        tables = inspector.get_table_names()
        
        if 'departments' in tables:
            print("📊 Đang xóa bảng departments...")
            
            # Xóa foreign key constraint trước
            print("🔗 Đang xóa foreign key constraints...")
            
            # Tìm và xóa foreign key từ bảng users đến departments
            user_columns = inspector.get_columns('users')
            for col in user_columns:
                if col['name'] == 'department_id':
                    # Xóa foreign key constraint
                    db.execute(text("ALTER TABLE users DROP CONSTRAINT IF EXISTS users_department_id_fkey"))
                    print("✅ Đã xóa foreign key constraint users_department_id_fkey")
                    break
            
            # Xóa cột department_id khỏi bảng users
            print("🗑️  Đang xóa cột department_id khỏi bảng users...")
            db.execute(text("ALTER TABLE users DROP COLUMN IF EXISTS department_id"))
            print("✅ Đã xóa cột department_id khỏi bảng users")
            
            # Xóa bảng departments
            print("🗑️  Đang xóa bảng departments...")
            db.execute(text("DROP TABLE IF EXISTS departments CASCADE"))
            print("✅ Đã xóa bảng departments")
            
            db.commit()
            print("🎉 Xóa Departments thành công!")
            
            # Hiển thị cấu trúc bảng users sau khi xóa
            print("\n📋 Cấu trúc bảng users sau khi xóa:")
            user_columns = inspector.get_columns('users')
            for col in user_columns:
                print(f"  - {col['name']}: {col['type']}")
                
        else:
            print("ℹ️  Bảng departments không tồn tại, không cần xóa")
            
            # Kiểm tra và xóa cột department_id nếu còn tồn tại
            user_columns = inspector.get_columns('users')
            has_department_id = any(col['name'] == 'department_id' for col in user_columns)
            
            if has_department_id:
                print("🗑️  Đang xóa cột department_id khỏi bảng users...")
                db.execute(text("ALTER TABLE users DROP COLUMN IF EXISTS department_id"))
                db.commit()
                print("✅ Đã xóa cột department_id khỏi bảng users")
            else:
                print("ℹ️  Cột department_id không tồn tại trong bảng users")
        
        # Hiển thị danh sách bảng hiện tại
        print("\n📋 Danh sách bảng hiện tại:")
        tables = inspector.get_table_names()
        for table in tables:
            print(f"  - {table}")
        
    except Exception as e:
        db.rollback()
        print(f"❌ Lỗi khi xóa Departments: {e}")
        return False
    finally:
        db.close()
    
    return True

if __name__ == "__main__":
    success = remove_departments()
    if success:
        print("\n🎉 Migration hoàn thành thành công!")
        sys.exit(0)
    else:
        print("\n💥 Migration thất bại!")
        sys.exit(1) 