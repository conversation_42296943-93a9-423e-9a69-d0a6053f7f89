#!/usr/bin/env python3
"""
Migration script để tạo bảng Languages và Translations cho module multi_languages
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.database import engine, Base
from src.modules.multi_languages.models import Language, Translation

def create_multi_languages_tables():
    """Tạo bảng Languages và Translations cho module multi_languages"""
    try:
        print("Đang tạo bảng Languages và Translations...")
        # Tạo bảng Languages và Translations
        Language.__table__.create(engine, checkfirst=True)
        Translation.__table__.create(engine, checkfirst=True)
        print("✅ Bảng Languages và Translations đã được tạo thành công!")

        # Thêm dữ liệu mẫu
        from sqlalchemy.orm import sessionmaker
        Session = sessionmaker(bind=engine)
        session = Session()

        # Kiểm tra xem đã có dữ liệu chưa
        existing_languages = session.query(Language).count()
        if existing_languages == 0:
            print("<PERSON>ang thêm dữ liệu ngôn ngữ mặc định...")
            vi = Language(code='vi', name='Tiếng Việt', is_default=True, is_activated=True)
            en = Language(code='en', name='English', is_default=False, is_activated=True)
            session.add_all([vi, en])
            session.commit()
            print("✅ Đã thêm ngôn ngữ Tiếng Việt (mặc định) và English!")
        else:
            print(f"ℹ️  Đã có {existing_languages} bản ghi Languages trong database")
        session.close()
    except Exception as e:
        print(f"❌ Lỗi khi tạo bảng Languages/Translations: {e}")
        return False
    return True

def drop_multi_languages_tables():
    """Xóa bảng Languages và Translations của module multi_languages"""
    try:
        print("Đang xóa bảng Translations và Languages...")
        # Xóa bảng Translations trước do có foreign key
        Translation.__table__.drop(engine, checkfirst=True)
        Language.__table__.drop(engine, checkfirst=True)
        print("✅ Bảng Languages và Translations đã được xóa thành công!")
        return True
    except Exception as e:
        print(f"❌ Lỗi khi xóa bảng Languages/Translations: {e}")
        return False

def seed_sample_translations():
    """Thêm dữ liệu bản dịch mẫu cho các ngôn ngữ"""
    try:
        from sqlalchemy.orm import sessionmaker
        Session = sessionmaker(bind=engine)
        session = Session()
        # Lấy id các ngôn ngữ
        vi = session.query(Language).filter_by(code='vi').first()
        en = session.query(Language).filter_by(code='en').first()
        if not vi or not en:
            print("❌ Chưa có dữ liệu ngôn ngữ. Hãy chạy 'create' trước.")
            session.close()
            return False
        # Kiểm tra đã có bản dịch chưa
        existing = session.query(Translation).count()
        if existing > 0:
            print(f"ℹ️  Đã có {existing} bản ghi Translations trong database")
            session.close()
            return True
        # Dữ liệu mẫu
        translations = [
            # Vietnamese
            Translation(language_id=vi.id, key='greeting.hello', value='Xin chào'),
            Translation(language_id=vi.id, key='greeting.goodbye', value='Tạm biệt'),
            Translation(language_id=vi.id, key='app.title', value='Hệ thống Metis'),
            # English
            Translation(language_id=en.id, key='greeting.hello', value='Hello'),
            Translation(language_id=en.id, key='greeting.goodbye', value='Goodbye'),
            Translation(language_id=en.id, key='app.title', value='Metis System'),
        ]
        session.add_all(translations)
        session.commit()
        print("✅ Đã thêm dữ liệu bản dịch mẫu cho Tiếng Việt và English!")
        session.close()
        return True
    except Exception as e:
        print(f"❌ Lỗi khi seed dữ liệu bản dịch: {e}")
        return False

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='Migration script cho module multi_languages')
    parser.add_argument('action', choices=['create', 'drop', 'seed'], help='Hành động thực hiện')
    args = parser.parse_args()
    if args.action == 'create':
        success = create_multi_languages_tables()
        if success:
            print("\n🎉 Migration thành công!")
            sys.exit(0)
        else:
            print("\n💥 Migration thất bại!")
            sys.exit(1)
    elif args.action == 'drop':
        success = drop_multi_languages_tables()
        if success:
            print("\n🎉 Xóa bảng thành công!")
            sys.exit(0)
        else:
            print("\n💥 Xóa bảng thất bại!")
            sys.exit(1)
    elif args.action == 'seed':
        success = seed_sample_translations()
        if success:
            print("\n🎉 Seed dữ liệu bản dịch thành công!")
            sys.exit(0)
        else:
            print("\n💥 Seed dữ liệu bản dịch thất bại!")
            sys.exit(1) 