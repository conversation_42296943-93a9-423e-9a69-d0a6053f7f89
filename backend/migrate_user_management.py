#!/usr/bin/env python3
"""
Migration script để tạo dữ liệu mẫu cho User Management Module
"""

import sys
import os
import uuid

# Thêm đường dẫn src vào sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.database import SessionLocal, engine
from src.modules.user_management.models import User, Role, UserRole
from src.modules.base.models import IrModule

def create_sample_data():
    """Tạo dữ liệu mẫu cho User Management"""
    db = SessionLocal()
    
    try:
        print("INFO: Bắt đầu tạo dữ liệu mẫu cho User Management...")
        
        # Tạo Roles
        roles_data = [
            {
                'name': 'Administrator',
                'description': 'Quản trị viên hệ thống với tất cả quyền hạn'
            },
            {
                'name': 'Manager',
                'description': 'Quản lý với quyền hạn quản lý users và sites'
            },
            {
                'name': 'User',
                'description': 'Người dùng thông thường với quyền hạn cơ bản'
            },
            {
                'name': 'Guest',
                'description': 'Khách với quyền hạn xem hạn chế'
            }
        ]
        
        created_roles = {}
        for role_data in roles_data:
            existing = db.query(Role).filter(Role.name == role_data['name']).first()
            if not existing:
                new_role = Role(**role_data)
                db.add(new_role)
                created_roles[role_data['name']] = new_role
                print(f"INFO: Đã tạo role: {role_data['name']}")
            else:
                created_roles[role_data['name']] = existing
                print(f"INFO: Role {role_data['name']} đã tồn tại")
        
        # Commit roles trước
        db.commit()
        
        # Refresh roles để lấy ID
        for role_name, role in created_roles.items():
            if not role.id:  # Nếu role mới được tạo
                db.refresh(role)
        
        # Tạo Users
        users_data = [
            {
                'username': 'admin',
                'full_name': 'Administrator',
                'email': '<EMAIL>',
                'phone_number': '0123456789',
                'is_active': True,
                'password_hash': 'admin123'  # Trong thực tế cần hash password
            },
            {
                'username': 'manager1',
                'full_name': 'Nguyễn Văn Manager',
                'email': '<EMAIL>',
                'phone_number': '0987654321',
                'is_active': True,
                'password_hash': 'manager123'
            },
            {
                'username': 'user1',
                'full_name': 'Trần Thị User',
                'email': '<EMAIL>',
                'phone_number': '0123456780',
                'is_active': True,
                'password_hash': 'user123'
            },
            {
                'username': 'user2',
                'full_name': 'Lê Văn Test',
                'email': '<EMAIL>',
                'phone_number': '0987654320',
                'is_active': False,
                'password_hash': 'user123'
            },
            {
                'username': 'guest1',
                'full_name': 'Phạm Thị Guest',
                'email': '<EMAIL>',
                'phone_number': '0123456781',
                'is_active': True,
                'password_hash': 'guest123'
            }
        ]
        
        created_users = {}
        for user_data in users_data:
            existing = db.query(User).filter(User.username == user_data['username']).first()
            if not existing:
                new_user = User(**user_data)
                db.add(new_user)
                created_users[user_data['username']] = new_user
                print(f"INFO: Đã tạo user: {user_data['username']}")
            else:
                created_users[user_data['username']] = existing
                print(f"INFO: User {user_data['username']} đã tồn tại")
        
        # Commit users
        db.commit()
        
        # Refresh users để lấy ID
        for username, user in created_users.items():
            if not user.id:  # Nếu user mới được tạo
                db.refresh(user)
        
        # Tạo User Roles
        user_roles_data = [
            ('admin', 'Administrator'),
            ('manager1', 'Manager'),
            ('user1', 'User'),
            ('user2', 'User'),
            ('guest1', 'Guest')
        ]
        
        for username, role_name in user_roles_data:
            user = created_users.get(username)
            role = created_roles.get(role_name)
            if not user:
                print(f"WARNING: Không tìm thấy user {username} trong created_users")
                continue
            if not role:
                print(f"WARNING: Không tìm thấy role {role_name} trong created_roles")
                continue
            # Kiểm tra xem user role đã tồn tại chưa
            existing_user_role = db.query(UserRole).filter(
                UserRole.user_id == user.id,
                UserRole.role_id == role.id
            ).first()
            if not existing_user_role:
                user_role = UserRole(user_id=user.id, role_id=role.id)
                db.add(user_role)
                print(f"INFO: Đã gán role {role_name} cho user {username}")
            else:
                print(f"INFO: User {username} đã có role {role_name}")
        
        # Commit user roles
        db.commit()
        print("INFO: Hoàn thành tạo dữ liệu mẫu cho User Management!")
        
    except Exception as e:
        print(f"ERROR: Lỗi khi tạo dữ liệu mẫu: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def update_user_management_module():
    """Cập nhật thông tin module User Management"""
    db = SessionLocal()
    
    try:
        print("INFO: Cập nhật thông tin module User Management...")
        
        module = db.query(IrModule).filter(IrModule.name == 'user_management').first()
        if module:
            module.state = 'installed'
            module.is_installed = True
            db.commit()
            print("INFO: Đã cập nhật trạng thái module User Management thành 'installed'")
        else:
            print("WARNING: Không tìm thấy module User Management trong database")
            
    except Exception as e:
        print(f"ERROR: Lỗi khi cập nhật module: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    print("INFO: Bắt đầu migration cho User Management Module...")
    
    try:
        # Tạo dữ liệu mẫu
        create_sample_data()
        
        # Cập nhật module
        update_user_management_module()
        
        print("INFO: Migration hoàn thành thành công!")
        
    except Exception as e:
        print(f"ERROR: Migration thất bại: {e}")
        sys.exit(1) 