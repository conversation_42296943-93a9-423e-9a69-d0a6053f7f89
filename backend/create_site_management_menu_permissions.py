# Script tự động sinh permission "View" cho các menu trong module site_management
import requests
import os

API_URL = os.getenv("PERMISSION_API_URL", "http://localhost:8000/api/user-management/permissions")
MODULE_NAME = "site_management"
MENU_INFOS = [
    {"key": "overview", "label": "Tổng quan"},
    {"key": "list", "label": "Danh sách Site"},
    {"key": "add", "label": "Thêm Site"}
]

# Lấy id của permission module site_management_view
resp = requests.get(f"http://localhost:8000/api/user-management/users/00000000-0000-0000-0000-000000000000/permissions")
# Dùng API phù hợp để lấy permission, ở đây sẽ thử lấy trực tiếp từ DB nếu cần

# Tạm thời dùng API liệt kê tất cả permission (nếu có), hoặc bạn có thể thay bằng truy vấn DB nếu cần
try:
    from sqlalchemy import create_engine
    from sqlalchemy.orm import sessionmaker
    from src.core.database import Base, Permission
    engine = create_engine(os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/metisdb"))
    Session = sessionmaker(bind=engine)
    session = Session()
    module_perm = session.query(Permission).filter_by(code=f"{MODULE_NAME}_view").first()
    parent_id = module_perm.id if module_perm else None
    session.close()
except Exception:
    parent_id = 5  # fallback nếu biết chắc id là 5

for menu in MENU_INFOS:
    payload = {
        "code": f"{MODULE_NAME}_menu_{menu['key']}",
        "name": f"Xem menu {menu['label']}",
        "type": "menu",
        "module_name": MODULE_NAME,
        "parent_id": parent_id
    }
    resp = requests.post(API_URL, json=payload)
    print(f"Tạo permission cho menu {menu['key']}: {resp.status_code} - {resp.text}")
