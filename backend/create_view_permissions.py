# Script tự động sinh permission 'View' cho các module hiện có
import requests
import os

API_URL = os.getenv("PERMISSION_API_URL", "http://localhost:8000/api/user-management/permissions")

MODULES = [
    "activity_logs",
    "base",
    "multi_languages",
    "sample_development",
    "site_management",
    "user_management"
]

for module in MODULES:
    payload = {
        "code": f"{module}_view",
        "name": f"Xem {module.replace('_', ' ').title()}",
        "type": "module",
        "module_name": module,
        "parent_id": None
    }
    resp = requests.post(API_URL, json=payload)
    print(f"Tạo permission cho module {module}: {resp.status_code} - {resp.text}")
