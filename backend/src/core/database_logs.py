# Đường dẫn: backend/src/core/database_logs.py
import os
import psycopg2
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base

# Chuỗi kết n<PERSON>i đến PostgreSQL database cho logs
LOGS_DATABASE_URL = os.getenv(
    "LOGS_DATABASE_URL", 
    "postgresql://postgres:postgres@localhost:5432/metisdb_logs"
)

# Tạo engine cho database logs
logs_engine = create_engine(LOGS_DATABASE_URL)

# Tạo session class cho database logs
LogsSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=logs_engine)

# Tạo Base class cho database logs
LogsBase = declarative_base()

def get_logs_db():
    """Dependency function để inject logs database session vào FastAPI endpoints"""
    db = LogsSessionLocal()
    try:
        yield db
    finally:
        db.close()

def get_logs_db_connection():
    """Tạo kết nối PostgreSQL trực tiếp đến database logs"""
    try:
        # Parse connection string
        if LOGS_DATABASE_URL.startswith('postgresql://'):
            # Format: postgresql://username:password@host:port/database
            parts = LOGS_DATABASE_URL.replace('postgresql://', '').split('@')
            user_pass = parts[0].split(':')
            host_db = parts[1].split('/')
            host_port = host_db[0].split(':')
            
            username = user_pass[0]
            password = user_pass[1]
            host = host_port[0]
            port = host_port[1] if len(host_port) > 1 else '5432'
            database = host_db[1]
            
            return psycopg2.connect(
                host=host,
                port=port,
                database=database,
                user=username,
                password=password
            )
        else:
            # Fallback to direct connection
            return psycopg2.connect(
                host='localhost',
                port='5432',
                database='metisdb_logs',
                user='postgres',
                password='postgres'
            )
    except Exception as e:
        print(f"Error connecting to logs database: {e}")
        raise 