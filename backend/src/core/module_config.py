# Đường dẫn: backend/src/core/module_config.py
# Cấu hình quản lý module

# Danh sách module mặc định sẽ được cài đặt tự động khi khởi động server
DEFAULT_INSTALL_MODULES = [
    'base',           # Module lõi luôn được cài đặt
    'user_management', # Module quản lý người dùng cũng được cài đặt mặc định
    'site_management', # Module quản lý Site cũng được cài đặt mặc định
    'multi_languages', # Module quản lý đa ngôn ngữ cũng được cài đặt mặc định
    'activity_logs'   # Module quản lý activity logs cũng được cài đặt mặc định
]

# Danh sách module sẽ được ẩn khỏi AppsPage (không hiển thị cho người dùng)
HIDDEN_MODULES = [
    # Không có module nào bị ẩn mặc định
]

# C<PERSON><PERSON> hình chi tiết cho từng module
MODULE_CONFIGS = {
    'base': {
        'is_core_module': True,    # Core Module - hiển thị trong tab Module hệ thống
        'auto_install': True,      # Tự động cài đặt khi khởi động
        'hidden': False,           # Hiển thị trên AppsPage
        'description': 'Module lõi chứa các thành phần cơ bản của Metis.',
        'category': 'Core',
        'author': 'Metis AI',
        'version': '1.0'
    },
    'user_management': {
        'is_core_module': True,    # Core Module - hiển thị trong tab Module hệ thống
        'auto_install': True,      # Tự động cài đặt khi khởi động
        'hidden': False,           # Hiển thị trên AppsPage
        'description': 'Hệ thống quản lý người dùng và vai trò với phân quyền chi tiết',
        'category': 'Core',
        'author': 'Metis AI',
        'version': '1.0'
    },
    'site_management': {
        'is_core_module': True,    # Core Module - hiển thị trong tab Module hệ thống
        'auto_install': True,      # Tự động cài đặt khi khởi động
        'hidden': False,           # Hiển thị trên AppsPage
        'description': 'Module quản lý thông tin và khai báo Site.',
        'category': 'Core',
        'author': 'Metis AI',
        'version': '1.0'
    },
    'multi_languages': {
        'is_core_module': True,    # Core Module - hiển thị trong tab Module hệ thống
        'auto_install': True,      # Tự động cài đặt khi khởi động
        'hidden': False,           # Hiển thị trên AppsPage
        'description': 'Module quản lý và cấu hình đa ngôn ngữ cho hệ thống.',
        'category': 'Core',
        'author': 'Metis AI',
        'version': '1.0'
    },
    'activity_logs': {
        'is_core_module': True,    # Core Module - hiển thị trong tab Module hệ thống
        'auto_install': True,      # Tự động cài đặt khi khởi động
        'hidden': False,           # Hiển thị trên AppsPage
        'description': 'Module quản lý toàn bộ thông tin log hệ thống.',
        'category': 'Core',
        'author': 'Metis AI',
        'version': '1.0'
    },
    'sample_development': {
        'is_core_module': False,   # Module phát triển - hiển thị trong tab Module phát triển
        'auto_install': False,     # Không tự động cài đặt
        'hidden': False,           # Hiển thị trên AppsPage
        'description': 'Module phát triển mẫu để test giao diện',
        'category': 'Development',
        'author': 'Metis AI',
        'version': '1.0'
    }
}

def get_auto_install_modules():
    """Lấy danh sách module cần tự động cài đặt"""
    return DEFAULT_INSTALL_MODULES

def get_hidden_modules():
    """Lấy danh sách module cần ẩn khỏi AppsPage"""
    return HIDDEN_MODULES

def is_module_hidden(module_name: str) -> bool:
    """Kiểm tra module có bị ẩn không"""
    return module_name in HIDDEN_MODULES

def get_module_config(module_name: str) -> dict:
    """Lấy cấu hình chi tiết của module"""
    return MODULE_CONFIGS.get(module_name, {
        'is_core_module': False,   # Mặc định là Module phát triển
        'auto_install': False,     # Không tự động cài đặt
        'hidden': False,           # Hiển thị trên AppsPage
        'description': f'Module {module_name}',
        'category': 'Other',
        'author': 'Metis Team',
        'version': '1.0'
    })

def should_auto_install(module_name: str) -> bool:
    """Kiểm tra module có cần tự động cài đặt không"""
    config = get_module_config(module_name)
    return config.get('auto_install', False)

def is_core_module(module_name: str) -> bool:
    """Kiểm tra module có phải là Core module không"""
    config = get_module_config(module_name)
    return config.get('is_core_module', False)

def can_uninstall_module(module_name: str) -> bool:
    """Kiểm tra module có thể gỡ cài đặt không (Core module không thể gỡ)"""
    return not is_core_module(module_name)

def get_system_modules():
    """Lấy danh sách module hệ thống (Core modules không bị ẩn)"""
    system_modules = []
    for module_name, config in MODULE_CONFIGS.items():
        if config.get('is_core_module', False) and not config.get('hidden', False):
            system_modules.append(module_name)
    return system_modules

def get_development_modules():
    """Lấy danh sách module phát triển (không phải Core modules và không bị ẩn)"""
    development_modules = []
    for module_name, config in MODULE_CONFIGS.items():
        if not config.get('is_core_module', False) and not config.get('hidden', False):
            development_modules.append(module_name)
    return development_modules 