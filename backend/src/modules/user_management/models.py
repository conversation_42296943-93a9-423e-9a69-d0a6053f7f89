# Đường dẫn: backend/src/modules/user_management/models.py
import uuid
from sqlalchemy import Column, String, Boolean, INT, ForeignKey, Enum, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, backref
import enum
from ...core.database import Base # Import Base từ thư mục core

class Role(Base):
    __tablename__ = "roles"
    id = Column(INT, primary_key=True)
    name = Column(String(100), nullable=False, unique=True)
    description = Column(String(500))
    is_activated = Column(Boolean, default=True)

class User(Base):
    __tablename__ = "users"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(100), unique=True, index=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    full_name = Column(String(150), nullable=False)
    email = Column(String(150), unique=True, index=True, nullable=False)
    phone_number = Column(String(20), nullable=True)
    is_active = Column(Boolean, default=True)

class UserRole(Base):
    __tablename__ = "user_roles"
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), primary_key=True)
    role_id = Column(INT, ForeignKey('roles.id'), primary_key=True)

class PermissionType(enum.Enum):
    module = "module"
    menu = "menu"
    button = "button"

class Permission(Base):
    __tablename__ = "permissions"
    id = Column(Integer, primary_key=True)
    code = Column(String(100), unique=True, nullable=False)  # Mã quyền duy nhất
    name = Column(String(255), nullable=False)  # Tên quyền
    type = Column(Enum(PermissionType), nullable=False)  # module/menu/button
    module_name = Column(String(100), nullable=True)  # Tên module liên quan
    parent_id = Column(Integer, ForeignKey('permissions.id'), nullable=True)  # Cho phép phân cấp menu/button
    parent = relationship('Permission', remote_side=[id], backref=backref('children', lazy='dynamic'))

class RolePermission(Base):
    __tablename__ = "role_permissions"
    role_id = Column(INT, ForeignKey('roles.id'), primary_key=True)
    permission_id = Column(Integer, ForeignKey('permissions.id'), primary_key=True)
    permission = relationship('Permission', backref=backref('role_permissions', lazy='dynamic'))
    role = relationship('Role', backref=backref('role_permissions', lazy='dynamic'))