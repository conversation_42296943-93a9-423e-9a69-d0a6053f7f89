# Đường dẫn: backend/src/modules/user_management/router.py
from fastapi import APIRouter, HTTPException, Depends, Request
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel
import uuid
import hashlib
import secrets
import logging

from ...core.database import get_db
from .models import User, Role, UserRole, Permission, RolePermission, PermissionType

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/user-management", tags=["User Management"])

# Pydantic models cho request/response
class UserBase(BaseModel):
    username: str
    full_name: str
    email: str
    phone_number: Optional[str] = None
    is_active: bool = True

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    username: Optional[str] = None
    full_name: Optional[str] = None
    email: Optional[str] = None
    phone_number: Optional[str] = None
    is_active: Optional[bool] = None

class UserResponse(UserBase):
    id: uuid.UUID
    
    class Config:
        from_attributes = True

class RoleBase(BaseModel):
    name: str
    description: Optional[str] = None
    is_activated: bool = True

class RoleCreate(RoleBase):
    pass

class RoleUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    is_activated: Optional[bool] = None

class RoleResponse(RoleBase):
    id: int
    
    class Config:
        from_attributes = True

class UserRoleResponse(BaseModel):
    user_id: uuid.UUID
    role_id: int
    username: str
    full_name: str
    role_name: str
    
    class Config:
        from_attributes = True

class UserRoleCreate(BaseModel):
    user_id: uuid.UUID
    role_id: int

class PermissionBase(BaseModel):
    code: str
    name: str
    type: PermissionType
    module_name: Optional[str] = None
    parent_id: Optional[int] = None

class PermissionCreate(PermissionBase):
    pass

class PermissionResponse(PermissionBase):
    id: int
    class Config:
        from_attributes = True

class RolePermissionCreate(BaseModel):
    role_id: int
    permission_ids: List[int]

# User endpoints
@router.get("/users", response_model=List[UserResponse])
def get_users(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Lấy danh sách tất cả Users"""
    users = db.query(User).offset(skip).limit(limit).all()
    return users

@router.get("/users/{user_id}", response_model=UserResponse)
def get_user(user_id: uuid.UUID, db: Session = Depends(get_db)):
    """Lấy thông tin User theo ID"""
    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise HTTPException(status_code=404, detail="User không tìm thấy")
    return user

@router.post("/users", response_model=UserResponse)
def create_user(user: UserCreate, db: Session = Depends(get_db)):
    """Tạo User mới"""
    # Kiểm tra username đã tồn tại chưa
    existing_user = db.query(User).filter(User.username == user.username).first()
    if existing_user:
        raise HTTPException(status_code=400, detail="Username đã tồn tại")
    
    # Kiểm tra email đã tồn tại chưa (nếu có email)
    if user.email:
        existing_email = db.query(User).filter(User.email == user.email).first()
        if existing_email:
            raise HTTPException(status_code=400, detail="Email đã tồn tại")
    
    # Hash password
    salt = secrets.token_hex(16)
    password_hash = hashlib.sha256((user.password + salt).encode()).hexdigest()
    
    # Tạo user mới
    user_data = user.dict()
    user_data.pop('password')  # Xóa password gốc
    user_data['password_hash'] = f"{salt}:{password_hash}"  # Lưu salt và hash
    
    db_user = User(**user_data)
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

@router.put("/users/{user_id}", response_model=UserResponse)
def update_user(user_id: uuid.UUID, user_update: UserUpdate, db: Session = Depends(get_db)):
    """Cập nhật thông tin User"""
    db_user = db.query(User).filter(User.id == user_id).first()
    if db_user is None:
        raise HTTPException(status_code=404, detail="User không tìm thấy")
    
    # Kiểm tra username mới có trùng với User khác không
    if user_update.username and user_update.username != db_user.username:
        existing_user = db.query(User).filter(User.username == user_update.username).first()
        if existing_user:
            raise HTTPException(status_code=400, detail="Username đã tồn tại")
    
    # Kiểm tra email mới có trùng với User khác không
    if user_update.email and user_update.email != db_user.email:
        existing_email = db.query(User).filter(User.email == user_update.email).first()
        if existing_email:
            raise HTTPException(status_code=400, detail="Email đã tồn tại")
    
    # Cập nhật các trường
    update_data = user_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_user, field, value)
    
    db.commit()
    db.refresh(db_user)
    return db_user

@router.delete("/users/{user_id}")
def delete_user(user_id: uuid.UUID, db: Session = Depends(get_db)):
    """Xóa User"""
    db_user = db.query(User).filter(User.id == user_id).first()
    if db_user is None:
        raise HTTPException(status_code=404, detail="User không tìm thấy")
    
    # Xóa các user roles trước
    db.query(UserRole).filter(UserRole.user_id == user_id).delete()
    
    db.delete(db_user)
    db.commit()
    return {"message": "User đã được xóa thành công"}

@router.put("/users/{user_id}/toggle-active")
def toggle_user_active(user_id: uuid.UUID, db: Session = Depends(get_db)):
    """Chuyển đổi trạng thái active của User"""
    db_user = db.query(User).filter(User.id == user_id).first()
    if db_user is None:
        raise HTTPException(status_code=404, detail="User không tìm thấy")
    
    db_user.is_active = not db_user.is_active
    db.commit()
    db.refresh(db_user)
    
    return {
        "message": f"User đã được {'kích hoạt' if db_user.is_active else 'vô hiệu hóa'}",
        "is_active": db_user.is_active
    }

@router.get("/users/check-username/{username}")
def check_username(username: str, db: Session = Depends(get_db)):
    """Kiểm tra username có tồn tại không"""
    existing_user = db.query(User).filter(User.username == username).first()
    return {
        "username": username,
        "exists": existing_user is not None,
        "available": existing_user is None
    }

@router.get("/users/check-email/{email}")
def check_email(email: str, db: Session = Depends(get_db)):
    """Kiểm tra email có tồn tại không"""
    existing_user = db.query(User).filter(User.email == email).first()
    return {
        "email": email,
        "exists": existing_user is not None,
        "available": existing_user is None
    }

@router.get("/roles/check-name/{name}")
def check_role_name(name: str, db: Session = Depends(get_db)):
    """Kiểm tra role name có tồn tại không"""
    existing_role = db.query(Role).filter(Role.name == name).first()
    return {
        "name": name,
        "exists": existing_role is not None,
        "available": existing_role is None
    }

# Role endpoints
@router.get("/roles", response_model=List[RoleResponse])
def get_roles(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Lấy danh sách tất cả Roles"""
    roles = db.query(Role).offset(skip).limit(limit).all()
    return roles

@router.get("/roles/{role_id}", response_model=RoleResponse)
def get_role(role_id: int, db: Session = Depends(get_db)):
    """Lấy thông tin Role theo ID"""
    role = db.query(Role).filter(Role.id == role_id).first()
    if role is None:
        raise HTTPException(status_code=404, detail="Role không tìm thấy")
    return role

@router.post("/roles", response_model=RoleResponse)
def create_role(role: RoleCreate, db: Session = Depends(get_db)):
    """Tạo Role mới"""
    # Kiểm tra name đã tồn tại chưa
    existing_role = db.query(Role).filter(Role.name == role.name).first()
    if existing_role:
        raise HTTPException(status_code=400, detail="Role name đã tồn tại")
    
    db_role = Role(**role.dict())
    db.add(db_role)
    db.commit()
    db.refresh(db_role)
    return db_role

@router.put("/roles/{role_id}", response_model=RoleResponse)
def update_role(role_id: int, role_update: RoleUpdate, db: Session = Depends(get_db)):
    """Cập nhật thông tin Role"""
    db_role = db.query(Role).filter(Role.id == role_id).first()
    if db_role is None:
        raise HTTPException(status_code=404, detail="Role không tìm thấy")
    
    # Kiểm tra name mới có trùng với Role khác không
    if role_update.name and role_update.name != db_role.name:
        existing_role = db.query(Role).filter(Role.name == role_update.name).first()
        if existing_role:
            raise HTTPException(status_code=400, detail="Role name đã tồn tại")
    
    # Cập nhật các trường
    update_data = role_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_role, field, value)
    
    db.commit()
    db.refresh(db_role)
    return db_role

@router.delete("/roles/{role_id}")
def delete_role(role_id: int, db: Session = Depends(get_db)):
    """Xóa Role"""
    db_role = db.query(Role).filter(Role.id == role_id).first()
    if db_role is None:
        raise HTTPException(status_code=404, detail="Role không tìm thấy")
    
    # Xóa các user roles trước
    db.query(UserRole).filter(UserRole.role_id == role_id).delete()
    
    db.delete(db_role)
    db.commit()
    return {"message": "Role đã được xóa thành công"}

@router.put("/roles/{role_id}/toggle-activated")
def toggle_role_activated(role_id: int, db: Session = Depends(get_db)):
    """Chuyển đổi trạng thái activated của Role"""
    db_role = db.query(Role).filter(Role.id == role_id).first()
    if db_role is None:
        raise HTTPException(status_code=404, detail="Role không tìm thấy")
    
    db_role.is_activated = not db_role.is_activated
    db.commit()
    db.refresh(db_role)
    
    return {
        "message": f"Role đã được {'kích hoạt' if db_role.is_activated else 'vô hiệu hóa'}",
        "is_activated": db_role.is_activated
    }

# UserRole endpoints
@router.get("/user-roles", response_model=List[UserRoleResponse])
def get_user_roles(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Lấy danh sách tất cả UserRoles với thông tin User và Role"""
    user_roles = db.query(
        UserRole.user_id,
        UserRole.role_id,
        User.username,
        User.full_name,
        Role.name.label('role_name')
    ).join(
        User, UserRole.user_id == User.id
    ).join(
        Role, UserRole.role_id == Role.id
    ).offset(skip).limit(limit).all()
    
    # Chuyển đổi kết quả thành list of dicts
    result = []
    for ur in user_roles:
        result.append({
            "user_id": ur.user_id,
            "role_id": ur.role_id,
            "username": ur.username,
            "full_name": ur.full_name,
            "role_name": ur.role_name
        })
    
    return result

@router.delete("/user-roles/{user_id}/{role_id}")
def delete_user_role(user_id: uuid.UUID, role_id: int, db: Session = Depends(get_db)):
    """Xóa UserRole"""
    user_role = db.query(UserRole).filter(
        UserRole.user_id == user_id,
        UserRole.role_id == role_id
    ).first()
    
    if user_role is None:
        raise HTTPException(status_code=404, detail="UserRole không tìm thấy")
    
    db.delete(user_role)
    db.commit()
    return {"message": "UserRole đã được xóa thành công"}

@router.post("/user-roles", response_model=UserRoleResponse)
def create_user_role(user_role: UserRoleCreate, db: Session = Depends(get_db)):
    """Tạo UserRole mới"""
    # Kiểm tra user có tồn tại không
    user = db.query(User).filter(User.id == user_role.user_id).first()
    if user is None:
        raise HTTPException(status_code=404, detail="User không tìm thấy")
    
    # Kiểm tra role có tồn tại không
    role = db.query(Role).filter(Role.id == user_role.role_id).first()
    if role is None:
        raise HTTPException(status_code=404, detail="Role không tìm thấy")
    
    # Kiểm tra user role đã tồn tại chưa
    existing_user_role = db.query(UserRole).filter(
        UserRole.user_id == user_role.user_id,
        UserRole.role_id == user_role.role_id
    ).first()
    
    if existing_user_role:
        raise HTTPException(status_code=400, detail="UserRole đã tồn tại")
    
    # Tạo user role mới
    db_user_role = UserRole(
        user_id=user_role.user_id,
        role_id=user_role.role_id
    )
    db.add(db_user_role)
    db.commit()
    db.refresh(db_user_role)
    
    # Trả về response với thông tin user và role
    return {
        "user_id": db_user_role.user_id,
        "role_id": db_user_role.role_id,
        "username": user.username,
        "full_name": user.full_name,
        "role_name": role.name
    }

# Login endpoint
class LoginRequest(BaseModel):
    username: str
    password: str

class LoginResponse(BaseModel):
    success: bool
    message: str
    user: Optional[UserResponse] = None
    token: Optional[str] = None

@router.post("/login", response_model=LoginResponse)
def login(login_data: LoginRequest, db: Session = Depends(get_db)):
    """Đăng nhập user"""
    # Tìm user theo username
    user = db.query(User).filter(User.username == login_data.username).first()
    
    if not user:
        return LoginResponse(
            success=False,
            message="Tên đăng nhập hoặc mật khẩu không đúng"
        )
    
    # Kiểm tra user có active không
    if not user.is_active:
        return LoginResponse(
            success=False,
            message="Tài khoản đã bị vô hiệu hóa"
        )
    
    # Kiểm tra password
    try:
        salt, stored_hash = user.password_hash.split(':')
        input_hash = hashlib.sha256((login_data.password + salt).encode()).hexdigest()
        
        if input_hash == stored_hash:
            # Tạo token đơn giản (trong thực tế nên dùng JWT)
            token = secrets.token_hex(32)
            
            return LoginResponse(
                success=True,
                message="Đăng nhập thành công",
                user=UserResponse(
                    id=user.id,
                    username=user.username,
                    full_name=user.full_name,
                    email=user.email,
                    phone_number=user.phone_number,
                    is_active=user.is_active
                ),
                token=token
            )
        else:
            return LoginResponse(
                success=False,
                message="Tên đăng nhập hoặc mật khẩu không đúng"
            )
    except Exception:
        return LoginResponse(
            success=False,
            message="Tên đăng nhập hoặc mật khẩu không đúng"
        )

@router.post("/logout")
async def logout(request: Request, db: Session = Depends(get_db)):
    """Logout user và ghi log"""
    try:
        print("DEBUG: Logout endpoint called")
        # Lấy thông tin user từ request (nếu có)
        user_id = None
        user_info = None
        
        # Ghi log logout
        from ..activity_logs.service import ActivityLogService
        print("DEBUG: Importing ActivityLogService")
        activity_service = ActivityLogService(db)
        print("DEBUG: ActivityLogService created")
        
        # Lấy IP và user agent
        client_ip = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent", "")
        print(f"DEBUG: Client IP: {client_ip}, User Agent: {user_agent}")
        
        # Tạo log logout
        print("DEBUG: Creating logout log")
        activity_service.create_log(
            action="LOGOUT",
            resource_type="User",
            description="User logged out",
            ip_address=client_ip,
            user_agent=user_agent
        )
        print("DEBUG: Logout log created successfully")
        
        return {"message": "Logged out successfully"}
    except Exception as e:
        print(f"ERROR: Error during logout: {e}")
        import traceback
        traceback.print_exc()
        return {"message": "Logged out successfully"}

@router.post("/permissions", response_model=PermissionResponse)
def create_permission(permission: PermissionCreate, db: Session = Depends(get_db)):
    """Tạo mới một permission"""
    db_permission = Permission(**permission.dict())
    db.add(db_permission)
    db.commit()
    db.refresh(db_permission)
    return db_permission

@router.post("/role-permissions")
def assign_permissions_to_role(data: RolePermissionCreate, db: Session = Depends(get_db)):
    """Gán nhiều permission cho một role (ghi đè)"""
    logger.debug(f"[DEBUG] role-permissions payload: {data}")
    try:
        role_id = int(data.role_id)
        permission_ids = [int(pid) for pid in data.permission_ids if pid is not None]
        logger.debug(f"[DEBUG] Parsed role_id: {role_id}, permission_ids: {permission_ids}")
    except Exception as e:
        logger.error(f"[ERROR] Lỗi ép kiểu dữ liệu đầu vào: {e}")
        raise HTTPException(status_code=422, detail=f"role_id và permission_ids phải là số nguyên: {e}")
    if not permission_ids:
        logger.error("[ERROR] permission_ids rỗng hoặc không hợp lệ!")
        raise HTTPException(status_code=422, detail="permission_ids không được rỗng!")
    # Xóa các quyền cũ của role
    db.query(RolePermission).filter(RolePermission.role_id == role_id).delete()
    # Gán các quyền mới
    for pid in permission_ids:
        db.add(RolePermission(role_id=role_id, permission_id=pid))
    db.commit()
    logger.info(f"[DEBUG] Đã gán {len(permission_ids)} quyền cho role {role_id}")
    return {"message": "Đã gán quyền cho role thành công"}

@router.get("/users/{user_id}/permissions", response_model=List[PermissionResponse])
def get_user_permissions(user_id: uuid.UUID, db: Session = Depends(get_db)):
    """Trả về danh sách permission (module/menu/button) mà user có qua các role"""
    # Lấy tất cả role_id của user
    role_ids = db.query(UserRole.role_id).filter(UserRole.user_id == user_id).all()
    role_ids = [r[0] for r in role_ids]
    if not role_ids:
        return []
    # Lấy tất cả permission của các role này
    permissions = db.query(Permission).join(RolePermission).filter(RolePermission.role_id.in_(role_ids)).all()
    return permissions

@router.get("/roles/{role_id}/permissions", response_model=List[int])
def get_permissions_of_role(role_id: int, db: Session = Depends(get_db)):
    """Trả về danh sách permission_id mà role đã được gán"""
    permission_ids = db.query(RolePermission.permission_id).filter(RolePermission.role_id == role_id).all()
    return [pid[0] for pid in permission_ids]

@router.get("/permissions", response_model=List[PermissionResponse])
def get_all_permissions(db: Session = Depends(get_db)):
    """Trả về toàn bộ permission (dạng list)"""
    return db.query(Permission).all()

@router.get("/permissions/tree")
def get_permissions_tree(db: Session = Depends(get_db)):
    """Trả về permission dạng cây (module > menu > button)"""
    def build_tree(parent_id=None):
        nodes = db.query(Permission).filter(Permission.parent_id == parent_id).all()
        return [
            {
                "id": node.id,
                "code": node.code,
                "name": node.name,
                "type": node.type.value,
                "module_name": node.module_name,
                "children": build_tree(node.id)
            }
            for node in nodes
        ]
    return build_tree(None)