from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Text
from sqlalchemy.orm import relationship
from ...core.database import Base

class Language(Base):
    __tablename__ = "languages"
    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(String(10), nullable=False, unique=True, index=True)  # vi, en, etc.
    name = Column(String(100), nullable=False)  # Tiếng <PERSON>i<PERSON>, English
    is_default = Column(Boolean, default=False)
    is_activated = Column(Boolean, default=True)
    translations = relationship("Translation", back_populates="language")

class Translation(Base):
    __tablename__ = "translations"
    id = Column(Integer, primary_key=True, autoincrement=True)
    language_id = Column(Integer, ForeignKey('languages.id'), nullable=False)
    key = Column(String(255), nullable=False, index=True)  # e.g. 'greeting.hello'
    value = Column(Text, nullable=False)
    language = relationship("Language", back_populates="translations") 