from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel
from ...core.database import get_db
from .models import Language, Translation

router = APIRouter(prefix="/api/multi-languages", tags=["Multi Languages"])

# Pydantic models
class LanguageBase(BaseModel):
    code: str
    name: str
    is_default: bool = False
    is_activated: bool = True

class LanguageCreate(LanguageBase):
    pass

class LanguageUpdate(BaseModel):
    name: Optional[str] = None
    is_default: Optional[bool] = None
    is_activated: Optional[bool] = None

class LanguageResponse(LanguageBase):
    id: int
    class Config:
        from_attributes = True

class TranslationBase(BaseModel):
    language_id: int
    key: str
    value: str

class TranslationCreate(TranslationBase):
    pass

class TranslationUpdate(BaseModel):
    value: Optional[str] = None

class TranslationResponse(TranslationBase):
    id: int
    class Config:
        from_attributes = True

# Language endpoints
@router.get("/languages", response_model=List[LanguageResponse])
def get_languages(db: Session = Depends(get_db)):
    return db.query(Language).all()

@router.post("/languages", response_model=LanguageResponse)
def create_language(lang: LanguageCreate, db: Session = Depends(get_db)):
    if db.query(Language).filter(Language.code == lang.code).first():
        raise HTTPException(status_code=400, detail="Language code already exists")
    if lang.is_default:
        # Only one default language
        db.query(Language).update({Language.is_default: False})
    db_lang = Language(**lang.dict())
    db.add(db_lang)
    db.commit()
    db.refresh(db_lang)
    return db_lang

@router.put("/languages/{lang_id}", response_model=LanguageResponse)
def update_language(lang_id: int, lang_update: LanguageUpdate, db: Session = Depends(get_db)):
    db_lang = db.query(Language).filter(Language.id == lang_id).first()
    if not db_lang:
        raise HTTPException(status_code=404, detail="Language not found")
    if lang_update.is_default:
        db.query(Language).update({Language.is_default: False})
    for field, value in lang_update.dict(exclude_unset=True).items():
        setattr(db_lang, field, value)
    db.commit()
    db.refresh(db_lang)
    return db_lang

@router.delete("/languages/{lang_id}")
def delete_language(lang_id: int, db: Session = Depends(get_db)):
    db_lang = db.query(Language).filter(Language.id == lang_id).first()
    if not db_lang:
        raise HTTPException(status_code=404, detail="Language not found")
    db.delete(db_lang)
    db.commit()
    return {"message": "Language deleted"}

# Translation endpoints
@router.get("/translations", response_model=List[TranslationResponse])
def get_translations(language_id: Optional[int] = None, db: Session = Depends(get_db)):
    query = db.query(Translation)
    if language_id:
        query = query.filter(Translation.language_id == language_id)
    return query.all()

@router.post("/translations", response_model=TranslationResponse)
def create_translation(tr: TranslationCreate, db: Session = Depends(get_db)):
    if db.query(Translation).filter(Translation.language_id == tr.language_id, Translation.key == tr.key).first():
        raise HTTPException(status_code=400, detail="Translation key already exists for this language")
    db_tr = Translation(**tr.dict())
    db.add(db_tr)
    db.commit()
    db.refresh(db_tr)
    return db_tr

@router.put("/translations/{tr_id}", response_model=TranslationResponse)
def update_translation(tr_id: int, tr_update: TranslationUpdate, db: Session = Depends(get_db)):
    db_tr = db.query(Translation).filter(Translation.id == tr_id).first()
    if not db_tr:
        raise HTTPException(status_code=404, detail="Translation not found")
    for field, value in tr_update.dict(exclude_unset=True).items():
        setattr(db_tr, field, value)
    db.commit()
    db.refresh(db_tr)
    return db_tr

@router.delete("/translations/{tr_id}")
def delete_translation(tr_id: int, db: Session = Depends(get_db)):
    db_tr = db.query(Translation).filter(Translation.id == tr_id).first()
    if not db_tr:
        raise HTTPException(status_code=404, detail="Translation not found")
    db.delete(db_tr)
    db.commit()
    return {"message": "Translation deleted"} 