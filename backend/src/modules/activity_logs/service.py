from datetime import datetime
from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from sqlalchemy import desc
from .models import ActivityLog
from ...core.database_logs import get_logs_db
from ...core.database import SessionLocal
from ..user_management.models import User

class ActivityLogService:
    def __init__(self, db: Session):
        self.db = db
    
    def get_user_info(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Lấy thông tin user từ database chính"""
        try:
            # Sử dụng session riêng cho database chính
            main_db = SessionLocal()
            user = main_db.query(User).filter(User.id == user_id).first()
            if user:
                return {
                    "id": str(user.id),
                    "username": user.username,
                    "full_name": user.full_name,
                    "email": user.email
                }
        except Exception as e:
            print(f"Error getting user info: {e}")
        finally:
            if 'main_db' in locals():
                main_db.close()
        return None
    
    def create_log(
        self,
        action: str,
        resource_type: str,
        user_id: Optional[str] = None,
        resource_id: Optional[str] = None,
        resource_name: Optional[str] = None,
        description: Optional[str] = None,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> ActivityLog:
        """Tạo một activity log mới"""
        # Lấy thông tin user nếu có user_id
        user_info = None
        if user_id:
            user_info = self.get_user_info(user_id)
        
        activity_log = ActivityLog(
            user_id=user_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            resource_name=resource_name,
            description=description,
            old_values=old_values,
            new_values=new_values,
            ip_address=ip_address,
            user_agent=user_agent,
            user_info=user_info,
            created_at=datetime.utcnow()
        )
        
        self.db.add(activity_log)
        self.db.commit()
        self.db.refresh(activity_log)
        return activity_log
    
    def get_logs(
        self,
        skip: int = 0,
        limit: int = 100,
        user_id: Optional[str] = None,
        action: Optional[str] = None,
        resource_type: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[ActivityLog]:
        """Lấy danh sách activity logs với filter"""
        query = self.db.query(ActivityLog)
        
        if user_id:
            query = query.filter(ActivityLog.user_id == user_id)
        
        if action:
            query = query.filter(ActivityLog.action == action)
        
        if resource_type:
            query = query.filter(ActivityLog.resource_type == resource_type)
        
        if start_date:
            query = query.filter(ActivityLog.created_at >= start_date)
        
        if end_date:
            query = query.filter(ActivityLog.created_at <= end_date)
        
        return query.order_by(desc(ActivityLog.created_at)).offset(skip).limit(limit).all()
    
    def get_log_by_id(self, log_id: str) -> Optional[ActivityLog]:
        """Lấy activity log theo ID"""
        return self.db.query(ActivityLog).filter(ActivityLog.id == log_id).first()
    
    def get_user_activity(self, user_id: str, skip: int = 0, limit: int = 50) -> List[ActivityLog]:
        """Lấy hoạt động của một user cụ thể"""
        return self.db.query(ActivityLog).filter(
            ActivityLog.user_id == user_id
        ).order_by(desc(ActivityLog.created_at)).offset(skip).limit(limit).all()
    
    def get_resource_activity(
        self,
        resource_type: str,
        resource_id: str,
        skip: int = 0,
        limit: int = 50
    ) -> List[ActivityLog]:
        """Lấy hoạt động của một resource cụ thể"""
        return self.db.query(ActivityLog).filter(
            ActivityLog.resource_type == resource_type,
            ActivityLog.resource_id == resource_id
        ).order_by(desc(ActivityLog.created_at)).offset(skip).limit(limit).all()
    
    def delete_log(self, log_id: str) -> bool:
        """Xóa một activity log"""
        log = self.get_log_by_id(log_id)
        if log:
            self.db.delete(log)
            self.db.commit()
            return True
        return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """Lấy thống kê về activity logs"""
        total_logs = self.db.query(ActivityLog).count()
        
        # Thống kê theo action
        action_stats = self.db.query(
            ActivityLog.action,
            self.db.func.count(ActivityLog.id).label('count')
        ).group_by(ActivityLog.action).all()
        
        # Thống kê theo resource type
        resource_stats = self.db.query(
            ActivityLog.resource_type,
            self.db.func.count(ActivityLog.id).label('count')
        ).group_by(ActivityLog.resource_type).all()
        
        # Thống kê theo ngày (7 ngày gần nhất)
        from datetime import timedelta
        seven_days_ago = datetime.utcnow() - timedelta(days=7)
        daily_stats = self.db.query(
            self.db.func.date(ActivityLog.created_at).label('date'),
            self.db.func.count(ActivityLog.id).label('count')
        ).filter(ActivityLog.created_at >= seven_days_ago).group_by(
            self.db.func.date(ActivityLog.created_at)
        ).order_by(self.db.func.date(ActivityLog.created_at)).all()
        
        return {
            "total_logs": total_logs,
            "action_statistics": [{"action": stat.action, "count": stat.count} for stat in action_stats],
            "resource_statistics": [{"resource_type": stat.resource_type, "count": stat.count} for stat in resource_stats],
            "daily_statistics": [{"date": str(stat.date), "count": stat.count} for stat in daily_stats]
        } 