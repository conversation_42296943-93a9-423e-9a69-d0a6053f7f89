import uuid
from datetime import datetime
from sqlalchemy import Column, String, DateTime, Text, JSON, ForeignKey, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from ...core.database_logs import LogsBase

class ActivityLog(LogsBase):
    __tablename__ = "activity_logs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=True)  # Không có foreign key vì ở database khác
    action = Column(String(50), nullable=False)  # CREATE, UPDATE, DELETE, LOGIN, LOGOUT, etc.
    resource_type = Column(String(100), nullable=False)  # User, Role, Site, etc.
    resource_id = Column(String(100), nullable=True)  # ID của resource bị thay đổi
    resource_name = Column(String(255), nullable=True)  # Tên hiển thị của resource
    description = Column(Text, nullable=True)  # <PERSON>ô tả chi tiết hành động
    old_values = Column(JSON, nullable=True)  # Giá trị cũ (cho UPDATE)
    new_values = Column(JSON, nullable=True)  # Giá trị mới
    ip_address = Column(String(45), nullable=True)  # IP address của user
    user_agent = Column(Text, nullable=True)  # User agent của browser
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Thông tin user (lưu trữ dưới dạng JSON thay vì relationship)
    user_info = Column(JSON, nullable=True)  # Lưu thông tin user như full_name, email, etc.
    
    def __repr__(self):
        return f"<ActivityLog(id={self.id}, action={self.action}, resource_type={self.resource_type})>" 