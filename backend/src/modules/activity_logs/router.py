from datetime import datetime
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from sqlalchemy.orm import Session
from pydantic import BaseModel
from ...core.database_logs import get_logs_db
from .service import ActivityLogService
from .models import ActivityLog

router = APIRouter(prefix="/activity-logs", tags=["Activity Logs"])

# Pydantic models cho request/response
class ActivityLogResponse(BaseModel):
    id: str
    user_id: Optional[str]
    action: str
    resource_type: str
    resource_id: Optional[str]
    resource_name: Optional[str]
    description: Optional[str]
    old_values: Optional[dict]
    new_values: Optional[dict]
    ip_address: Optional[str]
    user_agent: Optional[str]
    created_at: datetime
    user_full_name: Optional[str] = None
    user_info: Optional[dict] = None
    
    class Config:
        from_attributes = True

class ActivityLogCreate(BaseModel):
    action: str
    resource_type: str
    resource_id: Optional[str] = None
    resource_name: Optional[str] = None
    description: Optional[str] = None
    old_values: Optional[dict] = None
    new_values: Optional[dict] = None

class ActivityLogFilter(BaseModel):
    user_id: Optional[str] = None
    action: Optional[str] = None
    resource_type: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None

@router.get("/", response_model=List[ActivityLogResponse])
async def get_activity_logs(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    user_id: Optional[str] = Query(None),
    action: Optional[str] = Query(None),
    resource_type: Optional[str] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_logs_db)
):
    """Lấy danh sách activity logs với filter"""
    service = ActivityLogService(db)
    logs = service.get_logs(
        skip=skip,
        limit=limit,
        user_id=user_id,
        action=action,
        resource_type=resource_type,
        start_date=start_date,
        end_date=end_date
    )
    
    # Thêm thông tin user name từ user_info
    result = []
    for log in logs:
        log_dict = ActivityLogResponse.from_orm(log).dict()
        if log.user_info:
            log_dict["user_full_name"] = log.user_info.get("full_name")
        result.append(log_dict)
    
    return result

@router.get("/{log_id}", response_model=ActivityLogResponse)
async def get_activity_log(log_id: str, db: Session = Depends(get_logs_db)):
    """Lấy chi tiết một activity log"""
    service = ActivityLogService(db)
    log = service.get_log_by_id(log_id)
    
    if not log:
        raise HTTPException(status_code=404, detail="Activity log not found")
    
    log_dict = ActivityLogResponse.from_orm(log).dict()
    if log.user_info:
        log_dict["user_full_name"] = log.user_info.get("full_name")
    
    return log_dict

@router.get("/user/{user_id}", response_model=List[ActivityLogResponse])
async def get_user_activity(
    user_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_logs_db)
):
    """Lấy hoạt động của một user cụ thể"""
    service = ActivityLogService(db)
    logs = service.get_user_activity(user_id, skip=skip, limit=limit)
    
    result = []
    for log in logs:
        log_dict = ActivityLogResponse.from_orm(log).dict()
        if log.user_info:
            log_dict["user_full_name"] = log.user_info.get("full_name")
        result.append(log_dict)
    
    return result

@router.get("/resource/{resource_type}/{resource_id}", response_model=List[ActivityLogResponse])
async def get_resource_activity(
    resource_type: str,
    resource_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_logs_db)
):
    """Lấy hoạt động của một resource cụ thể"""
    service = ActivityLogService(db)
    logs = service.get_resource_activity(resource_type, resource_id, skip=skip, limit=limit)
    
    result = []
    for log in logs:
        log_dict = ActivityLogResponse.from_orm(log).dict()
        if log.user_info:
            log_dict["user_full_name"] = log.user_info.get("full_name")
        result.append(log_dict)
    
    return result

@router.delete("/{log_id}")
async def delete_activity_log(log_id: str, db: Session = Depends(get_logs_db)):
    """Xóa một activity log"""
    service = ActivityLogService(db)
    success = service.delete_log(log_id)
    
    if not success:
        raise HTTPException(status_code=404, detail="Activity log not found")
    
    return {"message": "Activity log deleted successfully"}

@router.get("/statistics/summary")
async def get_activity_statistics(db: Session = Depends(get_logs_db)):
    """Lấy thống kê tổng quan về activity logs"""
    service = ActivityLogService(db)
    return service.get_statistics()

@router.post("/", response_model=ActivityLogResponse)
async def create_activity_log(
    log_data: ActivityLogCreate,
    request: Request,
    db: Session = Depends(get_logs_db)
):
    """Tạo một activity log mới (thường được gọi từ middleware hoặc service khác)"""
    service = ActivityLogService(db)
    
    # Lấy thông tin từ request
    client_ip = request.client.host if request.client else None
    user_agent = request.headers.get("user-agent")
    
    log = service.create_log(
        action=log_data.action,
        resource_type=log_data.resource_type,
        resource_id=log_data.resource_id,
        resource_name=log_data.resource_name,
        description=log_data.description,
        old_values=log_data.old_values,
        new_values=log_data.new_values,
        ip_address=client_ip,
        user_agent=user_agent
    )
    
    log_dict = ActivityLogResponse.from_orm(log).dict()
    if log.user_info:
        log_dict["user_full_name"] = log.user_info.get("full_name")
    
    return log_dict 