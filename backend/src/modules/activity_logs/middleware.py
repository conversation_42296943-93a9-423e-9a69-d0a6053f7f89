# Đường dẫn: backend/src/modules/activity_logs/middleware.py
import json
from typing import Callable, Dict, Any
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from .service import ActivityLogService
from ...core.database_logs import get_logs_db

class ActivityLogMiddleware:
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            request = Request(scope, receive)
            
            # Bỏ qua các endpoint không cần log
            if self._should_skip_logging(request.url.path):
                await self.app(scope, receive, send)
                return
            
            # Lấy thông tin request
            method = request.method
            path = request.url.path
            client_ip = request.client.host if request.client else None
            user_agent = request.headers.get("user-agent", "")
            
            # Xác định action và resource type từ request
            action, resource_type, resource_id = self._parse_request_info(method, path)
            
            # Tạo response wrapper để capture response
            response_wrapper = ResponseWrapper(send)
            
            try:
                # Thực hiện request
                await self.app(scope, receive, response_wrapper.send)
                
                # Ghi log sau khi request hoàn thành
                await self._log_activity(
                    action=action,
                    resource_type=resource_type,
                    resource_id=resource_id,
                    description=f"{method} {path}",
                    ip_address=client_ip,
                    user_agent=user_agent,
                    status_code=response_wrapper.status_code
                )
                
            except Exception as e:
                # Ghi log lỗi
                await self._log_activity(
                    action="ERROR",
                    resource_type=resource_type,
                    resource_id=resource_id,
                    description=f"Error in {method} {path}: {str(e)}",
                    ip_address=client_ip,
                    user_agent=user_agent,
                    status_code=500
                )
                raise
        else:
            await self.app(scope, receive, send)
    
    def _should_skip_logging(self, path: str) -> bool:
        """Kiểm tra xem có nên bỏ qua logging cho path này không"""
        skip_paths = [
            "/docs",
            "/openapi.json",
            "/activity-logs",  # Tránh log chính các API activity logs
            "/health",
            "/favicon.ico"
        ]
        
        return any(path.startswith(skip_path) for skip_path in skip_paths)
    
    def _parse_request_info(self, method: str, path: str) -> tuple:
        """Phân tích request để xác định action, resource_type, resource_id"""
        action = "VIEW"
        resource_type = "Unknown"
        resource_id = None
        
        # Xác định action dựa trên HTTP method
        if method == "GET":
            action = "VIEW"
        elif method == "POST":
            action = "CREATE"
        elif method == "PUT" or method == "PATCH":
            action = "UPDATE"
        elif method == "DELETE":
            action = "DELETE"
        
        # Xác định resource type từ path
        path_parts = path.strip("/").split("/")
        
        if len(path_parts) >= 1:
            resource_type = path_parts[0].title()
            
            # Xác định resource ID nếu có
            if len(path_parts) >= 2 and path_parts[1] and not path_parts[1].startswith("?"):
                resource_id = path_parts[1]
        
        return action, resource_type, resource_id
    
    async def _log_activity(
        self,
        action: str,
        resource_type: str,
        resource_id: str = None,
        description: str = None,
        ip_address: str = None,
        user_agent: str = None,
        status_code: int = None
    ):
        """Ghi log activity"""
        try:
            # Tạo database session cho logs
            db = next(get_logs_db())
            service = ActivityLogService(db)
            
            # Thêm status code vào description nếu có
            if status_code:
                description = f"{description} (Status: {status_code})"
            
            # Tạo log
            service.create_log(
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                description=description,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
        except Exception as e:
            # Không throw exception để không ảnh hưởng đến request chính
            print(f"Error logging activity: {e}")


class ResponseWrapper:
    def __init__(self, send):
        self.send = send
        self.status_code = 200
    
    async def send(self, message):
        if message["type"] == "http.response.start":
            self.status_code = message.get("status", 200)
        await self.send(message)


# Decorator để ghi log cho các function cụ thể
def log_activity(action: str, resource_type: str, description: str = None):
    """Decorator để ghi log cho các function cụ thể"""
    def decorator(func: Callable) -> Callable:
        async def wrapper(*args, **kwargs):
            try:
                # Thực hiện function
                result = await func(*args, **kwargs)
                
                # Ghi log
                await _log_function_activity(
                    action=action,
                    resource_type=resource_type,
                    description=description or f"Function: {func.__name__}",
                    success=True
                )
                
                return result
                
            except Exception as e:
                # Ghi log lỗi
                await _log_function_activity(
                    action="ERROR",
                    resource_type=resource_type,
                    description=f"Error in {func.__name__}: {str(e)}",
                    success=False
                )
                raise
        
        return wrapper
    return decorator


async def _log_function_activity(
    action: str,
    resource_type: str,
    description: str,
    success: bool
):
    """Ghi log cho function activity"""
    try:
        db = next(get_logs_db())
        service = ActivityLogService(db)
        
        service.create_log(
            action=action,
            resource_type=resource_type,
            description=description
        )
        
    except Exception as e:
        print(f"Error logging function activity: {e}") 