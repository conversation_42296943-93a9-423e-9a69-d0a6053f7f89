# Activity Logs Module

Module ghi nhận chi tiết các thay đổi trên hệ thống và các hoạt động thêm, xóa, sửa trong database.

## Tính năng

- **Ghi log tự động**: Tự động ghi nhận các hoạt động HTTP request/response
- **Ghi log thủ công**: Cho phép ghi log thủ công cho các hoạt động cụ thể
- **Theo dõi thay đổi**: Lưu trữ giá trị cũ và mới cho các thao tác UPDATE
- **Thống kê**: Cung cấp thống kê về hoạt động hệ thống
- **Tìm kiếm và lọc**: Hỗ trợ tìm kiếm và lọc logs theo nhiều tiêu chí
- **Database riêng biệt**: Sử dụng database `metisdb_logs` riêng để lưu trữ logs

## C<PERSON><PERSON> trúc Database

Module sử dụng database riêng `metisdb_logs` để lưu trữ activity logs, tách biệt khỏi database chính `metisdb`.

### Bảng `activity_logs` (trong metisdb_logs)

| Cột | Kiểu dữ liệu | Mô tả |
|-----|-------------|-------|
| id | UUID | Khóa chính |
| user_id | UUID | ID của user thực hiện hành động (không có foreign key) |
| action | String(50) | Loại hành động (CREATE, UPDATE, DELETE, VIEW, etc.) |
| resource_type | String(100) | Loại tài nguyên (User, Role, Site, etc.) |
| resource_id | String(100) | ID của tài nguyên bị thay đổi |
| resource_name | String(255) | Tên hiển thị của tài nguyên |
| description | Text | Mô tả chi tiết hành động |
| old_values | JSON | Giá trị cũ (cho UPDATE) |
| new_values | JSON | Giá trị mới |
| ip_address | String(45) | IP address của user |
| user_agent | Text | User agent của browser |
| created_at | DateTime | Thời gian tạo log |
| user_info | JSON | Thông tin user (full_name, email, username) |

## Cấu hình Database

### Database chính (metisdb)
- Chứa dữ liệu chính của hệ thống
- Các bảng: users, roles, sites, etc.

### Database logs (metisdb_logs)
- Chứa riêng activity logs
- Kết nối: `postgresql://postgres:postgres@localhost:5432/metisdb_logs`
- Biến môi trường: `LOGS_DATABASE_URL`

## API Endpoints

### Lấy danh sách logs
```
GET /activity-logs/
```

**Query Parameters:**
- `skip`: Số bản ghi bỏ qua (mặc định: 0)
- `limit`: Số bản ghi trả về (mặc định: 100, tối đa: 1000)
- `user_id`: Lọc theo user ID
- `action`: Lọc theo loại hành động
- `resource_type`: Lọc theo loại tài nguyên
- `start_date`: Lọc từ ngày
- `end_date`: Lọc đến ngày

### Lấy chi tiết log
```
GET /activity-logs/{log_id}
```

### Lấy hoạt động của user
```
GET /activity-logs/user/{user_id}
```

### Lấy hoạt động của resource
```
GET /activity-logs/resource/{resource_type}/{resource_id}
```

### Xóa log
```
DELETE /activity-logs/{log_id}
```

### Lấy thống kê
```
GET /activity-logs/statistics/summary
```

### Tạo log thủ công
```
POST /activity-logs/
```

**Request Body:**
```json
{
  "action": "CREATE",
  "resource_type": "User",
  "resource_id": "123",
  "resource_name": "John Doe",
  "description": "Created new user",
  "old_values": null,
  "new_values": {
    "username": "john.doe",
    "email": "<EMAIL>"
  }
}
```

## Sử dụng trong Code

### Sử dụng Service trực tiếp

```python
from src.modules.activity_logs.service import ActivityLogService
from src.core.database_logs import get_logs_db

# Trong function của bạn
db = next(get_logs_db())
service = ActivityLogService(db)

# Ghi log
service.create_log(
    action="CREATE",
    resource_type="User",
    resource_id="123",
    resource_name="John Doe",
    description="Created new user",
    new_values={"username": "john.doe", "email": "<EMAIL>"}
)
```

### Sử dụng Decorator

```python
from src.modules.activity_logs.middleware import log_activity

@log_activity(action="CREATE", resource_type="User", description="Create new user")
async def create_user(user_data):
    # Logic tạo user
    pass
```

## Middleware

Module cung cấp middleware để tự động ghi log các HTTP request:

```python
from src.modules.activity_logs.middleware import ActivityLogMiddleware

# Thêm middleware vào FastAPI app
app.add_middleware(ActivityLogMiddleware)
```

Middleware sẽ tự động:
- Ghi log tất cả HTTP requests (trừ một số endpoint đặc biệt)
- Xác định action dựa trên HTTP method
- Xác định resource type từ URL path
- Lưu IP address và user agent

## Cấu hình

Module được cấu hình là Core Module với:
- `is_core_module`: true
- `auto_install`: true
- `hidden`: false

Module sẽ được tự động cài đặt khi khởi động server.

## Dependencies

- `base`: Module cơ bản
- `user_management`: Để lấy thông tin user từ database chính

## Migration

Để tạo bảng activity_logs trong database metisdb_logs, chạy:

```bash
cd backend
python migrate_activity_logs.py
```

Hoặc module sẽ được tự động tạo khi cài đặt.

## Lưu ý quan trọng

1. **Database riêng biệt**: Activity logs được lưu trong database `metisdb_logs` riêng biệt
2. **Không có foreign key**: Không có foreign key trực tiếp đến bảng users vì ở database khác
3. **Thông tin user**: Thông tin user được lưu dưới dạng JSON trong cột `user_info`
4. **Kết nối song song**: Module sử dụng 2 kết nối database: chính và logs
5. **Backup riêng**: Nên backup database logs riêng biệt với database chính 