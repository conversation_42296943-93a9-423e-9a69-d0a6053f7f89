#!/usr/bin/env python3
"""
Migration script để thêm cột is_activated vào bảng roles
"""
import sys
import os

# Thêm đường dẫn src vào sys.path để import được các module
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.database import get_db_connection

def migrate_add_is_activated_to_roles():
    """Thêm cột is_activated vào bảng roles"""
    
    try:
        # Sử dụng hàm get_db_connection từ core/database.py
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("✅ Đã kết nối thành công đến database")
        
        # Kiểm tra xem cột is_activated đã tồn tại chưa
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'roles' AND column_name = 'is_activated'
        """)
        
        column_exists = cursor.fetchone()
        
        if column_exists:
            print("ℹ️  Cột 'is_activated' đã tồn tại trong bảng 'roles'")
        else:
            # Thêm cột is_activated với giá trị mặc định là True
            cursor.execute("""
                ALTER TABLE roles 
                ADD COLUMN is_activated BOOLEAN DEFAULT TRUE NOT NULL
            """)
            print("✅ Đã thêm cột 'is_activated' vào bảng 'roles'")
            
            # Cập nhật tất cả các role hiện có thành True
            cursor.execute("""
                UPDATE roles 
                SET is_activated = TRUE 
                WHERE is_activated IS NULL
            """)
            print("✅ Đã cập nhật tất cả roles hiện có thành trạng thái kích hoạt")
        
        # Commit thay đổi
        conn.commit()
        
        # Hiển thị thông tin bảng roles sau migration
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'roles'
            ORDER BY ordinal_position
        """)
        
        columns = cursor.fetchall()
        print("\n📋 Cấu trúc bảng 'roles' sau migration:")
        print("-" * 80)
        print(f"{'Cột':<20} {'Kiểu dữ liệu':<15} {'Null':<8} {'Mặc định':<20}")
        print("-" * 80)
        
        for column in columns:
            column_name, data_type, is_nullable, column_default = column
            default_value = column_default if column_default else 'NULL'
            print(f"{column_name:<20} {data_type:<15} {is_nullable:<8} {default_value:<20}")
        
        # Hiển thị dữ liệu mẫu
        cursor.execute("SELECT id, name, description, is_activated FROM roles LIMIT 5")
        roles = cursor.fetchall()
        
        if roles:
            print(f"\n📊 Dữ liệu mẫu từ bảng 'roles' (hiển thị {len(roles)} records đầu tiên):")
            print("-" * 80)
            print(f"{'ID':<5} {'Tên Role':<20} {'Mô tả':<30} {'Kích hoạt':<10}")
            print("-" * 80)
            
            for role in roles:
                role_id, name, description, is_activated = role
                desc = description if description else 'N/A'
                status = 'Có' if is_activated else 'Không'
                print(f"{role_id:<5} {name:<20} {desc:<30} {status:<10}")
        
        cursor.close()
        conn.close()
        print("\n✅ Migration hoàn thành thành công!")
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    print("🚀 Bắt đầu migration: Thêm cột is_activated vào bảng roles")
    print("=" * 60)
    migrate_add_is_activated_to_roles() 