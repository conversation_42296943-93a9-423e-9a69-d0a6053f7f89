# Script tự động sinh permission cho menu và button của chức năng Role permissions trong user_management
import requests
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from src.modules.user_management.models import Permission

API_URL = os.getenv("PERMISSION_API_URL", "http://localhost:8000/api/user-management/permissions")
MODULE_NAME = "user_management"
MENU = {"key": "role_permissions", "label": "Role permissions"}
BUTTONS = [
    {"key": "save", "label": "<PERSON>ư<PERSON>", "parent_code": "user_management_menu_role_permissions"},
    {"key": "refresh", "label": "Làm mới", "parent_code": "user_management_menu_role_permissions"}
]

# Lấy id của permission module user_management_view
engine = create_engine(os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/metisdb"))
Session = sessionmaker(bind=engine)
session = Session()
module_perm = session.query(Permission).filter_by(code=f"{MODULE_NAME}_view").first()
parent_module_id = module_perm.id if module_perm else None

# Tạo permission cho menu Role permissions
payload_menu = {
    "code": f"{MODULE_NAME}_menu_{MENU['key']}",
    "name": f"Xem menu {MENU['label']}",
    "type": "menu",
    "module_name": MODULE_NAME,
    "parent_id": parent_module_id
}
resp = requests.post(API_URL, json=payload_menu)
print(f"Tạo permission cho menu {MENU['key']}: {resp.status_code} - {resp.text}")
if resp.status_code == 200:
    menu_id = resp.json().get('id')
else:
    perm = session.query(Permission).filter_by(code=payload_menu['code']).first()
    menu_id = perm.id if perm else None

# Tạo permission cho các button
for btn in BUTTONS:
    payload = {
        "code": f"{MODULE_NAME}_btn_{btn['key']}_role_permissions",
        "name": f"Button {btn['label']} (Role permissions)",
        "type": "button",
        "module_name": MODULE_NAME,
        "parent_id": menu_id
    }
    resp = requests.post(API_URL, json=payload)
    print(f"Tạo permission cho button {btn['key']}: {resp.status_code} - {resp.text}")
session.close()
