# Script tự động sinh permission cho các button trong module site_management
import requests
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from src.modules.user_management.models import Permission

API_URL = os.getenv("PERMISSION_API_URL", "http://localhost:8000/api/user-management/permissions")
MODULE_NAME = "site_management"
BUTTONS = [
    {"key": "edit", "label": "Sửa", "parent_code": "site_management_menu_list"},
    {"key": "delete", "label": "Xóa", "parent_code": "site_management_menu_list"},
    {"key": "refresh", "label": "Làm mới", "parent_code": "site_management_menu_list"},
    {"key": "add_new", "label": "Thêm mới", "parent_code": "site_management_menu_list"},
    {"key": "cancel", "label": "<PERSON>ủ<PERSON>", "parent_code": "site_management_menu_add"},
    {"key": "save_and_new", "label": "<PERSON><PERSON><PERSON> & Thêm mới", "parent_code": "site_management_menu_add"},
    {"key": "save_and_close", "label": "Lưu & Đóng", "parent_code": "site_management_menu_add"},
    {"key": "open_apps_page", "label": "Mở trang Apps", "parent_code": "site_management_menu_overview"}
]

# Lấy parent_id cho từng button
engine = create_engine(os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/metisdb"))
Session = sessionmaker(bind=engine)
session = Session()
parent_ids = {}
for btn in BUTTONS:
    parent_perm = session.query(Permission).filter_by(code=btn["parent_code"]).first()
    parent_ids[btn["key"]] = parent_perm.id if parent_perm else None
session.close()

for btn in BUTTONS:
    payload = {
        "code": f"{MODULE_NAME}_btn_{btn['key']}",
        "name": f"Button {btn['label']}",
        "type": "button",
        "module_name": MODULE_NAME,
        "parent_id": parent_ids[btn["key"]]
    }
    resp = requests.post(API_URL, json=payload)
    print(f"Tạo permission cho button {btn['key']}: {resp.status_code} - {resp.text}")
