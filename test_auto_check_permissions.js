// Test script để kiểm tra auto-check permissions khi chọn role
const puppeteer = require('puppeteer');

async function testAutoCheckPermissions() {
  console.log('🚀 Test auto-check permissions...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('RolePermissionsPage') || text.includes('checked keys') || text.includes('permissions loaded')) {
      console.log(`🔍 Frontend: ${text}`);
    }
  });
  
  try {
    // Đăng nhập
    console.log('🔐 Đăng nhập...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    await page.type('input[placeholder="Tên đăng nhập"]', 'admin');
    await page.type('input[placeholder="Mật khẩu"]', 'admin123');
    await page.click('button[type="submit"]');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Navigate đến user-management
    console.log('🔄 Navigate đến user-management...');
    await page.goto('http://localhost:5173/user-management', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Click vào menu Role permissions
    console.log('📋 Click vào menu Role permissions...');
    await page.evaluate(() => {
      const menuItems = Array.from(document.querySelectorAll('.ant-menu-item'));
      const rolePermissionsItem = menuItems.find(item => 
        item.textContent && item.textContent.includes('Role permissions')
      );
      
      if (rolePermissionsItem) {
        rolePermissionsItem.click();
      }
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('✅ Role permissions page loaded');
    
    // Kiểm tra trạng thái ban đầu
    console.log('🔍 Kiểm tra trạng thái ban đầu...');
    const initialState = await page.evaluate(() => {
      const checkedNodes = document.querySelectorAll('.ant-tree-checkbox-checked');
      return {
        checkedCount: checkedNodes.length,
        debugInfo: document.querySelector('.debug-info') ? document.querySelector('.debug-info').textContent : 'No debug info'
      };
    });
    
    console.log('📊 Initial state:', initialState);
    
    // Mô phỏng chọn role bằng cách gọi trực tiếp onChange
    console.log('🎯 Chọn role ID 6 (có permissions [1,2,3])...');
    await page.evaluate(() => {
      // Tìm SearchRoleDropdown component và trigger onChange
      const dropdown = document.querySelector('.search-role-dropdown');
      if (dropdown) {
        // Simulate selecting role ID 6
        const event = new CustomEvent('roleSelected', { detail: { roleId: 6 } });
        dropdown.dispatchEvent(event);
        
        // Try to find and trigger the onChange callback directly
        // This is a workaround since the dropdown is custom
        console.log('RolePermissionsPage: Manually triggering role selection for role 6');
        
        // Look for React component instance
        const reactKey = Object.keys(dropdown).find(key => key.startsWith('__reactInternalInstance'));
        if (reactKey) {
          const reactInstance = dropdown[reactKey];
          // Try to find the onChange prop in the component tree
          console.log('Found React instance, attempting to trigger onChange');
        }
      }
    });
    
    // Chờ một chút để component update
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Kiểm tra xem có permissions nào được check không
    console.log('🔍 Kiểm tra sau khi chọn role...');
    const afterSelectState = await page.evaluate(() => {
      const checkedNodes = document.querySelectorAll('.ant-tree-checkbox-checked');
      const checkedIds = Array.from(checkedNodes).map(node => {
        const treeNode = node.closest('.ant-tree-treenode');
        return treeNode ? treeNode.getAttribute('data-key') : null;
      }).filter(id => id !== null);
      
      return {
        checkedCount: checkedNodes.length,
        checkedIds: checkedIds,
        debugInfo: document.querySelector('.debug-info') ? document.querySelector('.debug-info').textContent : 'No debug info'
      };
    });
    
    console.log('📊 After select state:', afterSelectState);
    
    if (afterSelectState.checkedCount > 0) {
      console.log('✅ Auto-check permissions hoạt động! Checked IDs:', afterSelectState.checkedIds);
    } else {
      console.log('❌ Auto-check permissions không hoạt động');
      
      // Thử cách khác: click trực tiếp vào dropdown option
      console.log('🔄 Thử click trực tiếp vào dropdown...');
      
      // Click vào dropdown để mở
      await page.click('.search-role-dropdown .dropdown-input');
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Kiểm tra xem có options không
      const hasOptions = await page.evaluate(() => {
        const options = document.querySelectorAll('.dropdown-option');
        console.log('Found', options.length, 'dropdown options');
        
        // Tìm option có text chứa "admin" (role ID 6)
        const adminOption = Array.from(options).find(option => 
          option.textContent && option.textContent.toLowerCase().includes('admin')
        );
        
        if (adminOption) {
          console.log('Found admin option, clicking...');
          adminOption.click();
          return true;
        }
        
        // Nếu không tìm thấy admin, click option đầu tiên
        if (options.length > 0) {
          console.log('Clicking first option...');
          options[0].click();
          return true;
        }
        
        return false;
      });
      
      if (hasOptions) {
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Kiểm tra lại
        const finalState = await page.evaluate(() => {
          const checkedNodes = document.querySelectorAll('.ant-tree-checkbox-checked');
          const checkedIds = Array.from(checkedNodes).map(node => {
            const treeNode = node.closest('.ant-tree-treenode');
            return treeNode ? treeNode.getAttribute('data-key') : null;
          }).filter(id => id !== null);
          
          return {
            checkedCount: checkedNodes.length,
            checkedIds: checkedIds
          };
        });
        
        console.log('📊 Final state:', finalState);
        
        if (finalState.checkedCount > 0) {
          console.log('✅ Auto-check permissions hoạt động sau khi click dropdown!');
        } else {
          console.log('❌ Auto-check permissions vẫn không hoạt động');
        }
      }
    }
    
    console.log('⏳ Giữ browser mở 30 giây để quan sát...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành test');
  }
}

testAutoCheckPermissions().catch(console.error);
