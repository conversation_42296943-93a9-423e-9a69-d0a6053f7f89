// Test script để verify Site Management permission filtering
// Chạy script này trong browser console khi đã login với user phucnguyen

console.log("🧪 TESTING SITE MANAGEMENT PERMISSION FILTERING");
console.log("===============================================");

// Test 1: Kiểm tra user phucnguyen permissions
async function testUserPermissions() {
  console.log("\n📋 Test 1: Kiểm tra permissions của user phucnguyen");
  
  try {
    const response = await fetch('http://localhost:8000/api/user-management/users/996558ba-0653-4295-a8b2-86e88c69b9a8/permissions');
    const permissions = await response.json();
    
    console.log("✅ User phucnguyen có các permissions sau:");
    permissions.forEach(p => {
      console.log(`   - ${p.code}: ${p.name}`);
    });
    
    // Kiểm tra các permissions cụ thể
    const permissionCodes = permissions.map(p => p.code);
    
    console.log("\n🔍 Kiểm tra permissions cụ thể:");
    console.log(`   site_management_menu_overview: ${permissionCodes.includes('site_management_menu_overview') ? '✅ CÓ' : '❌ KHÔNG'}`);
    console.log(`   site_management_menu_list: ${permissionCodes.includes('site_management_menu_list') ? '✅ CÓ' : '❌ KHÔNG'}`);
    console.log(`   site_management_menu_add: ${permissionCodes.includes('site_management_menu_add') ? '✅ CÓ' : '❌ KHÔNG'}`);
    console.log(`   site_management_btn_refresh: ${permissionCodes.includes('site_management_btn_refresh') ? '✅ CÓ' : '❌ KHÔNG'}`);
    console.log(`   site_management_btn_add_new: ${permissionCodes.includes('site_management_btn_add_new') ? '✅ CÓ' : '❌ KHÔNG'}`);
    console.log(`   site_management_btn_edit: ${permissionCodes.includes('site_management_btn_edit') ? '✅ CÓ' : '❌ KHÔNG'}`);
    console.log(`   site_management_btn_delete: ${permissionCodes.includes('site_management_btn_delete') ? '✅ CÓ' : '❌ KHÔNG'}`);
    
    return permissions;
  } catch (error) {
    console.error("❌ Lỗi khi lấy permissions:", error);
    return [];
  }
}

// Test 2: Kiểm tra UI elements trong Site Management
function testUIElements() {
  console.log("\n🎨 Test 2: Kiểm tra UI elements trong Site Management");
  
  // Kiểm tra menu items
  console.log("\n📋 Menu Items:");
  const menuItems = document.querySelectorAll('.site-management-nav-menu .ant-menu-item');
  menuItems.forEach((item, index) => {
    const text = item.textContent.trim();
    const isVisible = item.style.display !== 'none' && !item.classList.contains('ant-menu-item-disabled');
    console.log(`   Menu ${index + 1}: "${text}" - ${isVisible ? '✅ HIỂN THỊ' : '❌ ẨN'}`);
  });
  
  // Kiểm tra buttons trong list view
  console.log("\n🔘 Buttons trong List View:");
  const refreshBtn = document.querySelector('[title*="Làm mới"], [title*="refresh"]');
  const addNewBtn = document.querySelector('[title*="Thêm mới"], [title*="Add"]');
  
  console.log(`   Refresh Button: ${refreshBtn ? '✅ HIỂN THỊ' : '❌ ẨN'}`);
  console.log(`   Add New Button: ${addNewBtn ? '✅ HIỂN THỊ' : '❌ ẨN'}`);
  
  // Kiểm tra action buttons trong table
  console.log("\n⚡ Action Buttons trong Table:");
  const editBtns = document.querySelectorAll('[title*="Sửa"], [title*="Edit"]');
  const deleteBtns = document.querySelectorAll('[title*="Xóa"], [title*="Delete"]');
  
  console.log(`   Edit Buttons: ${editBtns.length > 0 ? `✅ HIỂN THỊ (${editBtns.length})` : '❌ ẨN'}`);
  console.log(`   Delete Buttons: ${deleteBtns.length > 0 ? `✅ HIỂN THỊ (${deleteBtns.length})` : '❌ ẨN'}`);
  
  // Kiểm tra form buttons
  console.log("\n💾 Form Buttons:");
  const saveButtons = document.querySelectorAll('button:contains("Lưu"), button:contains("Save")');
  const cancelButtons = document.querySelectorAll('button:contains("Hủy"), button:contains("Cancel")');
  
  console.log(`   Save Buttons: ${saveButtons.length > 0 ? `✅ HIỂN THỊ (${saveButtons.length})` : '❌ ẨN'}`);
  console.log(`   Cancel Buttons: ${cancelButtons.length > 0 ? `✅ HIỂN THỊ (${cancelButtons.length})` : '❌ ẨN'}`);
}

// Test 3: Kiểm tra expected behavior
function testExpectedBehavior() {
  console.log("\n🎯 Test 3: Kiểm tra Expected Behavior");
  console.log("Dựa trên permissions của user phucnguyen, expected behavior:");
  
  console.log("\n✅ SHOULD BE VISIBLE:");
  console.log("   - Menu 'Tổng quan' (có permission site_management_menu_overview)");
  console.log("   - Button 'Làm mới' (có permission site_management_btn_refresh)");
  
  console.log("\n❌ SHOULD BE HIDDEN:");
  console.log("   - Menu 'Danh sách Site' (không có permission site_management_menu_list)");
  console.log("   - Menu 'Thêm Site' (không có permission site_management_menu_add)");
  console.log("   - Button 'Thêm mới' (không có permission site_management_btn_add_new)");
  console.log("   - Button 'Sửa' trong table (không có permission site_management_btn_edit)");
  console.log("   - Button 'Xóa' trong table (không có permission site_management_btn_delete)");
  console.log("   - Button 'Lưu & Đóng' trong form (không có permission site_management_btn_save_and_close)");
  console.log("   - Button 'Lưu & Thêm mới' trong form (không có permission site_management_btn_save_and_new)");
  console.log("   - Button 'Hủy' trong form (không có permission site_management_btn_cancel)");
}

// Chạy tất cả tests
async function runAllTests() {
  console.log("🚀 BẮT ĐẦU TESTING...\n");
  
  const permissions = await testUserPermissions();
  
  // Đợi một chút để UI render
  setTimeout(() => {
    testUIElements();
    testExpectedBehavior();
    
    console.log("\n🏁 TESTING HOÀN THÀNH!");
    console.log("===============================================");
    console.log("📝 Hướng dẫn:");
    console.log("1. Login với user phucnguyen");
    console.log("2. Vào Site Management page");
    console.log("3. Chạy script này trong browser console");
    console.log("4. Kiểm tra kết quả với expected behavior");
  }, 2000);
}

// Auto run
runAllTests();
