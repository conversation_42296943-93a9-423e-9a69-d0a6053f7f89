// Script debug chi tiết cho Role permissions
const puppeteer = require('puppeteer');

async function debugRolePermissions() {
  console.log('🚀 Debug Role permissions...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('RolePermissionsPage') || text.includes('Error') || text.includes('DEBUG')) {
      console.log(`🔍 Frontend: ${text}`);
    }
  });
  
  try {
    // Đăng nhập
    console.log('🔐 Đăng nhập...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    await page.type('input[placeholder="Tên đăng nhập"]', 'admin');
    await page.type('input[placeholder="Mật khẩu"]', 'admin123');
    await page.click('button[type="submit"]');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Navigate đến user-management
    console.log('🔄 Navigate đến user-management...');
    await page.goto('http://localhost:5173/user-management', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Click vào menu Role permissions
    console.log('📋 Click vào menu Role permissions...');
    await page.evaluate(() => {
      const menuItems = Array.from(document.querySelectorAll('.ant-menu-item'));
      const rolePermissionsItem = menuItems.find(item => 
        item.textContent && item.textContent.includes('Role permissions')
      );
      
      if (rolePermissionsItem) {
        console.log('Found Role permissions menu item, clicking...');
        rolePermissionsItem.click();
      }
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Debug: Kiểm tra state hiện tại
    console.log('🔍 Kiểm tra state hiện tại...');
    const currentState = await page.evaluate(() => {
      const title = document.querySelector('.ant-card-head-title');
      const dropdown = document.querySelector('.search-role-dropdown');
      const dropdownInput = document.querySelector('.search-role-dropdown .dropdown-input');
      const saveButton = Array.from(document.querySelectorAll('button')).find(btn => 
        btn.textContent && btn.textContent.trim() === 'Lưu'
      );
      
      return {
        title: title ? title.textContent : null,
        hasDropdown: !!dropdown,
        hasDropdownInput: !!dropdownInput,
        saveButton: {
          exists: !!saveButton,
          disabled: saveButton ? saveButton.disabled : null,
          text: saveButton ? saveButton.textContent : null
        }
      };
    });
    
    console.log('📊 Current state:', JSON.stringify(currentState, null, 2));
    
    if (currentState.hasDropdownInput) {
      console.log('🎯 Thử click vào dropdown...');
      await page.click('.search-role-dropdown .dropdown-input');
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Kiểm tra options
      const optionsState = await page.evaluate(() => {
        const options = document.querySelectorAll('.dropdown-option');
        const optionsData = Array.from(options).map((option, index) => ({
          index,
          text: option.textContent,
          className: option.className
        }));
        
        return {
          optionsCount: options.length,
          options: optionsData.slice(0, 5) // Chỉ lấy 5 options đầu
        };
      });
      
      console.log('📋 Options state:', JSON.stringify(optionsState, null, 2));
      
      if (optionsState.optionsCount > 0) {
        console.log('✅ Chọn role đầu tiên...');
        await page.evaluate(() => {
          const options = document.querySelectorAll('.dropdown-option');
          if (options.length > 0) {
            console.log('Clicking first role option...');
            options[0].click();
          }
        });
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Kiểm tra state sau khi chọn role
        const afterSelectState = await page.evaluate(() => {
          const saveButton = Array.from(document.querySelectorAll('button')).find(btn => 
            btn.textContent && btn.textContent.trim() === 'Lưu'
          );
          
          const selectedValue = document.querySelector('.search-role-dropdown input')?.value;
          
          return {
            selectedValue,
            saveButton: {
              exists: !!saveButton,
              disabled: saveButton ? saveButton.disabled : null
            }
          };
        });
        
        console.log('📊 After select state:', JSON.stringify(afterSelectState, null, 2));
        
        if (!afterSelectState.saveButton.disabled) {
          console.log('💾 Click nút Lưu...');
          await page.evaluate(() => {
            const saveButton = Array.from(document.querySelectorAll('button')).find(btn => 
              btn.textContent && btn.textContent.trim() === 'Lưu'
            );
            if (saveButton) {
              console.log('RolePermissionsPage: Clicking save button');
              saveButton.click();
            }
          });
          
          await new Promise(resolve => setTimeout(resolve, 5000));
          
          // Kiểm tra thông báo
          const notifications = await page.evaluate(() => {
            const messages = Array.from(document.querySelectorAll('.ant-message-notice-content'));
            return messages.map(msg => msg.textContent);
          });
          
          console.log('📢 Notifications:', notifications);
        } else {
          console.log('⚠️ Save button vẫn disabled sau khi chọn role');
        }
      } else {
        console.log('❌ Không tìm thấy role options');
      }
    } else {
      console.log('❌ Không tìm thấy dropdown input');
    }
    
    console.log('⏳ Giữ browser mở 30 giây để quan sát...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành debug');
  }
}

debugRolePermissions().catch(console.error);
