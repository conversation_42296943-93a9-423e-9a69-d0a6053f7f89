// Test user permissions
const puppeteer = require('puppeteer');

async function testUserPermissions() {
  console.log('🚀 Test user permissions...');
  
  const browser = await puppeteer.launch({ 
    headless: false, 
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Lắng nghe console logs
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('PermissionContext') || text.includes('permissions') || text.includes('Permission')) {
      console.log(`🔍 Frontend: ${text}`);
    }
  });
  
  try {
    // Đăng nhập
    console.log('🔐 Đăng nhập với admin...');
    await page.goto('http://localhost:5173/login', { waitUntil: 'networkidle2' });
    await page.type('input[placeholder="Tên đăng nhập"]', 'admin');
    await page.type('input[placeholder="Mật khẩu"]', 'admin123');
    await page.click('button[type="submit"]');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Navigate đến user-management
    console.log('🔄 Navigate đến user-management...');
    await page.goto('http://localhost:5173/user-management', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Kiểm tra permissions được load
    const permissionsInfo = await page.evaluate(() => {
      // Kiểm tra localStorage
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      
      // Kiểm tra context permissions (nếu có)
      const permissionContext = window.permissionContext || {};
      
      return {
        userId: user.id,
        username: user.username,
        permissionContextExists: !!window.permissionContext,
        localStorageUser: user
      };
    });
    
    console.log('📊 User info:', permissionsInfo);
    
    // Test API call để lấy permissions
    console.log('🔍 Testing permissions API...');
    const response = await page.evaluate(async (userId) => {
      try {
        const response = await fetch(`http://localhost:8000/api/user-management/users/${userId}/permissions`);
        const permissions = await response.json();
        console.log('User permissions from API:', permissions);
        return permissions;
      } catch (error) {
        console.error('Error fetching permissions:', error);
        return null;
      }
    }, permissionsInfo.userId);
    
    console.log('✅ User permissions:', response);
    
    if (response && response.length > 0) {
      console.log('📋 Permission codes:');
      response.forEach(perm => {
        console.log(`  - ${perm.code} (${perm.type}): ${perm.name}`);
      });
    } else {
      console.log('❌ No permissions found for user');
    }
    
    console.log('');
    console.log('📝 NEXT STEPS:');
    console.log('   1. Kiểm tra xem user có permissions không');
    console.log('   2. Nếu không có, cần gán role cho user');
    console.log('   3. Nếu có, cần implement permission filtering trong menu');
    console.log('');
    console.log('⏳ Browser sẽ mở trong 2 phút để kiểm tra...');
    
    await new Promise(resolve => setTimeout(resolve, 120000));
    
  } catch (error) {
    console.error('❌ Lỗi:', error);
  } finally {
    await browser.close();
    console.log('🏁 Hoàn thành test');
  }
}

testUserPermissions().catch(console.error);
